package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.button.dto.UpdatesPojo;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeServiceImpl;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

/**
 * UpdateFieldAction单元测试 重点测试多语言数据处理的变更 GenerateByAI
 */
public class UpdateFieldActionTest {

    private UpdateFieldAction updateFieldAction;
    private IObjectDescribe describe;
    private IFieldDescribe fieldDescribe;
    private ObjectDescribeExt objectDescribeExt;
    private FieldDescribeExt fieldDescribeExt;
    private IObjectData objectData;
    private IUdefButton button;
    private IUdefAction action;
    private User user;
    private ButtonExecutor.Arg buttonArg;

    private Map<String, Object> updateFieldMap;
    private Map<String, Object> args;
    private Map<String, IFieldDescribe> fieldDescribeMap;

    @Before
    public void setUp() {
        updateFieldAction = new UpdateFieldAction();

        // 创建Mock对象
        describe = Mockito.mock(IObjectDescribe.class);
        fieldDescribe = Mockito.mock(IFieldDescribe.class);
        objectDescribeExt = Mockito.mock(ObjectDescribeExt.class);
        fieldDescribeExt = Mockito.mock(FieldDescribeExt.class);
        objectData = new ObjectData();
        button = Mockito.mock(IUdefButton.class);
        action = Mockito.mock(IUdefAction.class);
        user = Mockito.mock(User.class);
        buttonArg = Mockito.mock(ButtonExecutor.Arg.class);

        // 注入Mock服务
        ReflectionTestUtils.setField(updateFieldAction, "metaDataService", Mockito.mock(MetaDataService.class));
        ReflectionTestUtils.setField(updateFieldAction, "parseVarService", Mockito.mock(ParseVarService.class));
        ReflectionTestUtils.setField(updateFieldAction, "layoutLogicService", Mockito.mock(LayoutLogicServiceImpl.class));
        ReflectionTestUtils.setField(updateFieldAction, "functionPrivilegeService", Mockito.mock(FunctionPrivilegeServiceImpl.class));
        ReflectionTestUtils.setField(updateFieldAction, "logService", Mockito.mock(LogService.class));
        ReflectionTestUtils.setField(updateFieldAction, "expressionCalculateLogicService", Mockito.mock(ExpressionCalculateLogicService.class));
        ReflectionTestUtils.setField(updateFieldAction, "userRoleInfoService", Mockito.mock(UserRoleInfoService.class));
        ReflectionTestUtils.setField(updateFieldAction, "orgService", Mockito.mock(OrgService.class));

        // 准备测试数据
        updateFieldMap = new HashMap<>();
        updateFieldMap.put("field1", "value1");
        updateFieldMap.put("field2", "value2");

        args = new HashMap<>();
        args.put("form_field1_ml", "multiLangValue1");
        args.put("form_field2_ml", "multiLangValue2");
        args.put("form_nonMultiLang", "normalValue");

        fieldDescribeMap = new HashMap<>();
        fieldDescribeMap.put("field1", fieldDescribe);
        fieldDescribeMap.put("field2", fieldDescribe);
    }

    /**
     * GenerateByAI 测试内容描述：测试getFormFieldApiName方法的基本功能
     */
    @Test
    public void testGetFormFieldApiName_NormalCase() {
        // Given
        String fieldApiName = "test_field";
        String expected = "form_test_field";

        // When
        String result = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", fieldApiName);

        // Then
        Assert.assertEquals("字段API名称应正确添加form_前缀", expected, result);
    }

    /**
     * GenerateByAI 测试内容描述：测试getFormFieldApiName方法处理空字符串的情况
     */
    @Test
    public void testGetFormFieldApiName_EmptyString() {
        // Given
        String fieldApiName = "";
        String expected = "form_";

        // When
        String result = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", fieldApiName);

        // Then
        Assert.assertEquals("空字符串应正确添加form_前缀", expected, result);
    }

    /**
     * GenerateByAI 测试内容描述：测试getFormFieldApiName方法处理null值的情况
     */
    @Test
    public void testGetFormFieldApiName_NullValue() {
        // Given
        String fieldApiName = null;
        String expected = "form_null";

        // When
        String result = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", fieldApiName);

        // Then
        Assert.assertEquals("null值应被正确处理", expected, result);
    }

    /**
     * GenerateByAI 测试内容描述：测试getMultiLangDataMap方法当updateFieldMap为空时的处理
     */
    @Test
    public void testGetMultiLangDataMap_EmptyUpdateFieldMap() {
        // Given
        Map<String, Object> emptyMap = new HashMap<>();

        // When
        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) ReflectionTestUtils.invokeMethod(
                updateFieldAction, "getMultiLangDataMap", emptyMap, describe, args);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("空的updateFieldMap应返回空结果", result.isEmpty());
    }

    /**
     * GenerateByAI 测试内容描述：测试getMultiLangDataMap方法当objectDescribe为null时的处理
     */
    @Test
    public void testGetMultiLangDataMap_NullObjectDescribe() {
        // When
        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) ReflectionTestUtils.invokeMethod(
                updateFieldAction, "getMultiLangDataMap", updateFieldMap, null, args);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("null的objectDescribe应返回空结果", result.isEmpty());
    }

    /**
     * GenerateByAI 测试内容描述：测试getMultiLangDataMap方法当args为空时的处理
     */
    @Test
    public void testGetMultiLangDataMap_EmptyArgs() {
        // Given
        Map<String, Object> emptyArgs = new HashMap<>();

        // When
        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) ReflectionTestUtils.invokeMethod(
                updateFieldAction, "getMultiLangDataMap", updateFieldMap, describe, emptyArgs);

        // Then
        Assert.assertNotNull("结果不应为null", result);
        Assert.assertTrue("空的args应返回空结果", result.isEmpty());
    }

    /**
     * GenerateByAI 测试内容描述：测试getUpdateFieldMap方法整合多语言数据的功能
     */
    @Test
    public void testGetUpdateFieldMap_WithMultiLangDataIntegration() {
        // Given
        String actionParameter = "{\n"
                + "    \"fields\": [\n"
                + "        {\n"
                + "            \"field\": \"field1\",\n"
                + "            \"value\": \"value1\",\n"
                + "            \"var_type\": \"constant\",\n"
                + "            \"return_type\": \"text\",\n"
                + "            \"default_to_zero\": false,\n"
                + "            \"decimal_places\": 0\n"
                + "        }\n"
                + "    ]\n"
                + "}";

        // 配置Mock行为
        Mockito.when(action.getActionParamter()).thenReturn(actionParameter);
        Mockito.when(buttonArg.isSkipUpdateFieldActionValidation()).thenReturn(true); // 跳过验证避免复杂的Mock设置
        Mockito.when(buttonArg.getArgs()).thenReturn(args);

        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = Mockito.mockStatic(ObjectDescribeExt.class);
             MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = Mockito.mockStatic(FieldDescribeExt.class)) {

            // 配置ObjectDescribeExt Mock
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(describe)).thenReturn(objectDescribeExt);
            Mockito.when(objectDescribeExt.getDisableFieldDescribes()).thenReturn(Collections.emptyList());
            Mockito.when(objectDescribeExt.getActiveReferenceFieldDescribes()).thenReturn(Collections.emptyList());
            Mockito.when(describe.containsField("field1")).thenReturn(true);
            Mockito.when(describe.getFieldDescribe("field1")).thenReturn(fieldDescribe);
            Mockito.when(objectDescribeExt.getFieldDescribeMap()).thenReturn(fieldDescribeMap);

            // 配置FieldDescribeExt Mock
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(fieldDescribe)).thenReturn(fieldDescribeExt);
            Mockito.when(fieldDescribeExt.isEnableDataMultiLang()).thenReturn(true);
            Mockito.when(fieldDescribeExt.isShowMask()).thenReturn(false);
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.getMultiLangExtraFieldName("field1")).thenReturn("field1_ml");

            // When
            Map<String, Object> result = updateFieldAction.getUpdateFieldMap(
                    describe, objectData, button, action, buttonArg, user);

            // Then
            Assert.assertNotNull("结果不应为null", result);
            Assert.assertEquals("字段值应正确保存", "value1", result.get("field1"));
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试多语言数据处理的边界情况 - 字段不支持多语言
     */
    @Test
    public void testGetMultiLangDataMap_FieldNotSupportMultiLang() {
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = Mockito.mockStatic(ObjectDescribeExt.class);
             MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = Mockito.mockStatic(FieldDescribeExt.class)) {

            // Given - 配置字段不支持多语言
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(describe)).thenReturn(objectDescribeExt);
            Mockito.when(objectDescribeExt.getFieldDescribeMap()).thenReturn(fieldDescribeMap);

            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(fieldDescribe)).thenReturn(fieldDescribeExt);
            Mockito.when(fieldDescribeExt.isEnableDataMultiLang()).thenReturn(false);

            // When
            @SuppressWarnings("unchecked")
            Map<String, Object> result = (Map<String, Object>) ReflectionTestUtils.invokeMethod(
                    updateFieldAction, "getMultiLangDataMap", updateFieldMap, describe, args);

            // Then
            Assert.assertNotNull("结果不应为null", result);
            Assert.assertTrue("不支持多语言的字段应返回空结果", result.isEmpty());
        }
    }

    /**
     * GenerateByAI 测试内容描述：验证getFormFieldApiName方法中form_前缀添加的正确性
     */
    @Test
    public void testGetFormFieldApiName_PrefixLogic() {
        // Given - 多种不同的字段名
        String[] testFieldNames = {
            "simple_field",
            "field_with_underscore",
            "fieldWithCamelCase",
            "123field",
            "field123",
            "field-with-dash"
        };

        // When & Then
        for (String fieldName : testFieldNames) {
            String result = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", fieldName);
            String expected = "form_" + fieldName;
            Assert.assertEquals(
                    String.format("字段名 '%s' 应正确添加form_前缀", fieldName),
                    expected,
                    result
            );
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试invoke方法的正常执行流程
     */
    @Test
    public void testInvoke_NormalFlow() {
        // Given
        ButtonExecutor.Arg arg = Mockito.mock(ButtonExecutor.Arg.class);
        ActionExecutorContext context = Mockito.mock(ActionExecutorContext.class);
        Map<String, List<IObjectData>> details = new HashMap<>();

        // 配置Mock行为
        Mockito.when(arg.toDetails()).thenReturn(details);
        Mockito.when(arg.getObjectData()).thenReturn(objectData);
        Mockito.when(context.getUser()).thenReturn(user);
        Mockito.when(context.getAction()).thenReturn(action);
        Mockito.when(context.getButton()).thenReturn(button);
        Mockito.when(context.getDescribe()).thenReturn(describe);

        // 配置其他必要的Mock
        Mockito.when(action.getActionParamter()).thenReturn("{\"fields\":[]}");
        Mockito.when(arg.isSkipUpdateFieldActionValidation()).thenReturn(true);
        Mockito.when(arg.getArgs()).thenReturn(new HashMap<>());

        // 配置ObjectDescribeExt静态方法Mock
        try (MockedStatic<ObjectDescribeExt> mockedStatic = Mockito.mockStatic(ObjectDescribeExt.class)) {
            mockedStatic.when(() -> ObjectDescribeExt.of(describe)).thenReturn(objectDescribeExt);
            Mockito.when(objectDescribeExt.getDisableFieldDescribes()).thenReturn(Collections.emptyList());

            // When
            ButtonExecutor.Result result = updateFieldAction.invoke(arg, context);

            // Then
            // invoke方法返回null是正常的，因为UpdateFieldAction不需要返回值
            Assert.assertNull("UpdateFieldAction的invoke方法应返回null", result);
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试getType方法返回正确的ActionExecutorType
     */
    @Test
    public void testGetType() {
        // When
        ActionExecutorType result = updateFieldAction.getType();

        // Then
        Assert.assertEquals("应返回UPDATES类型", ActionExecutorType.UPDATES, result);
    }

    /**
     * GenerateByAI 测试内容描述：测试configValue方法处理列表类型字段的转换
     */
    @Test
    public void testConfigValue_ListValueField() {
        // Given
        Map<String, Object> map = new HashMap<>();
        UpdatesPojo.Field field = new UpdatesPojo.Field();
        field.setField("test_field");
        field.setValue("single_value");
        field.setReturn_type(IFieldType.EMPLOYEE); // 员工字段是列表类型

        // When
        ReflectionTestUtils.invokeMethod(updateFieldAction, "configValue", map, field);

        // Then
        Assert.assertTrue("员工字段的值应被转换为列表", map.get("test_field") instanceof List);
        @SuppressWarnings("unchecked")
        List<String> resultList = (List<String>) map.get("test_field");
        Assert.assertEquals("列表应包含原始值", "single_value", resultList.get(0));
    }

    /**
     * GenerateByAI 测试内容描述：测试configValue方法处理非列表类型字段
     */
    @Test
    public void testConfigValue_NonListValueField() {
        // Given
        Map<String, Object> map = new HashMap<>();
        UpdatesPojo.Field field = new UpdatesPojo.Field();
        field.setField("text_field");
        field.setValue("text_value");
        field.setReturn_type(IFieldType.TEXT); // 文本字段不是列表类型

        // When
        ReflectionTestUtils.invokeMethod(updateFieldAction, "configValue", map, field);

        // Then
        Assert.assertEquals("文本字段的值应保持原样", "text_value", map.get("text_field"));
    }

    /**
     * GenerateByAI 测试内容描述：测试configValue方法处理null值
     */
    @Test
    public void testConfigValue_NullValue() {
        // Given
        Map<String, Object> map = new HashMap<>();
        UpdatesPojo.Field field = new UpdatesPojo.Field();
        field.setField("null_field");
        field.setValue(null);
        field.setReturn_type(IFieldType.EMPLOYEE);

        // When
        ReflectionTestUtils.invokeMethod(updateFieldAction, "configValue", map, field);

        // Then
        Assert.assertNull("null值应保持为null", map.get("null_field"));
    }

    /**
     * GenerateByAI 测试内容描述：测试configValue方法处理已经是列表的值
     */
    @Test
    public void testConfigValue_AlreadyListValue() {
        // Given
        Map<String, Object> map = new HashMap<>();
        UpdatesPojo.Field field = new UpdatesPojo.Field();
        field.setField("list_field");
        List<String> originalList = Arrays.asList("value1", "value2");
        field.setValue(originalList);
        field.setReturn_type(IFieldType.EMPLOYEE);

        // When
        ReflectionTestUtils.invokeMethod(updateFieldAction, "configValue", map, field);

        // Then
        Assert.assertSame("已经是列表的值应保持不变", originalList, map.get("list_field"));
    }

    /**
     * GenerateByAI 测试内容描述：测试getActionParameterField方法解析动作参数
     */
    @Test
    public void testGetActionParameterField_NormalCase() {
        // Given
        String actionParameter = "{\n"
                + "    \"fields\": [\n"
                + "        {\n"
                + "            \"field\": \"field1\",\n"
                + "            \"value\": \"value1\",\n"
                + "            \"var_type\": \"constant\",\n"
                + "            \"return_type\": \"text\"\n"
                + "        },\n"
                + "        {\n"
                + "            \"field\": \"field2\",\n"
                + "            \"value\": \"value2\",\n"
                + "            \"var_type\": \"variable\",\n"
                + "            \"return_type\": \"number\"\n"
                + "        }\n"
                + "    ]\n"
                + "}";

        // 配置Mock行为
        Mockito.when(action.getActionParamter()).thenReturn(actionParameter);
        Mockito.when(describe.containsField("field1")).thenReturn(true);
        Mockito.when(describe.containsField("field2")).thenReturn(true);

        try (MockedStatic<ObjectDescribeExt> mockedStatic = Mockito.mockStatic(ObjectDescribeExt.class)) {
            mockedStatic.when(() -> ObjectDescribeExt.of(describe)).thenReturn(objectDescribeExt);
            Mockito.when(objectDescribeExt.getDisableFieldDescribes()).thenReturn(Collections.emptyList());

            // When
            @SuppressWarnings("unchecked")
            List<UpdatesPojo.Field> result = (List<UpdatesPojo.Field>) ReflectionTestUtils.invokeMethod(
                    updateFieldAction, "getActionParameterField", action, describe);

            // Then
            Assert.assertNotNull("结果不应为null", result);
            Assert.assertEquals("应返回2个字段", 2, result.size());
            Assert.assertEquals("第一个字段名应正确", "field1", result.get(0).getField());
            Assert.assertEquals("第二个字段名应正确", "field2", result.get(1).getField());
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试getActionParameterField方法过滤禁用字段
     */
    @Test
    public void testGetActionParameterField_FilterDisabledFields() {
        // Given
        String actionParameter = "{\n"
                + "    \"fields\": [\n"
                + "        {\n"
                + "            \"field\": \"enabled_field\",\n"
                + "            \"value\": \"value1\"\n"
                + "        },\n"
                + "        {\n"
                + "            \"field\": \"disabled_field\",\n"
                + "            \"value\": \"value2\"\n"
                + "        }\n"
                + "    ]\n"
                + "}";

        IFieldDescribe disabledField = Mockito.mock(IFieldDescribe.class);
        Mockito.when(disabledField.getApiName()).thenReturn("disabled_field");

        // 配置Mock行为
        Mockito.when(action.getActionParamter()).thenReturn(actionParameter);
        Mockito.when(describe.containsField("enabled_field")).thenReturn(true);
        Mockito.when(describe.containsField("disabled_field")).thenReturn(true);

        try (MockedStatic<ObjectDescribeExt> mockedStatic = Mockito.mockStatic(ObjectDescribeExt.class)) {
            mockedStatic.when(() -> ObjectDescribeExt.of(describe)).thenReturn(objectDescribeExt);
            Mockito.when(objectDescribeExt.getDisableFieldDescribes()).thenReturn(Arrays.asList(disabledField));

            // When
            @SuppressWarnings("unchecked")
            List<UpdatesPojo.Field> result = (List<UpdatesPojo.Field>) ReflectionTestUtils.invokeMethod(
                    updateFieldAction, "getActionParameterField", action, describe);

            // Then
            Assert.assertNotNull("结果不应为null", result);
            Assert.assertEquals("应只返回1个启用的字段", 1, result.size());
            Assert.assertEquals("应返回启用的字段", "enabled_field", result.get(0).getField());
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试ignoreField方法对外部用户的处理
     */
    @Test
    public void testIgnoreField_OutUserWithVariableType() {
        // Given
        User outUser = Mockito.mock(User.class);
        Mockito.when(outUser.isOutUser()).thenReturn(true);

        UpdatesPojo.Field field = new UpdatesPojo.Field();
        field.setField("employee_field");
        field.setVar_type("variable");

        IFieldDescribe employeeField = Mockito.mock(IFieldDescribe.class);
        FieldDescribeExt fieldExt = Mockito.mock(FieldDescribeExt.class);

        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = Mockito.mockStatic(ObjectDescribeExt.class);
             MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = Mockito.mockStatic(FieldDescribeExt.class)) {

            // 配置Mock行为
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(describe)).thenReturn(objectDescribeExt);
            Mockito.when(objectDescribeExt.getFieldDescribeSilently("employee_field"))
                    .thenReturn(Optional.of(employeeField));
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(employeeField)).thenReturn(fieldExt);
            Mockito.when(fieldExt.isEmployee()).thenReturn(true);

            // When
            Boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
                    updateFieldAction, "ignoreField", outUser, describe, field);

            // Then
            Assert.assertTrue("外部用户的员工字段变量类型应被忽略", result);
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试ignoreField方法对内部用户的处理
     */
    @Test
    public void testIgnoreField_InternalUser() {
        // Given
        User internalUser = Mockito.mock(User.class);
        Mockito.when(internalUser.isOutUser()).thenReturn(false);

        UpdatesPojo.Field field = new UpdatesPojo.Field();
        field.setField("employee_field");
        field.setVar_type("variable");

        // When
        Boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
                updateFieldAction, "ignoreField", internalUser, describe, field);

        // Then
        Assert.assertFalse("内部用户的字段不应被忽略", result);
    }

    /**
     * GenerateByAI 测试内容描述：测试ignoreField方法对常量类型的处理
     */
    @Test
    public void testIgnoreField_ConstantType() {
        // Given
        User outUser = Mockito.mock(User.class);
        Mockito.when(outUser.isOutUser()).thenReturn(true);

        UpdatesPojo.Field field = new UpdatesPojo.Field();
        field.setField("employee_field");
        field.setVar_type("constant"); // 常量类型

        try (MockedStatic<ObjectDescribeExt> mockedStatic = Mockito.mockStatic(ObjectDescribeExt.class)) {
            mockedStatic.when(() -> ObjectDescribeExt.of(describe)).thenReturn(objectDescribeExt);
            Mockito.when(objectDescribeExt.getFieldDescribeSilently("employee_field"))
                    .thenReturn(Optional.empty()); // 常量类型不需要字段描述

            // When
            Boolean result = (Boolean) ReflectionTestUtils.invokeMethod(
                    updateFieldAction, "ignoreField", outUser, describe, field);

            // Then
            Assert.assertFalse("外部用户的常量类型字段不应被忽略", result);
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试validateLookup方法的正常执行
     */
    @Test
    public void testValidateLookup_NormalCase() {
        // Given
        Map<String, Object> objectDataMap = new HashMap<>();
        objectDataMap.put("lookup_field", "lookup_value");

        ObjectReferenceWrapper referenceField = Mockito.mock(ObjectReferenceWrapper.class);
        Mockito.when(referenceField.getApiName()).thenReturn("lookup_field");

        MetaDataService metaDataService = Mockito.mock(MetaDataService.class);
        ReflectionTestUtils.setField(updateFieldAction, "metaDataService", metaDataService);

        try (MockedStatic<ObjectDescribeExt> mockedStatic = Mockito.mockStatic(ObjectDescribeExt.class)) {
            // 配置Mock行为
            mockedStatic.when(() -> ObjectDescribeExt.of(describe)).thenReturn(objectDescribeExt);
            Mockito.when(objectDescribeExt.getActiveReferenceFieldDescribes())
                    .thenReturn(Arrays.asList(referenceField));

            // When
            ReflectionTestUtils.invokeMethod(updateFieldAction, "validateLookup", objectDataMap, describe, user);

            // Then
            // 验证metaDataService.validateLookupDataIgnorePolygonal被调用
            Mockito.verify(metaDataService).validateLookupDataIgnorePolygonal(
                    Mockito.eq(user), Mockito.any(IObjectData.class), Mockito.eq(referenceField));
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试validateLookup方法处理空的引用字段列表
     */
    @Test
    public void testValidateLookup_EmptyReferenceFields() {
        // Given
        Map<String, Object> objectDataMap = new HashMap<>();
        objectDataMap.put("normal_field", "normal_value");

        MetaDataService metaDataService = Mockito.mock(MetaDataService.class);
        ReflectionTestUtils.setField(updateFieldAction, "metaDataService", metaDataService);

        try (MockedStatic<ObjectDescribeExt> mockedStatic = Mockito.mockStatic(ObjectDescribeExt.class)) {
            // 配置Mock行为
            mockedStatic.when(() -> ObjectDescribeExt.of(describe)).thenReturn(objectDescribeExt);
            Mockito.when(objectDescribeExt.getActiveReferenceFieldDescribes())
                    .thenReturn(Collections.emptyList());

            // When
            ReflectionTestUtils.invokeMethod(updateFieldAction, "validateLookup", objectDataMap, describe, user);

            // Then
            // 验证metaDataService.validateLookupDataIgnorePolygonal没有被调用
            Mockito.verify(metaDataService, Mockito.never())
                    .validateLookupDataIgnorePolygonal(Mockito.any(User.class), Mockito.any(IObjectData.class), Mockito.any(ObjectReferenceWrapper.class));
        }
    }
}
