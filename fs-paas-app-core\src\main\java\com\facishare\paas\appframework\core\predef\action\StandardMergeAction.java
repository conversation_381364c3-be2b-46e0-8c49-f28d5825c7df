package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.dto.InternationalItem;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18N<PERSON>ey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.util.ProductUtil;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectReferenceField;
import com.facishare.paas.metadata.api.describe.IObjectReferenceMany;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.service.impl.ObjectDescribeServiceImpl;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.elasticsearch.common.util.set.Sets;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.common.util.ObjectAction.UPDATE;

@Slf4j
public class StandardMergeAction extends BaseObjectApprovalAction<StandardMergeAction.Arg, StandardMergeAction.Result> {
    protected List<IObjectDescribe> masterDetailDescribes = Lists.newArrayList();
    protected IObjectData targetObjectData;
    protected List<IObjectData> sourceObjectDataList = Lists.newArrayList();
    protected MetaDataActionService metaDataActionService =
            SpringUtil.getContext().getBean("metaDataActionService", MetaDataActionService.class);
    protected IObjectDescribeService objectDescribeService = SpringUtil.getContext().getBean(ObjectDescribeServiceImpl.class);
    protected IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);

    public static List<String> blockObject = Lists.newArrayList("VisitingObj", "CustomerAccountObj");
    protected final FunctionPrivilegeService functionPrivilegeService = SpringUtil.getContext().getBean("functionPrivilegeService", FunctionPrivilegeService.class);
    private final CRMNotificationServiceImpl crmNotificationService = (CRMNotificationServiceImpl) SpringUtil.getContext().getBean("crmNotificationService");

    // {0} 被合并通知
    private static final String MERGE_MSG_TITLE = "paas.merge.source.data.send.notification.title";
    // 您的 {0} [ {1} ]，被合并到 {2} [ {3} ]，操作人[ {4} ]
    private static final String MERGE_MSG_FULL_CONTENT = "paas.merge.source.data.send.notification.content";

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-sales-config",
                config -> {
                    String blockObjectTemp = config.get("block_object");
                    if (!StringUtils.isBlank(blockObjectTemp)) {
                        blockObject = Lists.newArrayList(blockObjectTemp.split(","));
                    }
                });
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.Merge.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        List<String> ids = Lists.newArrayList();
        ids.add(arg.getTargetDataId());
        ids.addAll(arg.getSourceDataIds());
        return ids;
    }

    @Override
    protected boolean needInvalidData() {
        return true;
    }

    protected List<String> getMergeRelationObjectApiNames() {
        if (CollectionUtils.notEmpty(masterDetailDescribes)) {
            return masterDetailDescribes.stream().map(x -> x.getApiName())
                    .distinct().collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    protected void prepareUpdateData() {
        ObjectDataDocument objectDataDocument = arg.getObjectData();
        if (objectDataDocument == null) {
            objectDataDocument = new ObjectDataDocument();
        }
        objectDataDocument.put("last_modified_time", System.currentTimeMillis());
        objectDataDocument.put("last_modified_by", Lists.newArrayList(actionContext.getUser().getUserId()));
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        //SourceDataIds包含getTargetDataId,提醒
        if (arg.getSourceDataIds().contains(arg.getTargetDataId())) {
            throw new ValidateException(I18N.text("sfa.merge.SourceData.contains.TargetData"));
        }
        //TODO 校验审批流
        try {
            masterDetailDescribes = objectDescribeService.findReferenceList(actionContext.getTenantId(), actionContext.getObjectApiName(), DefObjConstants.PACKAGE_NAME_CRM);
            masterDetailDescribes.removeIf(a -> ObjectDescribeExt.BLACK_LIST.contains(a.getApiName()));
            List<String> unusableProductApiNames = ProductUtil.findUnusableProductApiNames(actionContext.getTenantId());
            if (CollectionUtils.notEmpty(unusableProductApiNames)) {
                masterDetailDescribes.removeIf(a -> unusableProductApiNames.contains(a.getApiName()));
            }
        } catch (Exception e) {
            throw new ValidateException(e.toString());
        }
        checkDataPrivilege();
    }

    protected void checkDataPrivilege() {
        Map<String, Map<String, Boolean>> funPrivilegeCheckResult = functionPrivilegeService.batchFunPrivilegeCheck(actionContext.getUser(),
                Lists.newArrayList(actionContext.getObjectApiName()),
                Lists.newArrayList(UPDATE.getActionCode()));
        if (funPrivilegeCheckResult == null || funPrivilegeCheckResult.isEmpty()) {
            throw new ValidateException(I18N.text("sfa.merge.noprivilegeerror"));
        }
        Map<String, Boolean> updateFunPrivilege = funPrivilegeCheckResult.get(actionContext.getObjectApiName());
        if (updateFunPrivilege == null || updateFunPrivilege.isEmpty()
                || !updateFunPrivilege.containsKey(UPDATE.getActionCode())
                || !Boolean.TRUE.equals(updateFunPrivilege.get(UPDATE.getActionCode()))) {
            throw new ValidateException(I18N.text("sfa.merge.noprivilegeerror"));
        }

        if (CollectionUtils.notEmpty(dataList)) {
            Map<String, Permissions> permissionsMap = serviceFacade.checkDataPrivilege(actionContext.getUser(),
                    dataList, objectDescribe, ObjectAction.UPDATE.getActionCode());
            if (CollectionUtils.empty(permissionsMap)) {
                throw new ValidateException(I18N.text("sfa.merge.noprivilegeerror"));
            }
            List<IObjectData> noPrivilegeDataList = Lists.newArrayList();
            for (IObjectData item : dataList) {
                if (!permissionsMap.containsKey(item.getId())
                        || !(permissionsMap.get(item.getId()).equals(Permissions.READ_WRITE))) {
                    noPrivilegeDataList.add(item);
                }
            }
            if (CollectionUtils.notEmpty(noPrivilegeDataList)) {
                throw new ValidateException(I18N.text("sfa.merge.noprivilegeerror"));
            }
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        Result result = new Result();
        try {
            prepareUpdateData();
            ObjectDataDocument objectDataDocument = arg.getObjectData();
            if (objectDataDocument != null) {
                //db数据
                IObjectData cpDbData = ObjectDataExt.of(targetObjectData).copy();
                ObjectDataExt.of(cpDbData).removeFieldsNotSupportEdit(objectDescribe);
                for (Map.Entry<String, Object> entry : objectDataDocument.entrySet()) {
                    targetObjectData.set(entry.getKey(), entry.getValue());
                }
                //合并后的数据
                IObjectData cpMergeData = ObjectDataExt.of(targetObjectData).copy();
                ObjectDataExt.of(cpMergeData).removeFieldsNotSupportEdit(objectDescribe);
                Map<String, Object> diffMap = ObjectDataExt.of(cpDbData).diff(cpMergeData, this.objectDescribe);
                log.info("合并 {} 修改信息：{}--{}", objectDescribe.getApiName(), targetObjectData, objectDataDocument.keySet());
                if(ObjectUtils.isEmpty(diffMap)){
                    log.info("StandardMergeAction diffMap is null");
                }else{
                    log.info("StandardMergeAction.diffMap：{}", JSONObject.toJSONString(diffMap.keySet()));
                    //给计算字段变更监控上报标识
                    ActionContext context = getDefaultActionContext(actionContext.getUser());
                    serviceFacade.batchUpdateByFields(ActionContextExt.of(context).setActionType("mergeAction").getContext(), Lists.newArrayList(targetObjectData), Lists.newArrayList(diffMap.keySet()));
                    String buttonApiName = I18N.text(I18NKey.OBJECT_BY_COMBINING,
                            I18N.text(actionContext.getObjectApiName() + ".attribute.self.display_name"));
                    com.facishare.paas.appframework.log.dto.InternationalItem peerDisplayName = new com.facishare.paas.appframework.log.dto.InternationalItem();
                    peerDisplayName.setDefaultInternationalValue(buttonApiName);
                    peerDisplayName.setInternationalKey(I18NKey.OBJECT_BY_COMBINING);
                    peerDisplayName.setInternationalParameters(Lists.newArrayList(actionContext.getObjectApiName() + ".attribute.self.display_name"));
                    serviceFacade.log(actionContext.getUser(), EventType.MODIFY, ActionType.Modify, objectDescribe, targetObjectData, diffMap, cpDbData, buttonApiName, buttonApiName, peerDisplayName,null,Maps.newHashMap());

                }
            }
            if (arg.getNeedMergeRelationObjects()) {
                mergeRelationObject();
            }
            log.info("合并 {} 删除被合并数据:{}--{}", objectDescribe.getApiName(), sourceObjectDataList, actionContext.getUser());
            sourceObjectDataList.forEach(x -> x.set("life_status", ObjectLifeStatus.INVALID.getCode()));
            serviceFacade.batchUpdateByFields(getDefaultActionContext(actionContext.getUser()),sourceObjectDataList, Lists.newArrayList("life_status"));
            String deleteLog = serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(sourceObjectDataList, actionContext.getUser());
            log.info("合并 {} 删除被合并数据:{}", objectDescribe.getApiName(), deleteLog);
            result.setSuccessed(true);
            return result;
        } catch (Exception e) {
            log.error("合并对象{}异常", objectDescribe.getApiName(), e);
            throw new ValidateException(e.getMessage());
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        sendActionMq();
        sendCRMBySourceData();
        return result;
    }

    protected void mergeRelationObject() {
        log.info("合并相关团队");
        mergeTeamMember();
        log.info("合并关联自定义对象！！！！！");
        List<String> mergeObjectApiNames = getMergeRelationObjectApiNames();
        mergeObjectApiNames.removeIf(x -> blockObject.contains(x));
        if (CollectionUtils.notEmpty(mergeObjectApiNames)) {
            log.info("合并-自定义对象列表：{}", mergeObjectApiNames);
            String buttonApiName = I18N.text(I18NKey.OBJECT_BY_COMBINING,
                    I18N.text(actionContext.getObjectApiName() + ".attribute.self.display_name"));
            com.facishare.paas.appframework.log.dto.InternationalItem peerDisplayName = new com.facishare.paas.appframework.log.dto.InternationalItem();
            peerDisplayName.setDefaultInternationalValue(buttonApiName);
            peerDisplayName.setInternationalKey(I18NKey.OBJECT_BY_COMBINING);
            peerDisplayName.setInternationalParameters(Lists.newArrayList(actionContext.getObjectApiName() + ".attribute.self.display_name"));
            for (String objectApiName : mergeObjectApiNames) {
                Optional<IObjectDescribe> lookupDescribe = masterDetailDescribes.stream().filter(x -> x.getApiName().equals(objectApiName)).findFirst();
                if (!lookupDescribe.isPresent()) {
                    continue;
                }
                lookupDescribe.get().getFieldDescribes().forEach(x -> {
                    if (x instanceof IObjectReferenceField || x instanceof IObjectReferenceMany) {
                        ObjectReferenceWrapper referenceField = ObjectReferenceWrapper.of(x);
                        if (referenceField.getTargetApiName().equals(actionContext.getObjectApiName())) {
                            log.info("合并lookup_object-lookUpFieldApiName：{}--lookupDescribe：{}", x.getApiName(), lookupDescribe.get().getApiName());
                            mergeData(x.getApiName(), x.getType(), lookupDescribe.get(), buttonApiName,peerDisplayName);
                        }
                    } else if (x.get("type").equals("master_detail")) {
                        if (x.get("target_api_name").equals(actionContext.getObjectApiName())) {
                            log.info("合并master_detail-lookUpFieldApiName：{}--lookupDescribe：{}", x.getApiName(), lookupDescribe.get().getApiName());
                            mergeData(x.getApiName(), x.getType(), lookupDescribe.get(), buttonApiName,peerDisplayName);
                        }
                    }
                });
            }
        }
    }

    protected void mergeData(String lookUpFieldApiName, String lookUpFieldType, IObjectDescribe lookupDescribe, String buttonApiName, com.facishare.paas.appframework.log.dto.InternationalItem peerDisplayName) {
        int executeCount = 0;
        if ("contact_id".equals(lookUpFieldApiName) && Utils.OPPORTUNITY_API_NAME.equals(lookupDescribe.getApiName())) {
            return;
        }
        //用于标记发送过修改记录的数据id
        Map<String, String> flagSendUpdRecordIdMap = new HashMap<>();
        while (true) {
            try {
                executeCount++;
                SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
                searchTemplateQuery.setLimit(200);
                searchTemplateQuery.setPermissionType(0);
                searchTemplateQuery.setNeedReturnCountNum(false);
                List<IFilter> filters = Lists.newArrayList();
                filters.add(filter(lookUpFieldApiName, Operator.IN, arg.getSourceDataIds()));
                filters.add(filter(IObjectData.IS_DELETED, Operator.IN,
                        Lists.newArrayList(new String[]{String.valueOf(DELETE_STATUS.NORMAL.getValue()), String.valueOf(DELETE_STATUS.INVALID.getValue())})));
                searchTemplateQuery.setFilters(filters);
                List<IObjectData> mergeObjectDataList = serviceFacade.findBySearchQueryWithDeleted(actionContext.getUser(), lookupDescribe, searchTemplateQuery).getData();
                if (CollectionUtils.empty(mergeObjectDataList)) {
                    break;
                }
                IActionContext context = new ActionContext();
                context.setUserId(actionContext.getUser().getUserId());
                context.setEnterpriseId(actionContext.getTenantId());
                log.info("mergeObjectDataList:{},{}, arg:{}", lookUpFieldApiName, lookupDescribe.getApiName(), arg);

                List<IObjectData> oldDataList = ObjectDataExt.copyList(mergeObjectDataList);

                mergeObjectDataList.forEach(m -> {
                    if (IFieldType.OBJECT_REFERENCE_MANY.equals(lookUpFieldType)) {
                        List<String> oldValues = CollectionUtils.nullToEmpty((List<String>) m.get(lookUpFieldApiName));
                        List<String> newValues;
                        if (CollectionUtils.empty(oldValues)) {
                            newValues = Lists.newArrayList(arg.getTargetDataId());
                        } else {
                            newValues = Lists.newArrayList(oldValues);
                            newValues.removeAll(arg.getSourceDataIds());
                            if (!newValues.contains(arg.getTargetDataId())) {
                                newValues.add(arg.getTargetDataId());
                            }
                        }
                        m.set(lookUpFieldApiName, newValues);
                    } else {
                        m.set(lookUpFieldApiName, arg.getTargetDataId());
                    }
                    log.warn("StandardMergeAction mergeData ObjectApiName:{},fieldApiName:{},newValues:{},dataId:{}", lookupDescribe.getApiName(), lookUpFieldApiName, arg.getTargetDataId(), m.getId());
                });
                metaDataActionService.batchUpdateByFields(context, mergeObjectDataList, Lists.newArrayList(lookUpFieldApiName));

                //添加修改记录
                try {
                    mergeObjectDataList.forEach(m -> {
                        Map<String, Object> diffMap = Maps.newHashMap();
                        diffMap.put(lookUpFieldApiName, m.get(lookUpFieldApiName));
                        Optional<IObjectData> oldDataOpt = oldDataList.stream().filter(n -> n.getId().equals(m.getId())).findFirst();
                        if (oldDataOpt.isPresent()) {
                            String key = splitSaveUpdRecordKey(m);
                            if (!flagSendUpdRecordIdMap.containsKey(key)) {
                                serviceFacade.log(actionContext.getUser(), EventType.MODIFY, ActionType.Modify, lookupDescribe, m, diffMap, oldDataOpt.get(), buttonApiName, buttonApiName, peerDisplayName,null,Maps.newHashMap());
                                flagSendUpdRecordIdMap.put(key, m.getId());
                            }
                        }
                    });
                } catch (Exception e) {
                    log.warn("合并自定义对象recordLogs failed, user={} data={}", actionContext.getUser(), mergeObjectDataList, e);
                }
                if (executeCount >= 500) {
                    log.warn("合并自定义对象查询次数超过上限:{}, arg:{}", lookUpFieldApiName, arg);
                    break;
                }
            } catch (Exception e) {
                log.error("合并异常：{}：{}---{}", lookupDescribe.getApiName(), lookUpFieldApiName, e);
                throw e;
            }
        }
    }

    protected void mergeTeamMember() {
        //合并团队成员
        List<TeamMember> teamMembers = Lists.newArrayList();
        for (IObjectData objectData : sourceObjectDataList) {
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            teamMembers.addAll(objectDataExt.getTeamMembers());
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(targetObjectData);
        List<String> ownerList = objectDataExt.getOwner();
        teamMembers = teamMembers.stream()
                .filter(x -> StringUtils.isNotEmpty(x.getEmployee()) && !ownerList.contains(x.getEmployee()))
                .collect(Collectors.toList());

        if (CollectionUtils.notEmpty(teamMembers)) {
            List<TeamMember> tempList = Lists.newArrayList();
            for (TeamMember teamMember : teamMembers) {
                if (TeamMember.Role.OWNER.equals(teamMember.getRole())) {
                    tempList.add(new TeamMember(teamMember.getEmployee(), TeamMember.Role.NORMAL_STAFF, teamMember.getPermission(), teamMember.getOutTenantId(), teamMember.getMemberType(), teamMember.getTeamMemberDeptCascade()));
                } else {
                    tempList.add(new TeamMember(teamMember.getEmployee(), teamMember.getRoleCode(), teamMember.getPermission(), teamMember.getOutTenantId(), teamMember.getMemberType(), teamMember.getTeamMemberDeptCascade()));
                }
            }
            objectDataExt.addTeamMembers(tempList);
        }
        serviceFacade.batchUpdateRelevantTeam(actionContext.getUser(), Lists.newArrayList(targetObjectData), false);
    }

    private Filter filter(String name, Operator operator, Object value) {
        Filter filter = new Filter();
        filter.setFieldName(name);
        if (value instanceof List) {
            filter.setFieldValues((List<String>) value);
        } else {
            filter.setFieldValues(Arrays.asList(value.toString()));
        }
        filter.setOperator(operator);
        return filter;
    }

    private ActionContext getDefaultActionContext(User user) {
        IObjectDescribe objectDescribe = null;
        try {
            objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(user.getTenantId(), actionContext.getObjectApiName());
        } catch (Exception e) {

        }
        ActionContext actionContext = new ActionContext();
        actionContext.setEnterpriseId(user.getTenantId());
        actionContext.setUserId(user.getUserId());
        actionContext.setDbType("pg");
        actionContext.setAllowUpdateInvalid(true);
        actionContext.put("not_validate", true);
        actionContext.setPrivilegeCheck(false);
        actionContext.setObjectDescribe(objectDescribe);
        return actionContext;
    }

    protected void sendActionMq() {
        List<IObjectData> tempSourceDataList = ObjectDataExt.copyList(sourceObjectDataList);
        List<IObjectData> tempTargetDataList = ObjectDataExt.copyList(Lists.newArrayList(targetObjectData));
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            serviceFacade.sendActionMq(actionContext.getUser(), tempSourceDataList, ObjectAction.INVALID);
            serviceFacade.sendActionMq(actionContext.getUser(), tempSourceDataList, ObjectAction.DELETE);
            List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), Lists.newArrayList(arg.getTargetDataId()), actionContext.getObjectApiName());
            fillDataForActionMq(objectDataList);
            serviceFacade.sendActionMq(actionContext.getUser(), ObjectDataDocument.fillOldData(objectDataList, tempTargetDataList), ObjectAction.MERGE);
        });
        parallelTask.run();
    }

    protected void fillDataForActionMq(List<IObjectData> objectDataList) {
    }

    @Data
    public static class Arg {
        private String targetDataId;
        private List<String> sourceDataIds;
        private ObjectDataDocument objectData;
        private Boolean needMergeRelationObjects;
        private String jobId;
        private Map<String, Object> valueMap;
        private Boolean needSendCRMBySource;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private Boolean successed;
    }

    /**
     * 标志 关联的自定义对象发送过修改记录
     *
     * @param data
     * @return
     */
    private String splitSaveUpdRecordKey(IObjectData data) {
        if (ObjectUtils.isEmpty(data) || ObjectUtils.isEmpty(data.getTenantId()) || ObjectUtils.isEmpty(data.getId())) {
            return "";
        }
        String key = "tenantId=%s-id=%s";
        return String.format(key, data.getTenantId(), data.getId());
    }

    /**
     * 给源数据的相关团队发送crm通知
     */
    protected void sendCRMBySourceData() {
        // 判断是否发送消息
        if (!Boolean.TRUE.equals(arg.getNeedSendCRMBySource())) {
            return;
        }
        // 操作人
        String finalUserName = "";
        if (User.SUPPER_ADMIN_USER_ID.equals(actionContext.getUser().getUpstreamOwnerIdOrUserId())) {
            finalUserName = I18N.text(I18NKey.SYSTEM);
        } else {
            finalUserName = serviceFacade.getUser(actionContext.getUser().getTenantId(), actionContext.getUser().getUpstreamOwnerIdOrUserId()).getUserName();
        }
        // 多语key
        String apiNameDisplayNameKey = GetI18nKeyUtil.getDescribeDisplayNameKey(actionContext.getObjectApiName());
        String apiNameKeyParameter = String.format("#I18N#%s", apiNameDisplayNameKey);
        String objectApiName = I18N.text(apiNameDisplayNameKey);
        // 当消息的多语key解析失败时显示的title
        String title = String.format("%s被合并通知", I18N.text(apiNameDisplayNameKey)); // ignoreI18n
        // 给源相关团队发通知
        for (IObjectData sourceObject : sourceObjectDataList) {
            // 获取相关团队及成员
            List<Integer> receiverIds = Lists.newArrayList();
            ObjectDataExt objectDataExt = ObjectDataExt.of(sourceObject);
            if (ObjectUtils.isEmpty(objectDataExt.getTeamMembers())) {
                log.warn("sendCRMBySourceData teamMembers is null. sourceObject:{}", sourceObject);
                continue;
            }
            for (TeamMember teamMember : objectDataExt.getTeamMembers()) {
                if (!TeamMember.MemberType.EMPLOYEE.equals(teamMember.getMemberType())) {
                    continue;
                }
                receiverIds.add(Integer.parseInt(teamMember.getEmployee()));
            }
            if (ObjectUtils.isEmpty(receiverIds)) {
                log.warn("sendCRMBySourceData receiverIds is null. sourceObject:{}", sourceObject);
                continue;
            }
            // 当消息的多语key解析失败时消息内容
            String fullContent = String.format("您的%s[ %s ]，被合并到%s[ %s ]，操作人[ %s ]", objectApiName, sourceObject.getName(), objectApiName, targetObjectData.getName(), finalUserName); // ignoreI18n
            // 构建消息内容并发送
            InternationalItem titleInternationalItem = InternationalItem.builder()
                    .internationalKey(MERGE_MSG_TITLE)
                    .internationalParameters(Lists.newArrayList(apiNameKeyParameter))
                    .build();
            InternationalItem fullContentInternationalItem = InternationalItem.builder()
                    .internationalKey(MERGE_MSG_FULL_CONTENT)
                    .internationalParameters(Lists.newArrayList(apiNameKeyParameter, sourceObject.getName(), apiNameKeyParameter, targetObjectData.getName(), finalUserName))
                    .build();
            NewCrmNotification newCrmNotification = NewCrmNotification.builder()
                    .receiverIDs(Sets.newHashSet(receiverIds))
                    .sourceId(UUID.randomUUID().toString().replace("-", ""))
                    .type(13)
                    .remindSender(true)
                    .title(title)
                    .fullContent(fullContent)
                    .titleInfo(titleInternationalItem)
                    .fullContentInfo(fullContentInternationalItem)
                    .appId(actionContext.getAppId())
                    .build();
            ParallelUtils.createParallelTask()
                    .submit(() -> crmNotificationService.sendNewCrmNotification(actionContext.getUser(), newCrmNotification))
                    .run();
        }
    }

}
