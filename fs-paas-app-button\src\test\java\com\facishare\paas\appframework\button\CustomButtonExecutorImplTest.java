package com.facishare.paas.appframework.button;

import com.facishare.paas.appframework.button.action.*;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.RuleResult;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.functions.utils.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CustomButtonExecutorImpl单元测试 - JUnit5版本
 * 
 * GenerateByAI
 * 
 * 测试CustomButtonExecutorImpl类的核心业务方法：
 * - triggerValidationFunction: 触发验证函数
 * - triggerFunctionAction: 触发函数动作
 * - startCustomButton: 启动自定义按钮
 * - checkParam: 参数校验
 * - triggerValidateRule: 触发验证规则
 * 
 * 覆盖场景：
 * - 正常流程执行
 * - 异常处理和边界条件
 * - 不同按钮类型和动作类型
 * - 参数验证和权限检查
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("CustomButtonExecutorImpl - 自定义按钮执行器测试")
class CustomButtonExecutorImplTest {

    @Mock
    private MetaDataFindServiceImpl metaDataFindService;
    
    @Mock
    private CustomButtonServiceImpl buttonService;
    
    @Mock
    private PostActionServiceImpl actionService;
    
    @Mock
    private ActionExecutorManager actionExecutorManager;
    
    @Mock
    private DescribeLogicServiceImpl describeLogicService;
    
    @Mock
    private FunctionPrivilegeService functionPrivilegeService;
    
    @Mock
    private UserRoleInfoService userRoleInfoService;
    
    @Mock
    private OrgService orgService;
    
    @Mock
    private MaskFieldLogicService maskFieldLogicService;
    
    @Mock
    private UpdateFieldAction updateFieldAction;
    
    @Mock
    private ValidateRuleService validateRuleService;

    @InjectMocks
    private CustomButtonExecutorImpl customButtonExecutor;

    // 测试数据
    private User mockUser;
    private IUdefButton mockButton;
    private IObjectDescribe mockDescribe;
    private IObjectData mockObjectData;
    private ButtonExecutor.Arg mockArg;
    private List<IUdefAction> mockActionList;
    private ActionExecutor mockActionExecutor;

    // 测试常量
    private static final String TEST_TENANT_ID = "test-tenant-123";
    private static final String TEST_USER_ID = "test-user-456";
    private static final String TEST_BUTTON_API_NAME = "test_button";
    private static final String TEST_DESCRIBE_API_NAME = "TestObj";
    private static final String TEST_OBJECT_ID = "test-object-id-789";

    @BeforeEach
    void setUp() {
        // 创建Mock对象
        setupMockObjects();
        
        // 配置Mock行为
        setupMockBehaviors();
    }

    /**
     * 设置Mock对象
     */
    private void setupMockObjects() {
        // 创建Mock User
        mockUser = mock(User.class);
        when(mockUser.getTenantId()).thenReturn(TEST_TENANT_ID);
        when(mockUser.getUserId()).thenReturn(TEST_USER_ID);
        when(mockUser.isOutUser()).thenReturn(false);
        when(mockUser.isSupperAdmin()).thenReturn(false);

        // 创建Mock Button
        mockButton = mock(IUdefButton.class);
        when(mockButton.getApiName()).thenReturn(TEST_BUTTON_API_NAME);
        when(mockButton.getLabel()).thenReturn("测试按钮");
        when(mockButton.getParamForm()).thenReturn(Collections.emptyList());
        when(mockButton.getExtendInfo()).thenReturn("{}");

        // 创建Mock Describe
        mockDescribe = mock(IObjectDescribe.class);
        when(mockDescribe.getApiName()).thenReturn(TEST_DESCRIBE_API_NAME);
        when(mockDescribe.getDisplayName()).thenReturn("测试对象");
        when(mockDescribe.getTenantId()).thenReturn(TEST_TENANT_ID);

        // 创建Mock ObjectData
        mockObjectData = mock(IObjectData.class);
        when(mockObjectData.get("_id")).thenReturn(TEST_OBJECT_ID);

        // 创建Mock Arg
        mockArg = mock(ButtonExecutor.Arg.class);
        when(mockArg.getButtonApiName()).thenReturn(TEST_BUTTON_API_NAME);
        when(mockArg.getDescribeApiName()).thenReturn(TEST_DESCRIBE_API_NAME);
        when(mockArg.getObjectDataId()).thenReturn(TEST_OBJECT_ID);
        when(mockArg.getObjectData()).thenReturn(mockObjectData);
        when(mockArg.getArgs()).thenReturn(Maps.newHashMap());
        when(mockArg.getActionStage()).thenReturn(UdefActionExt.PRE);
        when(mockArg.isSkipValidationFunction()).thenReturn(false);
        when(mockArg.isSkippedValidateRule()).thenReturn(false);

        // 创建Mock Action List
        mockActionList = Arrays.asList(createMockAction("action1"), createMockAction("action2"));
        
        // 创建Mock ActionExecutor
        mockActionExecutor = mock(ActionExecutor.class);
    }

    /**
     * 配置Mock行为
     */
    private void setupMockBehaviors() {
        // 配置buttonService
        when(buttonService.findButtonByApiName(any(User.class), anyString(), anyString()))
                .thenReturn(mockButton);

        // 配置describeLogicService
        when(describeLogicService.findObject(anyString(), anyString()))
                .thenReturn(mockDescribe);

        // 配置metaDataFindService
        when(metaDataFindService.findObjectData(any(User.class), anyString(), anyString()))
                .thenReturn(mockObjectData);

        // 配置actionService
        when(actionService.findActionListByStage(any(User.class), any(IUdefButton.class), anyString(), anyString()))
                .thenReturn(mockActionList);
        when(actionService.findActionList(any(User.class), any(IUdefButton.class), anyString()))
                .thenReturn(mockActionList);

        // 配置mockButton.getActions()返回String列表
        when(mockButton.getActions()).thenReturn(Arrays.asList("action1", "action2"));

        // 配置actionExecutorManager
        when(actionExecutorManager.getActionExecutor(any(ActionExecutorType.class)))
                .thenReturn(mockActionExecutor);

        // 配置actionExecutor
        when(mockActionExecutor.invoke(any(ButtonExecutor.Arg.class), any(ActionExecutorContext.class)))
                .thenReturn(ButtonExecutor.Result.empty());

        // 配置functionPrivilegeService
        when(functionPrivilegeService.getReadonlyFields(any(User.class), anyString()))
                .thenReturn(Collections.emptySet());
    }

    /**
     * 创建Mock Action
     */
    private IUdefAction createMockAction(String actionId) {
        IUdefAction mockAction = mock(IUdefAction.class);
        when(mockAction.getId()).thenReturn(actionId);
        when(mockAction.getActionType()).thenReturn("custom_function");
        when(mockAction.getStage()).thenReturn(UdefActionExt.PRE);
        when(mockAction.getActionParamter()).thenReturn("{}");
        return mockAction;
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试triggerValidationFunction方法的正常执行流程
     */
    @Test
    @DisplayName("triggerValidationFunction - 正常验证函数触发")
    void testTriggerValidationFunction_NormalFlow() {
        // 准备测试数据 - mockButton.getActions()已在setupMockBehaviors中配置

        // 执行测试
        ButtonExecutor.Result result = customButtonExecutor.triggerValidationFunction(mockUser, mockArg);

        // 验证结果
        assertNotNull(result, "结果不应为null");

        // 验证Mock调用
        verify(buttonService).findButtonByApiName(mockUser, TEST_BUTTON_API_NAME, TEST_DESCRIBE_API_NAME);
        verify(describeLogicService).findObject(TEST_TENANT_ID, TEST_DESCRIBE_API_NAME);
        verify(actionService).findActionListByStage(eq(mockUser), eq(mockButton), eq(TEST_DESCRIBE_API_NAME), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试triggerValidationFunction方法在按钮不存在时的处理
     */
    @Test
    @DisplayName("triggerValidationFunction - 按钮不存在时返回空结果")
    void testTriggerValidationFunction_ButtonNotFound() {
        // 准备测试数据 - 按钮不存在
        when(buttonService.findButtonByApiName(any(User.class), anyString(), anyString()))
                .thenReturn(null);
        
        // 执行测试
        ButtonExecutor.Result result = customButtonExecutor.triggerValidationFunction(mockUser, mockArg);
        
        // 验证结果
        assertNotNull(result, "结果不应为null");
        // ButtonExecutor.Result没有isEmpty方法，检查是否为空结果
        assertFalse(result.isHasReturnValue(), "结果应为空");
        
        // 验证Mock调用
        verify(buttonService).findButtonByApiName(mockUser, TEST_BUTTON_API_NAME, TEST_DESCRIBE_API_NAME);
        // 按钮不存在时，不应调用后续方法
        verify(describeLogicService, never()).findObject(anyString(), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试triggerValidationFunction方法在按钮无动作时的处理
     */
    @Test
    @DisplayName("triggerValidationFunction - 按钮无动作时返回空结果")
    void testTriggerValidationFunction_NoActions() {
        // 准备测试数据 - 按钮无动作
        when(mockButton.getActions()).thenReturn(Collections.emptyList());
        
        // 执行测试
        ButtonExecutor.Result result = customButtonExecutor.triggerValidationFunction(mockUser, mockArg);
        
        // 验证结果
        assertNotNull(result, "结果不应为null");
        // ButtonExecutor.Result没有isEmpty方法，检查是否为空结果
        assertFalse(result.isHasReturnValue(), "结果应为空");
        
        // 验证Mock调用
        verify(buttonService).findButtonByApiName(mockUser, TEST_BUTTON_API_NAME, TEST_DESCRIBE_API_NAME);
        verify(describeLogicService).findObject(TEST_TENANT_ID, TEST_DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试triggerFunctionAction方法的正常执行流程
     */
    @Test
    @DisplayName("triggerFunctionAction - 正常函数动作触发")
    void testTriggerFunctionAction_NormalFlow() {
        // 准备测试数据
        ButtonExecutorContext context = ButtonExecutorContext.builder()
                .user(mockUser)
                .describe(mockDescribe)
                .button(mockButton)
                .stage(UdefActionExt.PRE)
                .build();
        
        // mockButton.getActions()已在setupMockBehaviors中配置

        // 执行测试
        ButtonExecutor.Result result = customButtonExecutor.triggerFunctionAction(mockArg, context);
        
        // 验证结果
        assertNotNull(result, "结果不应为null");
        
        // 验证Mock调用
        verify(actionService).findActionListByStage(eq(mockUser), eq(mockButton), eq(TEST_DESCRIBE_API_NAME), anyString());
        verify(actionExecutorManager, atLeastOnce()).getActionExecutor(any(ActionExecutorType.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试triggerFunctionAction方法处理ValidateException异常
     */
    @Test
    @DisplayName("triggerFunctionAction - 处理ValidateException异常")
    void testTriggerFunctionAction_ValidateException() {
        // 准备测试数据
        ButtonExecutorContext context = ButtonExecutorContext.builder()
                .user(mockUser)
                .describe(mockDescribe)
                .button(mockButton)
                .stage(UdefActionExt.PRE)
                .build();
        
        // mockButton.getActions()已在setupMockBehaviors中配置

        // 模拟异常
        ValidateException expectedException = new ValidateException("验证失败");
        when(mockActionExecutor.invoke(any(ButtonExecutor.Arg.class), any(ActionExecutorContext.class)))
                .thenThrow(expectedException);
        
        // 执行测试并验证异常
        FunctionException thrownException = assertThrows(FunctionException.class, () -> {
            customButtonExecutor.triggerFunctionAction(mockArg, context);
        });
        
        // 验证异常信息
        assertNotNull(thrownException.getMessage(), "异常消息不应为null");
        assertTrue(thrownException.getMessage().contains("验证失败"), "异常消息应包含原始错误信息");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试startCustomButton方法的正常执行流程
     */
    @Test
    @DisplayName("startCustomButton - 正常按钮启动流程")
    void testStartCustomButton_NormalFlow() {
        // 准备测试数据
        ButtonExecutorContext context = ButtonExecutorContext.builder()
                .user(mockUser)
                .describe(mockDescribe)
                .button(mockButton)
                .stage(UdefActionExt.CURRENT)
                .build();

        // 创建非PRE阶段的动作
        IUdefAction currentAction = createMockAction("current_action");
        when(currentAction.getStage()).thenReturn(UdefActionExt.CURRENT);

        // 修复类型问题 - mockButton.getActions()返回List<String>，需要单独Mock
        List<String> actionIds = Arrays.asList("current_action");
        when(mockButton.getActions()).thenReturn(actionIds);
        when(actionService.findActionList(any(User.class), any(IUdefButton.class), anyString()))
                .thenReturn(Arrays.asList(currentAction));

        // 执行测试
        ButtonExecutor.Result result = customButtonExecutor.startCustomButton(mockArg, context);

        // 验证结果
        assertNotNull(result, "结果不应为null");

        // 验证Mock调用
        verify(actionService).findActionList(mockUser, mockButton, TEST_DESCRIBE_API_NAME);
        verify(actionExecutorManager).getActionExecutor(any(ActionExecutorType.class));
        verify(mockActionExecutor).invoke(any(ButtonExecutor.Arg.class), any(ActionExecutorContext.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试startCustomButton方法在按钮无动作时的处理
     */
    @Test
    @DisplayName("startCustomButton - 按钮无动作时返回空结果")
    void testStartCustomButton_NoActions() {
        // 准备测试数据
        ButtonExecutorContext context = ButtonExecutorContext.builder()
                .user(mockUser)
                .describe(mockDescribe)
                .button(mockButton)
                .stage(UdefActionExt.CURRENT)
                .build();

        when(mockButton.getActions()).thenReturn(Collections.emptyList());

        // 执行测试
        ButtonExecutor.Result result = customButtonExecutor.startCustomButton(mockArg, context);

        // 验证结果
        assertNotNull(result, "结果不应为null");
        // ButtonExecutor.Result没有isEmpty方法，检查是否为空结果
        assertFalse(result.isHasReturnValue(), "结果应为空");

        // 验证Mock调用
        verify(actionService, never()).findActionList(any(User.class), any(IUdefButton.class), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试startCustomButton方法处理FunctionException异常
     */
    @Test
    @DisplayName("startCustomButton - 处理FunctionException异常")
    void testStartCustomButton_FunctionException() {
        // 准备测试数据
        ButtonExecutorContext context = ButtonExecutorContext.builder()
                .user(mockUser)
                .describe(mockDescribe)
                .button(mockButton)
                .stage(UdefActionExt.CURRENT)
                .build();

        IUdefAction currentAction = createMockAction("current_action");
        when(currentAction.getStage()).thenReturn(UdefActionExt.CURRENT);

        // 修复类型问题
        List<String> actionIds = Arrays.asList("current_action");
        when(mockButton.getActions()).thenReturn(actionIds);
        when(actionService.findActionList(any(User.class), any(IUdefButton.class), anyString()))
                .thenReturn(Arrays.asList(currentAction));

        // 模拟FunctionException异常
        FunctionException expectedException = new FunctionException("函数执行失败");
        when(mockActionExecutor.invoke(any(ButtonExecutor.Arg.class), any(ActionExecutorContext.class)))
                .thenThrow(expectedException);

        // 执行测试并验证异常
        ValidateException thrownException = assertThrows(ValidateException.class, () -> {
            customButtonExecutor.startCustomButton(mockArg, context);
        });

        // 验证异常信息
        assertNotNull(thrownException.getMessage(), "异常消息不应为null");
        assertTrue(thrownException.getMessage().contains("函数执行失败"), "异常消息应包含原始错误信息");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkParam方法的正常参数校验
     */
    @Test
    @DisplayName("checkParam - 正常参数校验通过")
    void testCheckParam_NormalValidation() {
        // 准备测试数据
        Map<String, Object> args = new HashMap<>();
        args.put("test_param", "test_value");

        when(mockButton.getParamForm()).thenReturn(Collections.emptyList());

        // 执行测试 - 应该不抛出异常
        assertDoesNotThrow(() -> {
            customButtonExecutor.checkParam(mockButton, mockDescribe, mockObjectData, args, mockUser);
        });

        // 验证Mock调用
        verify(functionPrivilegeService).getReadonlyFields(mockUser, TEST_DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkParam方法在参数为空时的校验
     */
    @Test
    @DisplayName("checkParam - 空参数表单时跳过校验")
    void testCheckParam_EmptyParamForm() {
        // 准备测试数据
        Map<String, Object> args = new HashMap<>();
        when(mockButton.getParamForm()).thenReturn(null);

        // 执行测试 - 应该不抛出异常
        assertDoesNotThrow(() -> {
            customButtonExecutor.checkParam(mockButton, mockDescribe, mockObjectData, args, mockUser);
        });

        // 验证Mock调用 - 空参数表单时不应调用权限检查
        verify(functionPrivilegeService, never()).getReadonlyFields(any(User.class), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试triggerValidateRule方法的正常执行流程
     */
    @Test
    @DisplayName("triggerValidateRule - 正常验证规则触发")
    void testTriggerValidateRule_NormalFlow() {
        // 准备测试数据
        ButtonExecutorContext context = ButtonExecutorContext.builder()
                .user(mockUser)
                .describe(mockDescribe)
                .button(mockButton)
                .stage(UdefActionExt.PRE)
                .build();

        when(actionService.findActionList(any(User.class), any(IUdefButton.class), anyString()))
                .thenReturn(mockActionList);

        // 执行测试
        ButtonExecutor.Result result = customButtonExecutor.triggerValidateRule(mockArg, context);

        // 验证结果
        assertNotNull(result, "结果不应为null");

        // 验证Mock调用
        verify(actionService).findActionList(mockUser, mockButton, TEST_DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试triggerValidateRule方法在无动作时的处理
     */
    @Test
    @DisplayName("triggerValidateRule - 无动作时返回空结果")
    void testTriggerValidateRule_NoActions() {
        // 准备测试数据
        ButtonExecutorContext context = ButtonExecutorContext.builder()
                .user(mockUser)
                .describe(mockDescribe)
                .button(mockButton)
                .stage(UdefActionExt.PRE)
                .build();

        when(actionService.findActionList(any(User.class), any(IUdefButton.class), anyString()))
                .thenReturn(Collections.emptyList());

        // 执行测试
        ButtonExecutor.Result result = customButtonExecutor.triggerValidateRule(mockArg, context);

        // 验证结果
        assertNotNull(result, "结果不应为null");
        // ButtonExecutor.Result没有isEmpty方法，检查是否为空结果
        assertFalse(result.isHasReturnValue(), "结果应为空");

        // 验证Mock调用
        verify(actionService).findActionList(mockUser, mockButton, TEST_DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试不同动作阶段的参数化测试
     */
    @ParameterizedTest
    @ValueSource(strings = {UdefActionExt.PRE, UdefActionExt.CURRENT, UdefActionExt.POST})
    @DisplayName("triggerFunctionAction - 参数化测试不同动作阶段")
    void testTriggerFunctionAction_DifferentStages(String stage) {
        // 准备测试数据
        ButtonExecutorContext context = ButtonExecutorContext.builder()
                .user(mockUser)
                .describe(mockDescribe)
                .button(mockButton)
                .stage(stage)
                .build();

        when(mockButton.getActions()).thenReturn(Arrays.asList("test_action"));

        // 执行测试
        ButtonExecutor.Result result = customButtonExecutor.triggerFunctionAction(mockArg, context);

        // 验证结果
        assertNotNull(result, "结果不应为null");

        // 验证Mock调用
        verify(actionService).findActionListByStage(mockUser, mockButton, TEST_DESCRIBE_API_NAME, stage);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试MetaDataBusinessException异常处理
     */
    @Test
    @DisplayName("triggerFunctionAction - 处理MetaDataBusinessException异常")
    void testTriggerFunctionAction_MetaDataBusinessException() {
        // 准备测试数据
        ButtonExecutorContext context = ButtonExecutorContext.builder()
                .user(mockUser)
                .describe(mockDescribe)
                .button(mockButton)
                .stage(UdefActionExt.PRE)
                .build();

        when(mockButton.getActions()).thenReturn(Arrays.asList("test_action"));

        // 模拟MetaDataBusinessException异常
        MetaDataBusinessException expectedException = new MetaDataBusinessException("元数据业务异常");
        when(mockActionExecutor.invoke(any(ButtonExecutor.Arg.class), any(ActionExecutorContext.class)))
                .thenThrow(expectedException);

        // 执行测试并验证异常
        FunctionException thrownException = assertThrows(FunctionException.class, () -> {
            customButtonExecutor.triggerFunctionAction(mockArg, context);
        });

        // 验证异常信息
        assertNotNull(thrownException.getMessage(), "异常消息不应为null");
        assertTrue(thrownException.getMessage().contains("元数据业务异常"), "异常消息应包含原始错误信息");
    }
}
