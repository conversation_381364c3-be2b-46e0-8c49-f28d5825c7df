package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.exception.FunctionTimeoutException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.function.dto.FuncBizExtendParam;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataMiscService;
import com.facishare.paas.appframework.metadata.QuoteValueService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.net.SocketTimeoutException;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AbstractFuncAction单元测试 - JUnit5版本
 * 
 * GenerateByAI
 * 
 * 测试AbstractFuncAction抽象基类的核心功能：
 * - getActionParameter: 参数解析
 * - invoke: 主入口方法和异步调用机制
 * - _invoke: 核心执行流程
 * - handleException: 异常处理
 * - handleResult: 结果处理
 * - executeFunction: 函数执行
 * - validateResult: 结果验证
 * 
 * 覆盖场景：
 * - 正常执行流程
 * - 异步执行机制
 * - 各种异常处理
 * - 参数解析和处理
 * - 抽象方法调用机制
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("AbstractFuncAction - 抽象函数动作基类测试")
class AbstractFuncActionTest {

    @Mock
    private FunctionLogicService functionLogicService;
    
    @Mock
    private ParseVarService parseVarService;
    
    @Mock
    private MetaDataMiscService metaDataMiscService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private QuoteValueService quoteValueService;
    
    @Mock
    private ArgumentProcessorService argumentProcessorService;

    @Spy
    @InjectMocks
    private TestableAbstractFuncAction abstractFuncAction;

    // 测试常量
    private static final String TENANT_ID = "74255";
    private static final String USER_ID = "1000";
    private static final String FUNCTION_API_NAME = "testFunction";
    private static final String DESCRIBE_API_NAME = "TestObject";
    private static final String BUTTON_API_NAME = "testButton";

    private User testUser;
    private IObjectData testObjectData;
    private IObjectDescribe testDescribe;
    private IUdefButton testButton;
    private IUdefAction testAction;
    private IUdefFunction testFunction;
    private ActionExecutorContext testContext;
    private ButtonExecutor.Arg testArg;

    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser = new User(TENANT_ID, USER_ID);
        
        // 创建测试对象数据
        testObjectData = new ObjectData();
        testObjectData.set("id", "123");
        testObjectData.set("name", "test");
        
        // 创建Mock对象
        testDescribe = mock(IObjectDescribe.class);
        testButton = mock(IUdefButton.class);
        testAction = mock(IUdefAction.class);
        testFunction = mock(IUdefFunction.class);
        testContext = mock(ActionExecutorContext.class);
        testArg = mock(ButtonExecutor.Arg.class);
        
        // 配置基础Mock行为
        when(testDescribe.getApiName()).thenReturn(DESCRIBE_API_NAME);
        when(testButton.getApiName()).thenReturn(BUTTON_API_NAME);
        when(testFunction.getApiName()).thenReturn(FUNCTION_API_NAME);
        when(testContext.getUser()).thenReturn(testUser);
        when(testContext.getDescribe()).thenReturn(testDescribe);
        when(testContext.getButton()).thenReturn(testButton);
        when(testContext.getAction()).thenReturn(testAction);
        when(testContext.getIgnoreFields()).thenReturn(Collections.emptySet());
        when(testArg.getObjectData()).thenReturn(testObjectData);
        when(testArg.toDetails()).thenReturn(Collections.emptyMap());
        when(testArg.getArgs()).thenReturn(Collections.emptyMap());
        when(testArg.getActionParams()).thenReturn(Collections.emptyMap());
        when(testArg.getRelatedDataList()).thenReturn(Collections.emptyMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getActionParameter方法的JSON解析功能
     */
    @Test
    @DisplayName("getActionParameter - JSON参数解析")
    void testGetActionParameter_JsonParsing() {
        // Given
        String actionParameterJson = "{\n"
                + "    \"func_api_name\": \"" + FUNCTION_API_NAME + "\",\n"
                + "    \"func_args\": [],\n"
                + "    \"ui_event_id\": \"event123\"\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        
        // When
        AbstractFuncAction.ActionParameter result = abstractFuncAction.getActionParameter(testAction);
        
        // Then
        assertNotNull(result, "结果不应为null");
        assertEquals(FUNCTION_API_NAME, result.getFunctionAPIName(), "函数API名称应正确解析");
        assertNotNull(result.getFunctionArgList(), "函数参数列表不应为null");
        assertEquals("event123", result.getUiEventId(), "UI事件ID应正确解析");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法的同步执行流程
     */
    @Test
    @DisplayName("invoke - 同步执行流程")
    void testInvoke_SynchronousExecution() {
        // Given
        String actionParameterJson = "{\n"
                + "    \"func_api_name\": \"" + FUNCTION_API_NAME + "\",\n"
                + "    \"func_args\": []\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        when(functionLogicService.findUDefFunction(testUser, FUNCTION_API_NAME, DESCRIBE_API_NAME))
                .thenReturn(testFunction);
        
        RunResult runResult = new RunResult();
        runResult.setSuccess(true);
        runResult.setFunctionResult("test result");
        when(functionLogicService.executeUDefFunction(
                any(User.class), any(IUdefFunction.class), any(Map.class), any(IObjectData.class),
                any(Map.class), any(Map.class), any(Map.class), any(FuncBizExtendParam.Arg.class)))
                .thenReturn(runResult);
        
        when(argumentProcessorService.processArguments(any(), any(), any(), any(), any(), any()))
                .thenReturn(Collections.emptyList());
        
        // 配置needAsyncInvoke返回false，确保同步执行
        doReturn(false).when(abstractFuncAction).needAsyncInvoke(testUser, DESCRIBE_API_NAME, BUTTON_API_NAME);
        
        // When
        ButtonExecutor.Result result = abstractFuncAction.invoke(testArg, testContext);
        
        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isHasReturnValue(), "应有返回值");
        assertEquals("test result", result.getReturnValue(), "返回值应正确");
        
        // 验证关键方法被调用
        verify(functionLogicService).findUDefFunction(testUser, FUNCTION_API_NAME, DESCRIBE_API_NAME);
        verify(functionLogicService).executeUDefFunction(
                any(User.class), any(IUdefFunction.class), any(Map.class), any(IObjectData.class),
                any(Map.class), any(Map.class), any(Map.class), any(FuncBizExtendParam.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法的异步执行流程
     */
    @Test
    @DisplayName("invoke - 异步执行流程")
    void testInvoke_AsynchronousExecution() {
        // Given
        String actionParameterJson = "{\n"
                + "    \"func_api_name\": \"" + FUNCTION_API_NAME + "\",\n"
                + "    \"func_args\": []\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        
        // 配置needAsyncInvoke返回true，触发异步执行
        doReturn(true).when(abstractFuncAction).needAsyncInvoke(testUser, DESCRIBE_API_NAME, BUTTON_API_NAME);
        
        // When
        ButtonExecutor.Result result = abstractFuncAction.invoke(testArg, testContext);
        
        // Then
        assertNull(result, "异步执行应返回null");
        
        // 验证synchronizeData被调用
        verify(testArg).synchronizeData();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试函数不存在时的异常处理
     */
    @Test
    @DisplayName("invoke - 函数不存在异常")
    void testInvoke_FunctionNotFound() {
        // Given
        String actionParameterJson = "{\n"
                + "    \"func_api_name\": \"nonExistentFunction\",\n"
                + "    \"func_args\": []\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        when(functionLogicService.findUDefFunction(testUser, "nonExistentFunction", DESCRIBE_API_NAME))
                .thenReturn(null);
        
        doReturn(false).when(abstractFuncAction).needAsyncInvoke(testUser, DESCRIBE_API_NAME, BUTTON_API_NAME);
        
        // When & Then
        FunctionException exception = assertThrows(FunctionException.class, () -> {
            abstractFuncAction.invoke(testArg, testContext);
        });
        
        assertNotNull(exception.getMessage(), "异常消息不应为null");
        verify(functionLogicService).findUDefFunction(testUser, "nonExistentFunction", DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateResult方法处理成功结果
     */
    @Test
    @DisplayName("validateResult - 成功结果验证")
    void testValidateResult_SuccessResult() {
        // Given
        RunResult successResult = new RunResult();
        successResult.setSuccess(true);
        
        // When & Then
        assertDoesNotThrow(() -> {
            abstractFuncAction.validateResult(successResult);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateResult方法处理失败结果
     */
    @Test
    @DisplayName("validateResult - 失败结果验证")
    void testValidateResult_FailureResult() {
        // Given
        RunResult failureResult = new RunResult();
        failureResult.setSuccess(false);
        failureResult.setErrorInfo("Function execution failed");
        
        // When & Then
        FunctionException exception = assertThrows(FunctionException.class, () -> {
            abstractFuncAction.validateResult(failureResult);
        });
        
        assertEquals("Function execution failed", exception.getMessage(), "异常消息应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateResult方法处理业务错误（400状态码）
     */
    @Test
    @DisplayName("validateResult - 业务错误处理")
    void testValidateResult_BusinessError() {
        // Given
        RunResult businessErrorResult = new RunResult();
        businessErrorResult.setSuccess(false);
        businessErrorResult.setCode(400);
        businessErrorResult.setErrorInfo("Business validation failed");
        
        // When & Then
        FunctionException exception = assertThrows(FunctionException.class, () -> {
            abstractFuncAction.validateResult(businessErrorResult);
        });
        
        assertEquals("Business validation failed", exception.getMessage(), "业务错误消息应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：参数化测试handleException方法处理不同类型的异常
     */
    @ParameterizedTest
    @ValueSource(classes = {
            RestProxyBusinessException.class,
            FunctionException.class,
            SocketTimeoutException.class,
            FunctionTimeoutException.class,
            RuntimeException.class
    })
    @DisplayName("handleException - 不同异常类型处理")
    void testHandleException_DifferentExceptionTypes(Class<? extends Exception> exceptionClass) {
        // Given
        RuntimeException testException;
        if (exceptionClass == RestProxyBusinessException.class) {
            testException = new RestProxyBusinessException(500, "Proxy error");
        } else if (exceptionClass == FunctionException.class) {
            testException = new FunctionException("Function error");
        } else if (exceptionClass == SocketTimeoutException.class) {
            testException = new RuntimeException(new SocketTimeoutException("Timeout"));
        } else if (exceptionClass == FunctionTimeoutException.class) {
            testException = new RuntimeException(new FunctionTimeoutException("Function timeout"));
        } else {
            testException = new RuntimeException("Generic error");
        }
        
        // When & Then
        FunctionException result = assertThrows(FunctionException.class, () -> {
            abstractFuncAction.handleException(testException, testFunction);
        });
        
        assertNotNull(result.getMessage(), "异常消息不应为null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试handleResult方法处理有返回值的结果
     */
    @Test
    @DisplayName("handleResult - 有返回值结果处理")
    void testHandleResult_WithReturnValue() {
        // Given
        RunResult runResult = new RunResult();
        runResult.setFunctionResult("test return value");
        
        // When
        ButtonExecutor.Result result = abstractFuncAction.handleResult(testUser, testArg, runResult);
        
        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isHasReturnValue(), "应标记为有返回值");
        assertEquals("test return value", result.getReturnValue(), "返回值应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试handleResult方法处理无返回值的结果
     */
    @Test
    @DisplayName("handleResult - 无返回值结果处理")
    void testHandleResult_WithoutReturnValue() {
        // Given
        RunResult runResult = new RunResult();
        runResult.setFunctionResult(null);
        
        // When
        ButtonExecutor.Result result = abstractFuncAction.handleResult(testUser, testArg, runResult);
        
        // Then
        assertNotNull(result, "结果不应为null");
        assertFalse(result.isHasReturnValue(), "应标记为无返回值");
        assertNull(result.getReturnValue(), "返回值应为null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试executeFunction方法的核心执行逻辑
     */
    @Test
    @DisplayName("executeFunction - 核心执行逻辑")
    void testExecuteFunction_CoreLogic() {
        // Given
        Map<String, Object> functionArgMap = new HashMap<>();
        functionArgMap.put("param1", "value1");
        functionArgMap.put("param2", "value2");

        Map<String, List<IObjectData>> details = new HashMap<>();

        RunResult expectedResult = new RunResult();
        expectedResult.setSuccess(true);
        expectedResult.setFunctionResult("execution result");

        when(functionLogicService.executeUDefFunction(
                eq(testUser), eq(testFunction), eq(functionArgMap), eq(testObjectData),
                eq(details), any(Map.class), any(Map.class), any(FuncBizExtendParam.Arg.class)))
                .thenReturn(expectedResult);

        // When
        RunResult result = abstractFuncAction.executeFunction(testArg, testContext, testFunction, functionArgMap, details);

        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isSuccess(), "执行应成功");
        assertEquals("execution result", result.getFunctionResult(), "执行结果应正确");

        // 验证关键方法被调用
        verify(quoteValueService).fillQuoteValueVirtualField(testUser, testObjectData, details);
        verify(functionLogicService).executeUDefFunction(
                eq(testUser), eq(testFunction), eq(functionArgMap), eq(testObjectData),
                eq(details), any(), any(), any(FuncBizExtendParam.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试executeFunction方法处理带搜索查询的上下文
     */
    @Test
    @DisplayName("executeFunction - 带搜索查询的上下文")
    void testExecuteFunction_WithSearchQuery() {
        // Given
        Map<String, Object> functionArgMap = new HashMap<>();
        Map<String, List<IObjectData>> details = new HashMap<>();
        Map<String, Object> searchQuery = new HashMap<>();
        searchQuery.put("keyword", "test");
        searchQuery.put("status", "active");

        when(testContext.getSearchQuery()).thenReturn(searchQuery);

        RunResult expectedResult = new RunResult();
        expectedResult.setSuccess(true);

        when(functionLogicService.executeUDefFunction(
                any(), any(), any(), any(), any(), any(), any(), any(FuncBizExtendParam.Arg.class)))
                .thenReturn(expectedResult);

        // When
        RunResult result = abstractFuncAction.executeFunction(testArg, testContext, testFunction, functionArgMap, details);

        // Then
        assertNotNull(result, "结果不应为null");

        // 验证FuncBizExtendParam.Arg包含搜索查询
        verify(functionLogicService).executeUDefFunction(
                eq(testUser), eq(testFunction), eq(functionArgMap), eq(testObjectData),
                eq(details), any(Map.class), any(Map.class), any(FuncBizExtendParam.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试needAsyncInvoke方法的默认行为
     */
    @Test
    @DisplayName("needAsyncInvoke - 默认行为")
    void testNeedAsyncInvoke_DefaultBehavior() {
        // When
        boolean result = abstractFuncAction.needAsyncInvoke(testUser, DESCRIBE_API_NAME, BUTTON_API_NAME);

        // Then
        assertFalse(result, "默认应返回false，即同步执行");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试processFunctionArgs方法（已废弃但仍需测试兼容性）
     */
    @Test
    @DisplayName("processFunctionArgs - 废弃方法兼容性测试")
    void testProcessFunctionArgs_DeprecatedMethod() {
        // Given
        List<AbstractFuncAction.FunctionArg> args = new ArrayList<>();
        AbstractFuncAction.FunctionArg arg = new AbstractFuncAction.FunctionArg();
        arg.setName("testArg");
        arg.setType("text");
        arg.setValue("$testValue$");
        args.add(arg);

        Map<String, Object> variableData = new HashMap<>();
        variableData.put("testValue", "processed value");

        List<AbstractFuncAction.FunctionArg> processedArgs = new ArrayList<>();
        AbstractFuncAction.FunctionArg processedArg = new AbstractFuncAction.FunctionArg();
        processedArg.setName("testArg");
        processedArg.setType("text");
        processedArg.setValue("$testValue$");
        processedArg.setRealValue("processed value");
        processedArgs.add(processedArg);

        when(argumentProcessorService.processArguments(args, variableData, testObjectData, testDescribe, testUser, testButton))
                .thenReturn((List) processedArgs);

        // When
        @SuppressWarnings("deprecation")
        List<AbstractFuncAction.FunctionArg> result = abstractFuncAction.processFunctionArgs(
                args, variableData, testObjectData, testDescribe, testUser, testButton);

        // Then
        assertNotNull(result, "结果不应为null");
        assertEquals(1, result.size(), "应返回1个处理后的参数");
        assertEquals("processed value", result.get(0).getRealValue(), "参数值应正确处理");

        // 验证委托给ArgumentProcessorService
        verify(argumentProcessorService).processArguments(args, variableData, testObjectData, testDescribe, testUser, testButton);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ActionParameter.fromJson静态方法
     */
    @Test
    @DisplayName("ActionParameter.fromJson - JSON解析静态方法")
    void testActionParameter_FromJson() {
        // Given
        String json = "{\n"
                + "    \"func_api_name\": \"testFunc\",\n"
                + "    \"func_args\": [\n"
                + "        {\n"
                + "            \"name\": \"arg1\",\n"
                + "            \"type\": \"text\",\n"
                + "            \"value\": \"value1\"\n"
                + "        }\n"
                + "    ],\n"
                + "    \"ui_event_id\": \"event456\"\n"
                + "}";

        // When
        AbstractFuncAction.ActionParameter result = AbstractFuncAction.ActionParameter.fromJson(json);

        // Then
        assertNotNull(result, "结果不应为null");
        assertEquals("testFunc", result.getFunctionAPIName(), "函数API名称应正确");
        assertEquals("event456", result.getUiEventId(), "UI事件ID应正确");
        assertNotNull(result.getFunctionArgList(), "函数参数列表不应为null");
        assertEquals(1, result.getFunctionArgList().size(), "应有1个函数参数");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试FunctionArg数据类的基本功能
     */
    @Test
    @DisplayName("FunctionArg - 数据类基本功能")
    void testFunctionArg_DataClass() {
        // Given
        AbstractFuncAction.FunctionArg functionArg = new AbstractFuncAction.FunctionArg();

        // When
        functionArg.setName("testName");
        functionArg.setType("testType");
        functionArg.setValue("testValue");
        functionArg.setRealValue("testRealValue");

        // Then
        assertEquals("testName", functionArg.getName(), "名称应正确设置");
        assertEquals("testType", functionArg.getType(), "类型应正确设置");
        assertEquals("testValue", functionArg.getValue(), "值应正确设置");
        assertEquals("testRealValue", functionArg.getRealValue(), "实际值应正确设置");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试完整的执行流程集成测试
     */
    @Test
    @DisplayName("invoke - 完整执行流程集成测试")
    void testInvoke_FullExecutionFlow() {
        // Given
        String actionParameterJson = "{\n"
                + "    \"func_api_name\": \"" + FUNCTION_API_NAME + "\",\n"
                + "    \"func_args\": [\n"
                + "        {\n"
                + "            \"name\": \"param1\",\n"
                + "            \"type\": \"text\",\n"
                + "            \"value\": \"$testVar$\"\n"
                + "        }\n"
                + "    ]\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        when(functionLogicService.findUDefFunction(testUser, FUNCTION_API_NAME, DESCRIBE_API_NAME))
                .thenReturn(testFunction);

        // 配置参数处理
        List<AbstractFuncAction.FunctionArg> processedArgs = new ArrayList<>();
        AbstractFuncAction.FunctionArg processedArg = new AbstractFuncAction.FunctionArg();
        processedArg.setName("param1");
        processedArg.setRealValue("processed value");
        processedArgs.add(processedArg);

        when(argumentProcessorService.processArguments(any(), any(), any(), any(), any(), any()))
                .thenReturn((List) processedArgs);

        // 配置函数执行结果
        RunResult runResult = new RunResult();
        runResult.setSuccess(true);
        runResult.setFunctionResult("integration test result");
        when(functionLogicService.executeUDefFunction(
                any(User.class), any(IUdefFunction.class), any(Map.class), any(IObjectData.class),
                any(Map.class), any(Map.class), any(Map.class), any(FuncBizExtendParam.Arg.class)))
                .thenReturn(runResult);

        doReturn(false).when(abstractFuncAction).needAsyncInvoke(testUser, DESCRIBE_API_NAME, BUTTON_API_NAME);

        // When
        ButtonExecutor.Result result = abstractFuncAction.invoke(testArg, testContext);

        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isHasReturnValue(), "应有返回值");
        assertEquals("integration test result", result.getReturnValue(), "返回值应正确");

        // 验证完整的执行流程
        verify(functionLogicService).findUDefFunction(testUser, FUNCTION_API_NAME, DESCRIBE_API_NAME);
        verify(argumentProcessorService).processArguments(any(), any(), any(), any(), any(), any());
        verify(functionLogicService).executeUDefFunction(
                any(User.class), any(IUdefFunction.class), any(Map.class), any(IObjectData.class),
                any(Map.class), any(Map.class), any(Map.class), any(FuncBizExtendParam.Arg.class));
        verify(quoteValueService).fillQuoteValueVirtualField(any(), any(), any());
    }

    /**
     * 测试用的AbstractFuncAction具体实现类
     */
    static class TestableAbstractFuncAction extends AbstractFuncAction {
        @Override
        public ActionExecutorType getType() {
            return ActionExecutorType.CURRENT_FUNCTION;
        }
    }
}
