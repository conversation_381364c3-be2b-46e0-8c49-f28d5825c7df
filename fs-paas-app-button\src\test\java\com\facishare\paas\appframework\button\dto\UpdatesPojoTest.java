package com.facishare.paas.appframework.button.dto;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * UpdatesPojo DTO单元测试 - JUnit5版本
 * 
 * GenerateByAI
 * 
 * 测试UpdatesPojo更新数据传输对象：
 * - UpdatesPojo主类的字段设置和获取
 * - Field内部类的构造和字段设置
 * - Builder模式的使用
 * - JSON序列化和反序列化
 * - 边界条件和null值处理
 * - 复杂字段类型验证
 * 
 * 覆盖场景：
 * - 正常更新数据设置
 * - 空值和null值处理
 * - 复杂字段结构
 * - JSON数据转换
 * - 数据完整性验证
 */
@DisplayName("UpdatesPojo - 更新数据DTO测试")
class UpdatesPojoTest {

    private Gson gson;
    private UpdatesPojo updatesPojo;

    @BeforeEach
    void setUp() {
        gson = new GsonBuilder().create();
        updatesPojo = new UpdatesPojo();
    }

    /**
     * 创建测试用的元数据Map
     */
    private Map<String, Object> createMetadataMap() {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("created_by", "user123");
        metadata.put("tags", Arrays.asList("tag1", "tag2"));
        return metadata;
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试UpdatesPojo基本字段设置和获取
     */
    @Test
    @DisplayName("UpdatesPojo - 基本字段设置和获取测试")
    void testUpdatesPojoBasicFields() {
        // Given
        List<UpdatesPojo.Field> fields = Arrays.asList(
                UpdatesPojo.Field.builder()
                        .field("name")
                        .value("test name")
                        .var_type("string")
                        .build(),
                UpdatesPojo.Field.builder()
                        .field("amount")
                        .value(1000)
                        .var_type("number")
                        .decimal_places(2)
                        .build()
        );

        // When
        updatesPojo.setFields(fields);

        // Then
        assertEquals(fields, updatesPojo.getFields(), "字段列表应正确设置");
        assertEquals(2, updatesPojo.getFields().size(), "字段数量应为2");
        
        UpdatesPojo.Field firstField = updatesPojo.getFields().get(0);
        assertEquals("name", firstField.getField(), "第一个字段名应正确");
        assertEquals("test name", firstField.getValue(), "第一个字段值应正确");
        assertEquals("string", firstField.getVar_type(), "第一个字段类型应正确");
        
        UpdatesPojo.Field secondField = updatesPojo.getFields().get(1);
        assertEquals("amount", secondField.getField(), "第二个字段名应正确");
        assertEquals(1000, secondField.getValue(), "第二个字段值应正确");
        assertEquals("number", secondField.getVar_type(), "第二个字段类型应正确");
        assertEquals(2, secondField.getDecimal_places(), "小数位数应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Field类的Builder模式构造
     */
    @Test
    @DisplayName("Field - Builder模式构造测试")
    void testFieldBuilder() {
        // Given & When
        UpdatesPojo.Field field = UpdatesPojo.Field.builder()
                .field("test_field")
                .value("test_value")
                .default_to_zero(true)
                .var_type("string")
                .return_type("text")
                .decimal_places(3)
                .other_value("other_test_value")
                .build();

        // Then
        assertNotNull(field, "Field对象不应为null");
        assertEquals("test_field", field.getField(), "字段名应正确");
        assertEquals("test_value", field.getValue(), "字段值应正确");
        assertTrue(field.isDefault_to_zero(), "默认为零标志应为true");
        assertEquals("string", field.getVar_type(), "变量类型应正确");
        assertEquals("text", field.getReturn_type(), "返回类型应正确");
        assertEquals(3, field.getDecimal_places(), "小数位数应正确");
        assertEquals("other_test_value", field.getOther_value(), "其他值应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Field类的构造函数
     */
    @Test
    @DisplayName("Field - 构造函数测试")
    void testFieldConstructors() {
        // Test NoArgsConstructor
        UpdatesPojo.Field field1 = new UpdatesPojo.Field();
        assertNotNull(field1, "无参构造函数创建的对象不应为null");
        assertNull(field1.getField(), "默认字段名应为null");
        assertNull(field1.getValue(), "默认字段值应为null");
        assertFalse(field1.isDefault_to_zero(), "默认为零标志应为false");
        assertEquals(0, field1.getDecimal_places(), "默认小数位数应为0");

        // Test AllArgsConstructor
        UpdatesPojo.Field field2 = new UpdatesPojo.Field(
                "test_field", "test_value", true, "number", "decimal", 2, "other"
        );
        assertNotNull(field2, "全参构造函数创建的对象不应为null");
        assertEquals("test_field", field2.getField(), "字段名应正确");
        assertEquals("test_value", field2.getValue(), "字段值应正确");
        assertTrue(field2.isDefault_to_zero(), "默认为零标志应为true");
        assertEquals("number", field2.getVar_type(), "变量类型应正确");
        assertEquals("decimal", field2.getReturn_type(), "返回类型应正确");
        assertEquals(2, field2.getDecimal_places(), "小数位数应正确");
        assertEquals("other", field2.getOther_value(), "其他值应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试null值处理
     */
    @Test
    @DisplayName("null值处理测试")
    void testNullValueHandling() {
        // When
        updatesPojo.setFields(null);

        // Then
        assertNull(updatesPojo.getFields(), "字段列表应为null");

        // Test Field with null values
        UpdatesPojo.Field field = UpdatesPojo.Field.builder()
                .field(null)
                .value(null)
                .var_type(null)
                .return_type(null)
                .other_value(null)
                .build();

        assertNull(field.getField(), "字段名应为null");
        assertNull(field.getValue(), "字段值应为null");
        assertNull(field.getVar_type(), "变量类型应为null");
        assertNull(field.getReturn_type(), "返回类型应为null");
        assertNull(field.getOther_value(), "其他值应为null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空集合处理
     */
    @Test
    @DisplayName("空集合处理测试")
    void testEmptyCollectionHandling() {
        // Given
        List<UpdatesPojo.Field> emptyFields = new ArrayList<>();

        // When
        updatesPojo.setFields(emptyFields);

        // Then
        assertNotNull(updatesPojo.getFields(), "字段列表不应为null");
        assertTrue(updatesPojo.getFields().isEmpty(), "字段列表应为空");
        assertEquals(0, updatesPojo.getFields().size(), "字段数量应为0");
    }

    /**
     * GenerateByAI
     * 测试内容描述：参数化测试不同的变量类型
     */
    @ParameterizedTest
    @ValueSource(strings = {"string", "number", "boolean", "date", "datetime", "decimal", "text"})
    @DisplayName("Field - 不同变量类型测试")
    void testFieldDifferentVarTypes(String varType) {
        // When
        UpdatesPojo.Field field = UpdatesPojo.Field.builder()
                .field("test_field")
                .value("test_value")
                .var_type(varType)
                .build();

        // Then
        assertEquals(varType, field.getVar_type(), "变量类型应正确设置");
        assertNotNull(field.getVar_type(), "变量类型不应为null");
        assertFalse(field.getVar_type().isEmpty(), "变量类型不应为空字符串");
    }

    /**
     * GenerateByAI
     * 测试内容描述：参数化测试不同的返回类型
     */
    @ParameterizedTest
    @ValueSource(strings = {"text", "number", "boolean", "json", "xml", "html"})
    @DisplayName("Field - 不同返回类型测试")
    void testFieldDifferentReturnTypes(String returnType) {
        // When
        UpdatesPojo.Field field = UpdatesPojo.Field.builder()
                .field("test_field")
                .value("test_value")
                .return_type(returnType)
                .build();

        // Then
        assertEquals(returnType, field.getReturn_type(), "返回类型应正确设置");
        assertNotNull(field.getReturn_type(), "返回类型不应为null");
        assertFalse(field.getReturn_type().isEmpty(), "返回类型不应为空字符串");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试不同数据类型的字段值
     */
    @Test
    @DisplayName("Field - 不同数据类型字段值测试")
    void testFieldDifferentValueTypes() {
        // String value
        UpdatesPojo.Field stringField = UpdatesPojo.Field.builder()
                .field("string_field")
                .value("string_value")
                .var_type("string")
                .build();
        assertEquals("string_value", stringField.getValue(), "字符串值应正确");

        // Integer value
        UpdatesPojo.Field intField = UpdatesPojo.Field.builder()
                .field("int_field")
                .value(42)
                .var_type("number")
                .build();
        assertEquals(42, intField.getValue(), "整数值应正确");

        // Boolean value
        UpdatesPojo.Field boolField = UpdatesPojo.Field.builder()
                .field("bool_field")
                .value(true)
                .var_type("boolean")
                .build();
        assertEquals(true, boolField.getValue(), "布尔值应正确");

        // Double value
        UpdatesPojo.Field doubleField = UpdatesPojo.Field.builder()
                .field("double_field")
                .value(3.14159)
                .var_type("decimal")
                .decimal_places(5)
                .build();
        assertEquals(3.14159, doubleField.getValue(), "小数值应正确");
        assertEquals(5, doubleField.getDecimal_places(), "小数位数应正确");

        // List value
        List<String> listValue = Arrays.asList("item1", "item2", "item3");
        UpdatesPojo.Field listField = UpdatesPojo.Field.builder()
                .field("list_field")
                .value(listValue)
                .var_type("array")
                .build();
        assertEquals(listValue, listField.getValue(), "列表值应正确");

        // Map value
        Map<String, Object> mapValue = new HashMap<>();
        mapValue.put("key1", "value1");
        mapValue.put("key2", 42);
        UpdatesPojo.Field mapField = UpdatesPojo.Field.builder()
                .field("map_field")
                .value(mapValue)
                .var_type("object")
                .build();
        assertEquals(mapValue, mapField.getValue(), "Map值应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试JSON序列化
     */
    @Test
    @DisplayName("JSON序列化测试")
    void testJsonSerialization() {
        // Given
        List<UpdatesPojo.Field> fields = Arrays.asList(
                UpdatesPojo.Field.builder()
                        .field("name")
                        .value("test name")
                        .var_type("string")
                        .return_type("text")
                        .build(),
                UpdatesPojo.Field.builder()
                        .field("amount")
                        .value(1000.50)
                        .default_to_zero(true)
                        .var_type("decimal")
                        .return_type("number")
                        .decimal_places(2)
                        .other_value("backup_amount")
                        .build()
        );
        updatesPojo.setFields(fields);

        // When
        String json = gson.toJson(updatesPojo);

        // Then
        assertNotNull(json, "JSON字符串不应为null");
        assertTrue(json.contains("name"), "JSON应包含字段名");
        assertTrue(json.contains("test name"), "JSON应包含字段值");
        assertTrue(json.contains("string"), "JSON应包含变量类型");
        assertTrue(json.contains("1000.5"), "JSON应包含数值");
        assertTrue(json.contains("decimal_places"), "JSON应包含小数位数");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试JSON反序列化
     */
    @Test
    @DisplayName("JSON反序列化测试")
    void testJsonDeserialization() {
        // Given
        String json = "{\n" +
                "  \"fields\": [\n" +
                "    {\n" +
                "      \"field\": \"name\",\n" +
                "      \"value\": \"test name\",\n" +
                "      \"default_to_zero\": false,\n" +
                "      \"var_type\": \"string\",\n" +
                "      \"return_type\": \"text\",\n" +
                "      \"decimal_places\": 0,\n" +
                "      \"other_value\": null\n" +
                "    },\n" +
                "    {\n" +
                "      \"field\": \"amount\",\n" +
                "      \"value\": 1000.50,\n" +
                "      \"default_to_zero\": true,\n" +
                "      \"var_type\": \"decimal\",\n" +
                "      \"return_type\": \"number\",\n" +
                "      \"decimal_places\": 2,\n" +
                "      \"other_value\": \"backup_amount\"\n" +
                "    }\n" +
                "  ]\n" +
                "}";

        // When
        UpdatesPojo deserializedPojo = gson.fromJson(json, UpdatesPojo.class);

        // Then
        assertNotNull(deserializedPojo, "反序列化对象不应为null");
        assertNotNull(deserializedPojo.getFields(), "字段列表不应为null");
        assertEquals(2, deserializedPojo.getFields().size(), "字段数量应为2");

        UpdatesPojo.Field firstField = deserializedPojo.getFields().get(0);
        assertEquals("name", firstField.getField(), "第一个字段名应正确");
        assertEquals("test name", firstField.getValue(), "第一个字段值应正确");
        assertFalse(firstField.isDefault_to_zero(), "第一个字段默认为零标志应为false");
        assertEquals("string", firstField.getVar_type(), "第一个字段变量类型应正确");
        assertEquals("text", firstField.getReturn_type(), "第一个字段返回类型应正确");

        UpdatesPojo.Field secondField = deserializedPojo.getFields().get(1);
        assertEquals("amount", secondField.getField(), "第二个字段名应正确");
        assertEquals(1000.50, secondField.getValue(), "第二个字段值应正确");
        assertTrue(secondField.isDefault_to_zero(), "第二个字段默认为零标志应为true");
        assertEquals("decimal", secondField.getVar_type(), "第二个字段变量类型应正确");
        assertEquals("number", secondField.getReturn_type(), "第二个字段返回类型应正确");
        assertEquals(2, secondField.getDecimal_places(), "第二个字段小数位数应正确");
        assertEquals("backup_amount", secondField.getOther_value(), "第二个字段其他值应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试复杂字段结构
     */
    @Test
    @DisplayName("复杂字段结构测试")
    void testComplexFieldStructure() {
        // Given
        List<UpdatesPojo.Field> complexFields = Arrays.asList(
                // 字符串字段
                UpdatesPojo.Field.builder()
                        .field("description")
                        .value("这是一个复杂的描述字段，包含中文和特殊字符@#$%")
                        .var_type("text")
                        .return_type("string")
                        .other_value("默认描述")
                        .build(),
                // 数值字段
                UpdatesPojo.Field.builder()
                        .field("price")
                        .value(999.99)
                        .default_to_zero(true)
                        .var_type("decimal")
                        .return_type("currency")
                        .decimal_places(2)
                        .other_value("0.00")
                        .build(),
                // 布尔字段
                UpdatesPojo.Field.builder()
                        .field("is_active")
                        .value(true)
                        .default_to_zero(false)
                        .var_type("boolean")
                        .return_type("checkbox")
                        .other_value("false")
                        .build(),
                // 复杂对象字段
                UpdatesPojo.Field.builder()
                        .field("metadata")
                        .value(createMetadataMap())
                        .var_type("object")
                        .return_type("json")
                        .other_value("{}")
                        .build()
        );

        // When
        updatesPojo.setFields(complexFields);

        // Then
        assertEquals(4, updatesPojo.getFields().size(), "复杂字段数量应为4");
        
        // 验证字符串字段
        UpdatesPojo.Field descField = updatesPojo.getFields().get(0);
        assertTrue(((String) descField.getValue()).contains("中文"), "应包含中文字符");
        assertTrue(((String) descField.getValue()).contains("@#$%"), "应包含特殊字符");
        
        // 验证数值字段
        UpdatesPojo.Field priceField = updatesPojo.getFields().get(1);
        assertEquals(999.99, priceField.getValue(), "价格值应正确");
        assertEquals(2, priceField.getDecimal_places(), "小数位数应正确");
        assertTrue(priceField.isDefault_to_zero(), "默认为零标志应为true");
        
        // 验证布尔字段
        UpdatesPojo.Field activeField = updatesPojo.getFields().get(2);
        assertEquals(true, activeField.getValue(), "布尔值应正确");
        assertEquals("checkbox", activeField.getReturn_type(), "返回类型应为checkbox");
        
        // 验证复杂对象字段
        UpdatesPojo.Field metadataField = updatesPojo.getFields().get(3);
        assertTrue(metadataField.getValue() instanceof Map, "值应为Map类型");
        assertEquals("json", metadataField.getReturn_type(), "返回类型应为json");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试数据完整性验证
     */
    @Test
    @DisplayName("数据完整性验证测试")
    void testDataIntegrityValidation() {
        // Given
        List<UpdatesPojo.Field> fields = Arrays.asList(
                UpdatesPojo.Field.builder()
                        .field("test_field")
                        .value("test_value")
                        .default_to_zero(false)
                        .var_type("string")
                        .return_type("text")
                        .decimal_places(0)
                        .other_value("default")
                        .build()
        );

        // When
        updatesPojo.setFields(fields);

        // Then - 验证所有数据都正确设置
        assertNotNull(updatesPojo.getFields(), "字段列表不应为null");
        assertEquals(1, updatesPojo.getFields().size(), "字段数量应为1");
        
        UpdatesPojo.Field field = updatesPojo.getFields().get(0);
        assertEquals("test_field", field.getField(), "字段名应正确");
        assertEquals("test_value", field.getValue(), "字段值应正确");
        assertFalse(field.isDefault_to_zero(), "默认为零标志应为false");
        assertEquals("string", field.getVar_type(), "变量类型应正确");
        assertEquals("text", field.getReturn_type(), "返回类型应正确");
        assertEquals(0, field.getDecimal_places(), "小数位数应正确");
        assertEquals("default", field.getOther_value(), "其他值应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象相等性
     */
    @Test
    @DisplayName("对象相等性测试")
    void testObjectEquality() {
        // Given
        UpdatesPojo.Field field1 = UpdatesPojo.Field.builder()
                .field("test")
                .value("value")
                .var_type("string")
                .build();
        
        UpdatesPojo.Field field2 = UpdatesPojo.Field.builder()
                .field("test")
                .value("value")
                .var_type("string")
                .build();

        // When & Then
        // 注意：由于使用了@Data注解，Lombok会自动生成equals和hashCode方法
        assertEquals(field1, field2, "相同数据的Field对象应相等");
        assertEquals(field1.hashCode(), field2.hashCode(), "相同数据的Field对象hashCode应相等");
        
        // 修改一个对象的数据
        field2.setValue("different_value");
        assertNotEquals(field1, field2, "不同数据的Field对象应不相等");
    }
}
