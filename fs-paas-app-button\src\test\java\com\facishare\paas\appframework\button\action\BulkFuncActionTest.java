package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.function.dto.BatchDataExecuteFunction;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataMiscService;
import com.facishare.paas.appframework.metadata.QuoteValueService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * BulkFuncAction单元测试 - JUnit5版本
 * 
 * GenerateByAI
 * 
 * 测试BulkFuncAction批量函数动作的核心功能：
 * - getType: 返回BULK_FUNCTION类型
 * - executeFunction: 批量执行逻辑
 * - 批量数据处理和结果聚合
 * - 继承自UIActionFuncAction的UI动作处理
 * 
 * 覆盖场景：
 * - 批量执行成功场景
 * - 批量执行失败场景
 * - 部分成功部分失败的混合场景
 * - 不同批量大小的处理
 * - 批量数据ID的处理
 * - 结果类型和错误信息的处理
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("BulkFuncAction - 批量函数动作测试")
class BulkFuncActionTest {

    @Mock
    private FunctionLogicService functionLogicService;
    
    @Mock
    private ParseVarService parseVarService;
    
    @Mock
    private MetaDataMiscService metaDataMiscService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private QuoteValueService quoteValueService;
    
    @Mock
    private ArgumentProcessorService argumentProcessorService;

    @Spy
    @InjectMocks
    private BulkFuncAction bulkFuncAction;

    // 测试常量
    private static final String TENANT_ID = "74255";
    private static final String USER_ID = "1000";
    private static final String FUNCTION_API_NAME = "testBulkFunction";
    private static final String DESCRIBE_API_NAME = "TestObject";
    private static final String BUTTON_API_NAME = "testBulkButton";

    private User testUser;
    private IObjectData testObjectData;
    private IObjectDescribe testDescribe;
    private IUdefButton testButton;
    private IUdefAction testAction;
    private IUdefFunction testFunction;
    private ActionExecutorContext testContext;
    private ButtonExecutor.Arg testArg;

    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser = new User(TENANT_ID, USER_ID);
        
        // 创建测试对象数据
        testObjectData = new ObjectData();
        testObjectData.set("id", "123");
        testObjectData.set("name", "test");
        
        // 创建Mock对象
        testDescribe = mock(IObjectDescribe.class);
        testButton = mock(IUdefButton.class);
        testAction = mock(IUdefAction.class);
        testFunction = mock(IUdefFunction.class);
        testContext = mock(ActionExecutorContext.class);
        testArg = mock(ButtonExecutor.Arg.class);
        
        // 配置基础Mock行为
        when(testDescribe.getApiName()).thenReturn(DESCRIBE_API_NAME);
        when(testButton.getApiName()).thenReturn(BUTTON_API_NAME);
        when(testFunction.getApiName()).thenReturn(FUNCTION_API_NAME);
        when(testContext.getUser()).thenReturn(testUser);
        when(testContext.getDescribe()).thenReturn(testDescribe);
        when(testContext.getButton()).thenReturn(testButton);
        when(testContext.getAction()).thenReturn(testAction);
        when(testContext.getIgnoreFields()).thenReturn(Collections.emptySet());
        when(testArg.getObjectData()).thenReturn(testObjectData);
        when(testArg.toDetails()).thenReturn(Collections.emptyMap());
        when(testArg.getArgs()).thenReturn(Collections.emptyMap());
        when(testArg.getActionParams()).thenReturn(Collections.emptyMap());
        when(testArg.getRelatedDataList()).thenReturn(Collections.emptyMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getType方法返回BULK_FUNCTION类型
     */
    @Test
    @DisplayName("getType - 返回BULK_FUNCTION类型")
    void testGetType_ReturnsBulkFunctionType() {
        // When
        ActionExecutorType result = bulkFuncAction.getType();
        
        // Then
        assertNotNull(result, "类型不应为null");
        assertEquals(ActionExecutorType.BULK_FUNCTION, result, "应返回BULK_FUNCTION类型");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试executeFunction方法的批量执行成功场景
     */
    @Test
    @DisplayName("executeFunction - 批量执行成功")
    void testExecuteFunction_BatchExecutionSuccess() {
        // Given
        List<String> dataIds = Arrays.asList("id1", "id2", "id3");
        Map<String, Object> functionArgMap = new HashMap<>();
        functionArgMap.put("param1", "value1");
        Map<String, List<IObjectData>> details = new HashMap<>();
        
        when(testArg.getDataIds()).thenReturn(dataIds);
        
        // 配置批量执行结果
        BatchDataExecuteFunction.Result batchResult = new BatchDataExecuteFunction.Result();
        batchResult.setSuccess(true);
        batchResult.setFunctionResult("批量执行成功");
        batchResult.setReturnType("UIAction");
        
        when(functionLogicService.batchDataExecuteFunction(testUser, testFunction, dataIds, functionArgMap))
                .thenReturn(batchResult);
        
        // When
        RunResult result = bulkFuncAction.executeFunction(testArg, testContext, testFunction, functionArgMap, details);
        
        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isSuccess(), "执行应成功");
        assertEquals("批量执行成功", result.getFunctionResult(), "函数结果应正确");
        assertEquals("UIAction", result.getReturnType(), "返回类型应正确");
        assertNull(result.getErrorInfo(), "错误信息应为null");
        
        // 验证批量执行方法被调用
        verify(functionLogicService).batchDataExecuteFunction(testUser, testFunction, dataIds, functionArgMap);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试executeFunction方法的批量执行失败场景
     */
    @Test
    @DisplayName("executeFunction - 批量执行失败")
    void testExecuteFunction_BatchExecutionFailure() {
        // Given
        List<String> dataIds = Arrays.asList("id1", "id2", "id3");
        Map<String, Object> functionArgMap = new HashMap<>();
        Map<String, List<IObjectData>> details = new HashMap<>();
        
        when(testArg.getDataIds()).thenReturn(dataIds);
        
        // 配置批量执行失败结果
        BatchDataExecuteFunction.Result batchResult = new BatchDataExecuteFunction.Result();
        batchResult.setSuccess(false);
        batchResult.setErrorInfo("批量执行失败：权限不足");
        batchResult.setReturnType("Error");
        
        when(functionLogicService.batchDataExecuteFunction(testUser, testFunction, dataIds, functionArgMap))
                .thenReturn(batchResult);
        
        // When
        RunResult result = bulkFuncAction.executeFunction(testArg, testContext, testFunction, functionArgMap, details);
        
        // Then
        assertNotNull(result, "结果不应为null");
        assertFalse(result.isSuccess(), "执行应失败");
        assertNull(result.getFunctionResult(), "函数结果应为null");
        assertEquals("Error", result.getReturnType(), "返回类型应正确");
        assertEquals("批量执行失败：权限不足", result.getErrorInfo(), "错误信息应正确");
        
        // 验证批量执行方法被调用
        verify(functionLogicService).batchDataExecuteFunction(testUser, testFunction, dataIds, functionArgMap);
    }

    /**
     * GenerateByAI
     * 测试内容描述：参数化测试不同批量大小的处理
     */
    @ParameterizedTest
    @ValueSource(ints = {1, 5, 10, 50, 100})
    @DisplayName("executeFunction - 不同批量大小处理")
    void testExecuteFunction_DifferentBatchSizes(int batchSize) {
        // Given
        List<String> dataIds = new ArrayList<>();
        for (int i = 1; i <= batchSize; i++) {
            dataIds.add("id" + i);
        }
        
        Map<String, Object> functionArgMap = new HashMap<>();
        functionArgMap.put("batchSize", batchSize);
        Map<String, List<IObjectData>> details = new HashMap<>();
        
        when(testArg.getDataIds()).thenReturn(dataIds);
        
        // 配置批量执行结果
        BatchDataExecuteFunction.Result batchResult = new BatchDataExecuteFunction.Result();
        batchResult.setSuccess(true);
        batchResult.setFunctionResult("处理了" + batchSize + "条数据");
        
        when(functionLogicService.batchDataExecuteFunction(testUser, testFunction, dataIds, functionArgMap))
                .thenReturn(batchResult);
        
        // When
        RunResult result = bulkFuncAction.executeFunction(testArg, testContext, testFunction, functionArgMap, details);
        
        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isSuccess(), "执行应成功");
        assertEquals("处理了" + batchSize + "条数据", result.getFunctionResult(), "函数结果应包含批量大小");
        
        // 验证批量执行方法被正确调用
        verify(functionLogicService).batchDataExecuteFunction(testUser, testFunction, dataIds, functionArgMap);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试executeFunction方法处理空数据ID列表
     */
    @Test
    @DisplayName("executeFunction - 空数据ID列表处理")
    void testExecuteFunction_EmptyDataIds() {
        // Given
        List<String> emptyDataIds = Collections.emptyList();
        Map<String, Object> functionArgMap = new HashMap<>();
        Map<String, List<IObjectData>> details = new HashMap<>();
        
        when(testArg.getDataIds()).thenReturn(emptyDataIds);
        
        // 配置批量执行结果
        BatchDataExecuteFunction.Result batchResult = new BatchDataExecuteFunction.Result();
        batchResult.setSuccess(true);
        batchResult.setFunctionResult("没有数据需要处理");
        
        when(functionLogicService.batchDataExecuteFunction(testUser, testFunction, emptyDataIds, functionArgMap))
                .thenReturn(batchResult);
        
        // When
        RunResult result = bulkFuncAction.executeFunction(testArg, testContext, testFunction, functionArgMap, details);
        
        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isSuccess(), "执行应成功");
        assertEquals("没有数据需要处理", result.getFunctionResult(), "函数结果应正确");
        
        // 验证批量执行方法被调用
        verify(functionLogicService).batchDataExecuteFunction(testUser, testFunction, emptyDataIds, functionArgMap);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试executeFunction方法处理复杂的函数参数
     */
    @Test
    @DisplayName("executeFunction - 复杂函数参数处理")
    void testExecuteFunction_ComplexFunctionArgs() {
        // Given
        List<String> dataIds = Arrays.asList("id1", "id2");
        Map<String, Object> complexFunctionArgMap = new HashMap<>();
        complexFunctionArgMap.put("stringParam", "test value");
        complexFunctionArgMap.put("numberParam", 42);
        complexFunctionArgMap.put("booleanParam", true);
        complexFunctionArgMap.put("listParam", Arrays.asList("item1", "item2"));
        
        Map<String, Object> nestedMap = new HashMap<>();
        nestedMap.put("nestedKey", "nestedValue");
        complexFunctionArgMap.put("objectParam", nestedMap);
        
        Map<String, List<IObjectData>> details = new HashMap<>();
        
        when(testArg.getDataIds()).thenReturn(dataIds);
        
        // 配置批量执行结果
        BatchDataExecuteFunction.Result batchResult = new BatchDataExecuteFunction.Result();
        batchResult.setSuccess(true);
        batchResult.setFunctionResult("复杂参数处理成功");
        
        when(functionLogicService.batchDataExecuteFunction(testUser, testFunction, dataIds, complexFunctionArgMap))
                .thenReturn(batchResult);
        
        // When
        RunResult result = bulkFuncAction.executeFunction(testArg, testContext, testFunction, complexFunctionArgMap, details);
        
        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isSuccess(), "执行应成功");
        assertEquals("复杂参数处理成功", result.getFunctionResult(), "函数结果应正确");
        
        // 验证批量执行方法被调用，并且参数正确传递
        verify(functionLogicService).batchDataExecuteFunction(testUser, testFunction, dataIds, complexFunctionArgMap);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试executeFunction方法处理null返回结果
     */
    @Test
    @DisplayName("executeFunction - null返回结果处理")
    void testExecuteFunction_NullBatchResult() {
        // Given
        List<String> dataIds = Arrays.asList("id1");
        Map<String, Object> functionArgMap = new HashMap<>();
        Map<String, List<IObjectData>> details = new HashMap<>();
        
        when(testArg.getDataIds()).thenReturn(dataIds);
        
        // 配置批量执行返回null
        when(functionLogicService.batchDataExecuteFunction(testUser, testFunction, dataIds, functionArgMap))
                .thenReturn(null);
        
        // When & Then
        assertThrows(NullPointerException.class, () -> {
            bulkFuncAction.executeFunction(testArg, testContext, testFunction, functionArgMap, details);
        }, "null结果应抛出NullPointerException");
        
        // 验证批量执行方法被调用
        verify(functionLogicService).batchDataExecuteFunction(testUser, testFunction, dataIds, functionArgMap);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试完整的invoke执行流程
     */
    @Test
    @DisplayName("invoke - 完整批量执行流程")
    void testInvoke_FullBulkExecutionFlow() {
        // Given
        String actionParameterJson = "{\n"
                + "    \"func_api_name\": \"" + FUNCTION_API_NAME + "\",\n"
                + "    \"func_args\": [\n"
                + "        {\n"
                + "            \"name\": \"batchParam\",\n"
                + "            \"type\": \"text\",\n"
                + "            \"value\": \"batch value\"\n"
                + "        }\n"
                + "    ]\n"
                + "}";

        List<String> dataIds = Arrays.asList("bulk1", "bulk2", "bulk3");

        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        when(testArg.getDataIds()).thenReturn(dataIds);
        when(functionLogicService.findUDefFunction(testUser, FUNCTION_API_NAME, DESCRIBE_API_NAME))
                .thenReturn(testFunction);

        // 配置参数处理
        when(argumentProcessorService.processArguments(any(), any(), any(), any(), any(), any()))
                .thenReturn(Collections.emptyList());

        // 配置批量执行结果
        BatchDataExecuteFunction.Result batchResult = new BatchDataExecuteFunction.Result();
        batchResult.setSuccess(true);
        Map<String, Object> uiActionMap = new HashMap<>();
        uiActionMap.put("action", "WebAction");
        uiActionMap.put("url", "http://example.com");
        batchResult.setFunctionResult(uiActionMap);
        batchResult.setReturnType("UIAction");

        when(functionLogicService.batchDataExecuteFunction(eq(testUser), eq(testFunction), eq(dataIds), any(Map.class)))
                .thenReturn(batchResult);

        // 配置needAsyncInvoke返回false，确保同步执行
        doReturn(false).when(bulkFuncAction).needAsyncInvoke(testUser, DESCRIBE_API_NAME, BUTTON_API_NAME);

        // When
        ButtonExecutor.Result result = bulkFuncAction.invoke(testArg, testContext);

        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isHasReturnValue(), "应有返回值");
        assertEquals("UIAction", result.getReturnType(), "返回类型应正确");
        assertNotNull(result.getReturnValue(), "返回值不应为null");

        // 验证完整的执行流程
        verify(functionLogicService).findUDefFunction(testUser, FUNCTION_API_NAME, DESCRIBE_API_NAME);
        verify(functionLogicService).batchDataExecuteFunction(eq(testUser), eq(testFunction), eq(dataIds), any(Map.class));
        verify(argumentProcessorService).processArguments(any(), any(), any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量执行的UI动作结果处理
     */
    @Test
    @DisplayName("handleResult - UI动作结果处理")
    void testHandleResult_UIActionProcessing() {
        // Given
        Map<String, Object> uiActionResult = new HashMap<>();
        uiActionResult.put("action", "WebAction");
        uiActionResult.put("url", "http://example.com/bulk");
        uiActionResult.put("title", "批量操作完成");

        RunResult runResult = new RunResult();
        runResult.setSuccess(true);
        runResult.setFunctionResult(uiActionResult);
        runResult.setReturnType("UIAction");

        // When
        ButtonExecutor.Result result = bulkFuncAction.handleResult(testUser, testArg, runResult);

        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isHasReturnValue(), "应有返回值");
        assertEquals("UIAction", result.getReturnType(), "返回类型应正确");
        assertEquals(uiActionResult, result.getReturnValue(), "返回值应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量执行的字符串结果处理
     */
    @Test
    @DisplayName("handleResult - 字符串结果处理")
    void testHandleResult_StringResultProcessing() {
        // Given
        RunResult runResult = new RunResult();
        runResult.setSuccess(true);
        runResult.setFunctionResult("批量操作完成，共处理3条数据");
        runResult.setReturnType("String");

        // When
        ButtonExecutor.Result result = bulkFuncAction.handleResult(testUser, testArg, runResult);

        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isHasReturnValue(), "应有返回值");
        assertEquals("String", result.getReturnType(), "返回类型应正确");
        assertEquals("批量操作完成，共处理3条数据", result.getReturnValue(), "返回值应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量执行的空结果处理
     */
    @Test
    @DisplayName("handleResult - 空结果处理")
    void testHandleResult_EmptyResultProcessing() {
        // Given
        RunResult runResult = new RunResult();
        runResult.setSuccess(true);
        runResult.setFunctionResult(null);
        runResult.setReturnType(null);

        // When
        ButtonExecutor.Result result = bulkFuncAction.handleResult(testUser, testArg, runResult);

        // Then
        assertNotNull(result, "结果不应为null");
        assertFalse(result.isHasReturnValue(), "应无返回值");
        assertNull(result.getReturnType(), "返回类型应为null");
        assertNull(result.getReturnValue(), "返回值应为null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量执行的混合成功失败场景
     */
    @Test
    @DisplayName("executeFunction - 混合成功失败场景")
    void testExecuteFunction_MixedSuccessFailureScenario() {
        // Given
        List<String> dataIds = Arrays.asList("success1", "fail1", "success2");
        Map<String, Object> functionArgMap = new HashMap<>();
        Map<String, List<IObjectData>> details = new HashMap<>();

        when(testArg.getDataIds()).thenReturn(dataIds);

        // 配置部分成功的批量执行结果
        BatchDataExecuteFunction.Result batchResult = new BatchDataExecuteFunction.Result();
        batchResult.setSuccess(false); // 整体标记为失败，因为有部分失败
        batchResult.setFunctionResult("部分数据处理失败");
        batchResult.setErrorInfo("数据fail1处理失败：权限不足");
        batchResult.setReturnType("PartialSuccess");

        when(functionLogicService.batchDataExecuteFunction(testUser, testFunction, dataIds, functionArgMap))
                .thenReturn(batchResult);

        // When
        RunResult result = bulkFuncAction.executeFunction(testArg, testContext, testFunction, functionArgMap, details);

        // Then
        assertNotNull(result, "结果不应为null");
        assertFalse(result.isSuccess(), "整体执行应标记为失败");
        assertEquals("部分数据处理失败", result.getFunctionResult(), "函数结果应正确");
        assertEquals("数据fail1处理失败：权限不足", result.getErrorInfo(), "错误信息应正确");
        assertEquals("PartialSuccess", result.getReturnType(), "返回类型应正确");

        // 验证批量执行方法被调用
        verify(functionLogicService).batchDataExecuteFunction(testUser, testFunction, dataIds, functionArgMap);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量执行的性能考虑场景
     */
    @Test
    @DisplayName("executeFunction - 大批量数据性能测试")
    void testExecuteFunction_LargeBatchPerformance() {
        // Given - 模拟大批量数据
        List<String> largeDataIds = new ArrayList<>();
        for (int i = 1; i <= 1000; i++) {
            largeDataIds.add("largeData" + i);
        }

        Map<String, Object> functionArgMap = new HashMap<>();
        functionArgMap.put("batchMode", "performance");
        Map<String, List<IObjectData>> details = new HashMap<>();

        when(testArg.getDataIds()).thenReturn(largeDataIds);

        // 配置大批量执行结果
        BatchDataExecuteFunction.Result batchResult = new BatchDataExecuteFunction.Result();
        batchResult.setSuccess(true);
        batchResult.setFunctionResult("大批量处理完成，共处理1000条数据");
        batchResult.setReturnType("BatchResult");

        when(functionLogicService.batchDataExecuteFunction(testUser, testFunction, largeDataIds, functionArgMap))
                .thenReturn(batchResult);

        // When
        long startTime = System.currentTimeMillis();
        RunResult result = bulkFuncAction.executeFunction(testArg, testContext, testFunction, functionArgMap, details);
        long endTime = System.currentTimeMillis();

        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isSuccess(), "大批量执行应成功");
        assertEquals("大批量处理完成，共处理1000条数据", result.getFunctionResult(), "函数结果应正确");
        assertEquals("BatchResult", result.getReturnType(), "返回类型应正确");

        // 性能断言 - 执行时间应该合理（这里只是示例，实际项目中可能需要更精确的性能测试）
        long executionTime = endTime - startTime;
        assertTrue(executionTime < 5000, "执行时间应在合理范围内（小于5秒）");

        // 验证批量执行方法被调用
        verify(functionLogicService).batchDataExecuteFunction(testUser, testFunction, largeDataIds, functionArgMap);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量执行的错误处理和恢复
     */
    @Test
    @DisplayName("executeFunction - 错误处理和恢复")
    void testExecuteFunction_ErrorHandlingAndRecovery() {
        // Given
        List<String> dataIds = Arrays.asList("data1", "data2");
        Map<String, Object> functionArgMap = new HashMap<>();
        Map<String, List<IObjectData>> details = new HashMap<>();

        when(testArg.getDataIds()).thenReturn(dataIds);

        // 配置批量执行抛出异常，然后恢复
        when(functionLogicService.batchDataExecuteFunction(testUser, testFunction, dataIds, functionArgMap))
                .thenThrow(new RuntimeException("网络连接失败"))
                .thenReturn(createSuccessfulBatchResult());

        // When & Then - 第一次调用应抛出异常
        assertThrows(RuntimeException.class, () -> {
            bulkFuncAction.executeFunction(testArg, testContext, testFunction, functionArgMap, details);
        }, "第一次调用应抛出异常");

        // 第二次调用应成功
        RunResult result = bulkFuncAction.executeFunction(testArg, testContext, testFunction, functionArgMap, details);
        assertNotNull(result, "恢复后的结果不应为null");
        assertTrue(result.isSuccess(), "恢复后的执行应成功");

        // 验证批量执行方法被调用了两次
        verify(functionLogicService, times(2)).batchDataExecuteFunction(testUser, testFunction, dataIds, functionArgMap);
    }

    /**
     * 创建成功的批量执行结果的辅助方法
     */
    private BatchDataExecuteFunction.Result createSuccessfulBatchResult() {
        BatchDataExecuteFunction.Result result = new BatchDataExecuteFunction.Result();
        result.setSuccess(true);
        result.setFunctionResult("恢复后执行成功");
        result.setReturnType("Success");
        return result;
    }
}
