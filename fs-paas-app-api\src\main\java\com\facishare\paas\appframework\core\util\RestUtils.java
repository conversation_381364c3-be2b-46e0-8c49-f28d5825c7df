package com.facishare.paas.appframework.core.util;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContext.Attributes;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.rest.CEPXHeader;
import com.facishare.paas.appframework.core.rest.InnerHeaders;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;

public class RestUtils {

    private static final Set<String> QUERY_PARAMETER_LIST = Sets.newHashSet(Attributes.TRIGGER_FLOW, Attributes.TRIGGER_WORK_FLOW,
            Attributes.SKIP_FUNCTION_ACTION, Attributes.SKIP_BUTTON_CONDITIONS, Attributes.SKIP_BASE_VALIDATE, "not_validate", "isSpecifyTime", "isSpecifyCreatedBy");

    public static Map<String, String> buildHeaders(User user, boolean needRequestId) {
        Map<String, String> headers = buildHeaders(user, null);
        if (needRequestId && !headers.containsKey(InnerHeaders.POST_ID)) {
            headers.put(InnerHeaders.POST_ID, UUID.randomUUID().toString());
        }
        return headers;
    }

    public static Map<String, String> buildHeaders(User user) {
        return buildHeaders(user, null);
    }

    public static Map<String, String> buildHeaders(String describeApiName) {
        User user = RequestContextManager.getContext() == null ? null : RequestContextManager.getContext().getUser();
        return buildHeaders(user, describeApiName);
    }

    public static Map<String, String> buildHeadersByObjectModule(String objectModule) {
        User user = RequestContextManager.getContext() == null ? null : RequestContextManager.getContext().getUser();
        return buildHeaders(user, null, objectModule);
    }

    public static Map<String, String> buildHeaders(User user, String describeApiName) {
        return buildHeaders(user, describeApiName, null);
    }

    public static Map<String, String> buildHeaders(User user, String describeApiName, String objectModule) {
        Map<String, String> headers = Maps.newHashMap();
        if (user != null) {
            headers.put(CEPXHeader.TENANT_ID.key(), user.getTenantId());
            //空值不传header
            if (StringUtils.isNotBlank(user.getUserId())) {
                headers.put(CEPXHeader.USER_ID.key(), user.getUserId());
                headers.put(InnerHeaders.USER_ID, user.getUserId());
            }
            headers.put(CEPXHeader.OUT_TENANT_ID.key(), user.getOutTenantId());
            headers.put(CEPXHeader.OUT_USER_ID.key(), user.getOutUserId());
            headers.put(CEPXHeader.UPSTREAM_OWNER_ID.key(), user.getUpstreamOwnerId());

            headers.put(InnerHeaders.TENANT_ID, user.getTenantId());
            headers.put(InnerHeaders.OUT_TENANT_ID, user.getOutTenantId());
            headers.put(InnerHeaders.OUT_USER_ID, user.getOutUserId());
            headers.put(InnerHeaders.UPSTREAM_OWNER_ID, user.getUpstreamOwnerId());
        }
        if (!Strings.isNullOrEmpty(describeApiName)) {
            headers.put(InnerHeaders.OBJECT_API_NAME, describeApiName);
        }
        if (!Strings.isNullOrEmpty(objectModule)) {
            headers.put(InnerHeaders.OBJECT_MODULE, objectModule);
        }
        RequestContext context = RequestContextManager.getContext();
        if (context != null) {
            if (!Strings.isNullOrEmpty(context.getAppId())) {
                headers.put(InnerHeaders.APP_ID, context.getAppId());
            }
            if (!Strings.isNullOrEmpty(context.getOutLinkType())) {
                headers.put(InnerHeaders.OUT_LINK_TYPE, context.getOutLinkType());
            }
            if (!Strings.isNullOrEmpty(context.getModelName())) {
                headers.put(InnerHeaders.MODEL_NAME, context.getModelName());
            }
            if (!Strings.isNullOrEmpty(context.getPeerHost())) {
                headers.put(InnerHeaders.PEER_HOST, context.getPeerHost());
            }
            if (!Strings.isNullOrEmpty(context.getPeerName())) {
                headers.put(InnerHeaders.PEER_NAME, context.getPeerName());
            }
            if (!Strings.isNullOrEmpty(context.getPeerDisplayName())) {
                headers.put(InnerHeaders.PEER_DISPLAY_NAME, RequestUtil.encode(context.getPeerDisplayName()));
            }
            if (!Strings.isNullOrEmpty(context.getPeerReason())) {
                headers.put(InnerHeaders.PEER_REASON, RequestUtil.encode(context.getPeerReason()));
            }
            if (context.getLang() != null) {
                headers.put(InnerHeaders.LOCALE, context.getLang().getValue());
                // headers.put(CEPXHeader.LOCALE.key(), context.getLang().getValue());
            }
            if (!Strings.isNullOrEmpty(context.getEventId())) {
                headers.put(InnerHeaders.EVENT_ID, context.getEventId());
            }
            if (!Strings.isNullOrEmpty(context.getEa())) {
                headers.put(InnerHeaders.ENTERPRISE_ACCOUNT, context.getEa());
            }
            if (!Strings.isNullOrEmpty(context.getBizId())) {
                headers.put(InnerHeaders.BIZ_ID, context.getBizId());
            }
            if (!Strings.isNullOrEmpty(context.getUpstreamOwnerId())) {
                headers.put(InnerHeaders.UPSTREAM_OWNER_ID, context.getUpstreamOwnerId());
            }
            if (!Strings.isNullOrEmpty(context.getOutIdentityType())) {
                headers.put(InnerHeaders.OUT_IDENTITY_TYPE, context.getOutIdentityType());
            }
            if (!Strings.isNullOrEmpty(context.getThirdAppId())) {
                headers.put(InnerHeaders.THIRD_APP_ID, context.getThirdAppId());
            }
            if (!Strings.isNullOrEmpty(context.getThirdUserId())) {
                headers.put(InnerHeaders.THIRD_USER_ID, context.getThirdUserId());
            }
            if (!Strings.isNullOrEmpty(context.getThirdType())) {
                headers.put(InnerHeaders.THIRD_TYPE, context.getThirdType());
            }
            String originalRequestSource = context.getAttribute(InnerHeaders.ORIGINAL_REQUEST_SOURCE);
            if (!Strings.isNullOrEmpty(originalRequestSource)) {
                headers.put(InnerHeaders.ORIGINAL_REQUEST_SOURCE, originalRequestSource);
            } else if (Objects.nonNull(context.getRequestSource())) {
                headers.put(InnerHeaders.ORIGINAL_REQUEST_SOURCE, context.getRequestSource().name());
            }
            if (!Strings.isNullOrEmpty(context.getClientInfo())) {
                headers.put(InnerHeaders.CLIENT_INFO, context.getClientInfo());
                headers.put(CEPXHeader.CLIENT_INFO.key(), context.getClientInfo());
            }
        }
        return headers;
    }

    public static Map<String, String> buildFlowHeaders(User user) {
        Map<String, String> headers = buildHeaders(user);
        headers.put("x-tenant-id", user.getTenantId());
        headers.put("x-user-id", user.getUserId());
        return headers;
    }

    public static Map<String, String> buildSendEmailHeaders(User user) {
        Map<String, String> headers = buildHeaders(user);
        headers.put("x-tenant-id", user.getTenantId());
        headers.put("x-user-id", user.getUserIdOrOutUserIdIfOutUser());
        return headers;
    }

    public static Map<String, String> buildQueryParams() {
        Map<String, String> queryParams = Maps.newHashMap();
        RequestContext context = RequestContextManager.getContext();
        if (context != null) {
            context.getAttributes().forEach((k, v) -> {
                if (QUERY_PARAMETER_LIST.contains(k) && Objects.nonNull(v)) {
                    queryParams.put(k.toString(), v.toString());
                }
            });
        }
        return queryParams;
    }

}
