package com.facishare.paas.appframework.button.dto;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SendEmailPojo DTO单元测试 - JUnit5版本
 * 
 * GenerateByAI
 * 
 * 测试SendEmailPojo邮件发送数据传输对象：
 * - 字段设置和获取
 * - 数据验证和约束
 * - JSON序列化和反序列化
 * - 边界条件和null值处理
 * - 邮件地址格式验证
 * 
 * 覆盖场景：
 * - 正常邮件数据设置
 * - 空值和null值处理
 * - 复杂收件人结构
 * - JSON数据转换
 * - 数据完整性验证
 */
@DisplayName("SendEmailPojo - 邮件发送DTO测试")
class SendEmailPojoTest {

    private Gson gson;
    private SendEmailPojo sendEmailPojo;

    @BeforeEach
    void setUp() {
        gson = new GsonBuilder().create();
        sendEmailPojo = new SendEmailPojo();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试基本字段的设置和获取
     */
    @Test
    @DisplayName("基本字段设置和获取测试")
    void testBasicFieldsSetterAndGetter() {
        // Given
        String sender = "<EMAIL>";
        String template = "email_template_001";

        // When
        sendEmailPojo.setSender(sender);
        sendEmailPojo.setTemplate(template);

        // Then
        assertEquals(sender, sendEmailPojo.getSender(), "发送者应正确设置");
        assertEquals(template, sendEmailPojo.getTemplate(), "模板应正确设置");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试邮件地址集合的设置和获取
     */
    @Test
    @DisplayName("邮件地址集合设置和获取测试")
    void testEmailAddressSetSetterAndGetter() {
        // Given
        Set<String> emailAddresses = new HashSet<>();
        emailAddresses.add("<EMAIL>");
        emailAddresses.add("<EMAIL>");
        emailAddresses.add("<EMAIL>");

        // When
        sendEmailPojo.setEmail_address(emailAddresses);

        // Then
        assertEquals(emailAddresses, sendEmailPojo.getEmail_address(), "邮件地址集合应正确设置");
        assertEquals(3, sendEmailPojo.getEmail_address().size(), "邮件地址数量应为3");
        assertTrue(sendEmailPojo.getEmail_address().contains("<EMAIL>"), 
                "应包含*****************");
        assertTrue(sendEmailPojo.getEmail_address().contains("<EMAIL>"), 
                "应包含*****************");
        assertTrue(sendEmailPojo.getEmail_address().contains("<EMAIL>"), 
                "应包含*****************");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试收件人映射的设置和获取
     */
    @Test
    @DisplayName("收件人映射设置和获取测试")
    void testRecipientsMapSetterAndGetter() {
        // Given
        Map<String, List<String>> recipients = new HashMap<>();
        recipients.put("to", Arrays.asList("<EMAIL>", "<EMAIL>"));
        recipients.put("cc", Arrays.asList("<EMAIL>"));
        recipients.put("bcc", Arrays.asList("<EMAIL>", "<EMAIL>"));

        // When
        sendEmailPojo.setRecipients(recipients);

        // Then
        assertEquals(recipients, sendEmailPojo.getRecipients(), "收件人映射应正确设置");
        assertEquals(3, sendEmailPojo.getRecipients().size(), "收件人类型数量应为3");
        
        List<String> toList = sendEmailPojo.getRecipients().get("to");
        assertEquals(2, toList.size(), "收件人数量应为2");
        assertTrue(toList.contains("<EMAIL>"), "应包含***************");
        assertTrue(toList.contains("<EMAIL>"), "应包含***************");
        
        List<String> ccList = sendEmailPojo.getRecipients().get("cc");
        assertEquals(1, ccList.size(), "抄送人数量应为1");
        assertTrue(ccList.contains("<EMAIL>"), "应包含***************");
        
        List<String> bccList = sendEmailPojo.getRecipients().get("bcc");
        assertEquals(2, bccList.size(), "密送人数量应为2");
        assertTrue(bccList.contains("<EMAIL>"), "应包含b***************");
        assertTrue(bccList.contains("<EMAIL>"), "应包含****************");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试null值处理
     */
    @Test
    @DisplayName("null值处理测试")
    void testNullValueHandling() {
        // When
        sendEmailPojo.setSender(null);
        sendEmailPojo.setTemplate(null);
        sendEmailPojo.setEmail_address(null);
        sendEmailPojo.setRecipients(null);

        // Then
        assertNull(sendEmailPojo.getSender(), "发送者应为null");
        assertNull(sendEmailPojo.getTemplate(), "模板应为null");
        assertNull(sendEmailPojo.getEmail_address(), "邮件地址集合应为null");
        assertNull(sendEmailPojo.getRecipients(), "收件人映射应为null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空集合处理
     */
    @Test
    @DisplayName("空集合处理测试")
    void testEmptyCollectionHandling() {
        // Given
        Set<String> emptyEmailSet = new HashSet<>();
        Map<String, List<String>> emptyRecipientsMap = new HashMap<>();

        // When
        sendEmailPojo.setEmail_address(emptyEmailSet);
        sendEmailPojo.setRecipients(emptyRecipientsMap);

        // Then
        assertNotNull(sendEmailPojo.getEmail_address(), "邮件地址集合不应为null");
        assertTrue(sendEmailPojo.getEmail_address().isEmpty(), "邮件地址集合应为空");
        
        assertNotNull(sendEmailPojo.getRecipients(), "收件人映射不应为null");
        assertTrue(sendEmailPojo.getRecipients().isEmpty(), "收件人映射应为空");
    }

    /**
     * GenerateByAI
     * 测试内容描述：参数化测试不同的邮件模板
     */
    @ParameterizedTest
    @ValueSource(strings = {
        "welcome_template", 
        "notification_template", 
        "reminder_template", 
        "report_template",
        "custom_template_123"
    })
    @DisplayName("不同邮件模板测试")
    void testDifferentEmailTemplates(String template) {
        // When
        sendEmailPojo.setTemplate(template);

        // Then
        assertEquals(template, sendEmailPojo.getTemplate(), "模板应正确设置");
        assertNotNull(sendEmailPojo.getTemplate(), "模板不应为null");
        assertFalse(sendEmailPojo.getTemplate().isEmpty(), "模板不应为空字符串");
    }

    /**
     * GenerateByAI
     * 测试内容描述：参数化测试不同的发送者邮箱格式
     */
    @ParameterizedTest
    @ValueSource(strings = {
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    })
    @DisplayName("不同发送者邮箱格式测试")
    void testDifferentSenderEmailFormats(String senderEmail) {
        // When
        sendEmailPojo.setSender(senderEmail);

        // Then
        assertEquals(senderEmail, sendEmailPojo.getSender(), "发送者邮箱应正确设置");
        assertTrue(senderEmail.contains("@"), "邮箱应包含@符号");
        assertTrue(senderEmail.contains("."), "邮箱应包含.符号");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试JSON序列化
     */
    @Test
    @DisplayName("JSON序列化测试")
    void testJsonSerialization() {
        // Given
        sendEmailPojo.setSender("<EMAIL>");
        sendEmailPojo.setTemplate("test_template");
        
        Set<String> emailAddresses = new HashSet<>();
        emailAddresses.add("<EMAIL>");
        emailAddresses.add("<EMAIL>");
        sendEmailPojo.setEmail_address(emailAddresses);
        
        Map<String, List<String>> recipients = new HashMap<>();
        recipients.put("to", Arrays.asList("<EMAIL>"));
        recipients.put("cc", Arrays.asList("<EMAIL>"));
        sendEmailPojo.setRecipients(recipients);

        // When
        String json = gson.toJson(sendEmailPojo);

        // Then
        assertNotNull(json, "JSON字符串不应为null");
        assertTrue(json.contains("<EMAIL>"), "JSON应包含发送者邮箱");
        assertTrue(json.contains("test_template"), "JSON应包含模板名称");
        assertTrue(json.contains("<EMAIL>"), "JSON应包含邮件地址");
        assertTrue(json.contains("<EMAIL>"), "JSON应包含收件人");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试JSON反序列化
     */
    @Test
    @DisplayName("JSON反序列化测试")
    void testJsonDeserialization() {
        // Given
        String json = "{\n" +
                "  \"sender\": \"<EMAIL>\",\n" +
                "  \"template\": \"test_template\",\n" +
                "  \"email_address\": [\"<EMAIL>\", \"<EMAIL>\"],\n" +
                "  \"recipients\": {\n" +
                "    \"to\": [\"<EMAIL>\"],\n" +
                "    \"cc\": [\"<EMAIL>\"]\n" +
                "  }\n" +
                "}";

        // When
        SendEmailPojo deserializedPojo = gson.fromJson(json, SendEmailPojo.class);

        // Then
        assertNotNull(deserializedPojo, "反序列化对象不应为null");
        assertEquals("<EMAIL>", deserializedPojo.getSender(), "发送者应正确");
        assertEquals("test_template", deserializedPojo.getTemplate(), "模板应正确");
        
        assertNotNull(deserializedPojo.getEmail_address(), "邮件地址集合不应为null");
        assertEquals(2, deserializedPojo.getEmail_address().size(), "邮件地址数量应为2");
        assertTrue(deserializedPojo.getEmail_address().contains("<EMAIL>"), 
                "应包含*****************");
        assertTrue(deserializedPojo.getEmail_address().contains("<EMAIL>"), 
                "应包含*****************");
        
        assertNotNull(deserializedPojo.getRecipients(), "收件人映射不应为null");
        assertEquals(2, deserializedPojo.getRecipients().size(), "收件人类型数量应为2");
        assertTrue(deserializedPojo.getRecipients().containsKey("to"), "应包含to键");
        assertTrue(deserializedPojo.getRecipients().containsKey("cc"), "应包含cc键");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试复杂收件人结构
     */
    @Test
    @DisplayName("复杂收件人结构测试")
    void testComplexRecipientsStructure() {
        // Given
        Map<String, List<String>> complexRecipients = new HashMap<>();
        complexRecipients.put("to", Arrays.asList(
                "<EMAIL>", 
                "<EMAIL>", 
                "<EMAIL>"
        ));
        complexRecipients.put("cc", Arrays.asList(
                "<EMAIL>", 
                "<EMAIL>"
        ));
        complexRecipients.put("bcc", Arrays.asList(
                "<EMAIL>"
        ));
        complexRecipients.put("reply-to", Arrays.asList(
                "<EMAIL>"
        ));

        // When
        sendEmailPojo.setRecipients(complexRecipients);

        // Then
        assertEquals(complexRecipients, sendEmailPojo.getRecipients(), "复杂收件人结构应正确设置");
        assertEquals(4, sendEmailPojo.getRecipients().size(), "收件人类型数量应为4");
        
        // 验证各类型收件人
        assertEquals(3, sendEmailPojo.getRecipients().get("to").size(), "主收件人数量应为3");
        assertEquals(2, sendEmailPojo.getRecipients().get("cc").size(), "抄送人数量应为2");
        assertEquals(1, sendEmailPojo.getRecipients().get("bcc").size(), "密送人数量应为1");
        assertEquals(1, sendEmailPojo.getRecipients().get("reply-to").size(), "回复地址数量应为1");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试数据完整性验证
     */
    @Test
    @DisplayName("数据完整性验证测试")
    void testDataIntegrityValidation() {
        // Given
        String sender = "<EMAIL>";
        String template = "notification_template";
        Set<String> emailAddresses = new HashSet<>();
        emailAddresses.add("<EMAIL>");
        emailAddresses.add("<EMAIL>");

        Map<String, List<String>> recipients = new HashMap<>();
        recipients.put("to", Arrays.asList("<EMAIL>", "<EMAIL>"));
        recipients.put("cc", Arrays.asList("<EMAIL>"));

        // When
        sendEmailPojo.setSender(sender);
        sendEmailPojo.setTemplate(template);
        sendEmailPojo.setEmail_address(emailAddresses);
        sendEmailPojo.setRecipients(recipients);

        // Then - 验证所有数据都正确设置
        assertEquals(sender, sendEmailPojo.getSender(), "发送者应正确");
        assertEquals(template, sendEmailPojo.getTemplate(), "模板应正确");
        assertEquals(emailAddresses, sendEmailPojo.getEmail_address(), "邮件地址集合应正确");
        assertEquals(recipients, sendEmailPojo.getRecipients(), "收件人映射应正确");
        
        // 验证数据一致性
        assertNotNull(sendEmailPojo.getSender(), "发送者不应为null");
        assertNotNull(sendEmailPojo.getTemplate(), "模板不应为null");
        assertNotNull(sendEmailPojo.getEmail_address(), "邮件地址集合不应为null");
        assertNotNull(sendEmailPojo.getRecipients(), "收件人映射不应为null");
        
        // 验证集合不为空
        assertFalse(sendEmailPojo.getEmail_address().isEmpty(), "邮件地址集合不应为空");
        assertFalse(sendEmailPojo.getRecipients().isEmpty(), "收件人映射不应为空");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试边界条件
     */
    @Test
    @DisplayName("边界条件测试")
    void testBoundaryConditions() {
        // Given
        String emptySender = "";
        String emptyTemplate = "";
        Set<String> singleEmailSet = new HashSet<>();
        singleEmailSet.add("<EMAIL>");

        Map<String, List<String>> singleRecipientMap = new HashMap<>();
        singleRecipientMap.put("to", Arrays.asList("<EMAIL>"));

        // When
        sendEmailPojo.setSender(emptySender);
        sendEmailPojo.setTemplate(emptyTemplate);
        sendEmailPojo.setEmail_address(singleEmailSet);
        sendEmailPojo.setRecipients(singleRecipientMap);

        // Then
        assertEquals("", sendEmailPojo.getSender(), "空发送者应正确设置");
        assertEquals("", sendEmailPojo.getTemplate(), "空模板应正确设置");
        assertEquals(1, sendEmailPojo.getEmail_address().size(), "单个邮件地址应正确设置");
        assertEquals(1, sendEmailPojo.getRecipients().size(), "单个收件人类型应正确设置");
        assertEquals(1, sendEmailPojo.getRecipients().get("to").size(), "单个收件人应正确设置");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象相等性
     */
    @Test
    @DisplayName("对象相等性测试")
    void testObjectEquality() {
        // Given
        SendEmailPojo pojo1 = new SendEmailPojo();
        pojo1.setSender("<EMAIL>");
        pojo1.setTemplate("template1");
        
        SendEmailPojo pojo2 = new SendEmailPojo();
        pojo2.setSender("<EMAIL>");
        pojo2.setTemplate("template1");

        // When & Then
        // 注意：由于使用了@Data注解，Lombok会自动生成equals和hashCode方法
        assertEquals(pojo1, pojo2, "相同数据的对象应相等");
        assertEquals(pojo1.hashCode(), pojo2.hashCode(), "相同数据的对象hashCode应相等");
        
        // 修改一个对象的数据
        pojo2.setTemplate("template2");
        assertNotEquals(pojo1, pojo2, "不同数据的对象应不相等");
    }
}
