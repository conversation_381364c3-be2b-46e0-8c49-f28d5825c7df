package com.facishare.paas.appframework.core.predef.action

import com.facishare.paas.appframework.core.model.ActionContext
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import spock.lang.Specification

/**
 * <AUTHOR> @date 2019/11/13 5:27 下午
 *
 */
class StandardAddDraftActionGroovyTest extends Specification {
    def draftJsonWithOutTitle = '''{"created_by":"1000","describe_api_name":"object_z87LW__c","master_draft_data":{"data_own_department":["1000"],"field_m0KDi__c":"12","name":"yuan的草稿1","object_describe_api_name":"object_z87LW__c","object_describe_id":"5d5a07b8a5083dd185da53a8","owner":["1000"],"record_type":"default__c"},"slave_draft_data":{}}'''
    def draftJsonWithTitle = '''{"created_by":"1000","describe_api_name":"object_z87LW__c","master_draft_data":{"data_own_department":["1000"],"field_m0KDi__c":"12","name":"yuan的草稿1","object_describe_api_name":"object_z87LW__c","object_describe_id":"5d5a07b8a5083dd185da53a8","owner":["1000"],"record_type":"default__c"},"slave_draft_data":{},"title":"草稿箱标题"}'''
    def tenantId = "whatever"
    def userId = "whatever"
    def apiName = "whatever"
    def actionCode = "whatever"
    def action = new StandardAddDraftAction()
    ServiceFacade serviceFacade = Mock(ServiceFacade)

    def setup() {
        def user = new User(tenantId, userId)
        def actionContext = new ActionContext(RequestContext.builder().user(user).tenantId(tenantId).build(), apiName, actionCode)
        action.serviceFacade = serviceFacade
        action.actionContext = actionContext
    }

    def "test save"() {
        expect:
        1 == 1
        /*
        given:
        def draft = Mock(IObjectDataDraft)

        when:
        action.save(draft)

        then:
        1 * serviceFacade.createDraft(_,_) >> draft
        */
    }

}
