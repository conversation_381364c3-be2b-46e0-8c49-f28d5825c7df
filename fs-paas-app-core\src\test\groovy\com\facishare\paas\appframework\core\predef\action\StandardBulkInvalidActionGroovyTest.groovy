package com.facishare.paas.appframework.core.predef.action


import com.facishare.paas.appframework.core.model.ActionContext
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import spock.lang.Specification

//import org.json.JSONObject

class StandardBulkInvalidActionGroovyTest extends Specification {

    def tenantId = "whatever"
    def userId = "whatever"
    def apiName = "whatever"
    def actionCode = "whatever"

    def service = Mock(ServiceFacade) {
        findObject(*_) >> Mock(IObjectDescribe)
        findObjectData(*_) >> Mock(IObjectData)
    }
    def arg = new StandardBulkInvalidAction.Arg()
    def user = new User(tenantId, userId)
    def actionContext = new ActionContext(RequestContext.builder().user(user).tenantId(tenantId).build(), apiName, actionCode)
    def action = new StandardBulkInvalidAction(serviceFacade: service, arg: arg, actionContext: actionContext, objectDescribeMap: Maps.newHashMap(), objectDataList: Lists.newArrayList())

    def "InitializeObjectDataList"() {
        expect:
        1 == 1
        /*
        setup:
        arg.setJson(json)
        action.getMasterAndDetailObjects(*_) >> [Mock(ObjectData)]

        when:
        action.initObjectDataList()

        then:
        action.objectDataList.size() == dataListSize
        action.objectDescribeMap.size() == mapSize

        where:
        json | dataListSize | mapSize
        //new JSONObject(["dataList": [["object_describe_id": "1", "object_describe_api_name": "1", "_id": "1", "tenant_id": "1"]]]).toString()                                                                                             | 1            | 1
        //new JSONObject(["dataList": [["object_describe_id": "1", "object_describe_api_name": "1", "_id": "1", "tenant_id": "1"], ["object_describe_id": "2", "object_describe_api_name": "2", "_id": "2", "tenant_id": "2"]]]).toString() | 2            | 2
        */
    }

    def "InitializeIllegalObjectDataList"() {
        expect:
        1 == 1
        /*
        setup:
        arg.setJson(json)
        action.getMasterAndDetailObjects(*_) >> [Mock(ObjectData)]

        when:
        action.initObjectDataList()

        then:
        thrown(ValidateException)

        where:
        json | placehloder
        null | 1
        ""   | 1
        "1"  | 1
        //new JSONObject(["a": "1"]).toString() | 1
        */

    }

    def "ArgHelper fromArg noExceptionThrown"() {
        expect:
        1 == 1
        /*
        when:
        arg.setJson(json)
        StandardBulkInvalidAction.Arg.fromJson(arg.getJson())
        then:
        noExceptionThrown()

        where:
        json                                                                                                       || placehloder
        null                                                                                                       || 1
        ""                                                                                                         || 1
        '''{"dataList":[{"object_describe_id":"5b84f10ba5083dafba78dba0",
"object_describe_api_name":"object_2w4CB__c","_id":"5ba4a1aea5ccd9212cc4a6fe","tenant_id":"71698"}]}''' || 1
        */
    }

}
