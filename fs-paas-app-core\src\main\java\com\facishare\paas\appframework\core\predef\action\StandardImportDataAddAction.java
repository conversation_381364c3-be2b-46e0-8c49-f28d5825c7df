package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.util.ImportExportExt;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.ImportTenantSetting;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.dao.DuplicateKeyException;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class StandardImportDataAddAction extends BaseImportDataAction {

    protected Map<String, List<ImportData>> importDataListMap = Maps.newHashMap();
    protected Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
    protected Map<String, List<ImportData>> validDataListMap = Maps.newHashMap();
    protected Map<String, List<IObjectData>> actualDataListMap = Maps.newHashMap();
    protected Map<String, List<IFieldDescribe>> validFieldListMap = Maps.newHashMap();

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.InsertImportData.getFunPrivilegeCodes();
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected Result doAct(Arg arg) {
        return super.doAct(arg);
    }

    @Override
    protected void initializeUniqueRule() {
        //不支持唯一性规则
    }

    @Override
    protected void initDuplicateRule() {
        //不支持查重
    }

    @Override
    protected void customInit(List<ImportData> dataList) {
        // 下游人员导入，同步负责人为上游对接人
        if (actionContext.getUser().isOutUser()) {
            syncOwnerFromUpstreamOwnerWhenOutUser();
        }
        // 公共对象2个预制字段赋值
        if (objectDescribe.isPublicObject()) {
            initPublicDataFlag();
        }
        for (Map.Entry<String, List<ImportData>> entry : importDataListMap.entrySet()) {
            String describeApiName = entry.getKey();
            IObjectDescribe describe = describeMap.get(describeApiName);
            List<ImportData> importDataList = entry.getValue();
            setDefaultRecordType(importDataList, describe);
        }
    }

    private void initPublicDataFlag() {
        importDataListMap.values().forEach(dataList -> {
            if (CollectionUtils.empty(dataList)) {
                return;
            }
            dataList.forEach(x -> ObjectDataExt.of(x.getData()).initPublicDataFlagBeforeCreate(actionContext.getUser()));
        });
    }

    protected void syncOwnerFromUpstreamOwnerWhenOutUser() {
        importDataListMap.values().forEach(dataList -> {
            if (CollectionUtils.empty(dataList)) {
                return;
            }
            dataList.forEach(x -> ObjectDataExt.of(x.getData()).setDataOwner(actionContext.getUser()));
        });
    }

    @Override
    protected List<ImportData> customConvertLabelToApiName(List<ObjectDataDocument> sourceDataList, IObjectDescribe describe) {
        //master data label convert apiName
        MasterInfo masterInfo = arg.getMasterInfo();
        if (Objects.isNull(masterInfo)) {
            log.warn("customConvertLabelToApiName master object is null");
            return Lists.newArrayList();
        }
        List<ImportData> importData = super.customConvertLabelToApiName(masterInfo.getData(), describeMap.get(masterInfo.getApiName()));
        if (CollectionUtils.notEmpty(importData)) {
            importDataListMap.put(masterInfo.getApiName(), importData);
        }
        //detail data label convert apiName
        List<DetailInfo> detailInfos = arg.getDetailInfo();
        if (CollectionUtils.empty(detailInfos)) {
            log.warn("customConvertLabelToApiName detail object is null");
            return Lists.newArrayList();
        }
        for (DetailInfo detailInfo : detailInfos) {
            if (Objects.nonNull(detailInfo) && CollectionUtils.notEmpty(detailInfo.getDataList())) {
                List<ImportData> detailImportDataList = super.customConvertLabelToApiName(detailInfo.getDataList(), describeMap.get(detailInfo.getApiName()));
                importDataListMap.put(detailInfo.getApiName(), detailImportDataList);
            }
        }
        return Lists.newArrayList();
    }

    @Override
    public void initDescribeMap() {
        describeMap.put(objectDescribe.getApiName(), objectDescribe);
        List<DetailInfo> detailInfos = arg.getDetailInfo();
        if (CollectionUtils.notEmpty(detailInfos)) {
            List<String> detailApiNames = detailInfos.stream().map(BaseInfo::getApiName).collect(Collectors.toList());
            Map<String, IObjectDescribe> detailDescribes = serviceFacade.findObjects(actionContext.getTenantId(), detailApiNames);
            if (CollectionUtils.empty(detailDescribes)) {
                return;
            }
            detailDescribes.values().forEach(x -> ObjectDescribeExt.of(x).getFieldDescribeSilently(IObjectData.RECORD_TYPE).ifPresent(f -> f.setRequired(Boolean.TRUE)));
            describeMap.putAll(detailDescribes);
        }
    }

    @Override
    protected void validateMultiLang(List<ImportData> dataList, IObjectDescribe describe) {
        importDataListMap.forEach((describeApiName, importDataList) -> {
            if (CollectionUtils.notEmpty(importDataList)) {
                super.validateMultiLang(importDataList, describeMap.get(describeApiName));
            }
        });
    }

    @Override
    protected void convertFields(List<ImportData> dataList, IObjectDescribe objectDescribe) {
        for (Map.Entry<String, List<ImportData>> entry : importDataListMap.entrySet()) {
            String describeApiName = entry.getKey();
            IObjectDescribe describe = describeMap.get(describeApiName);
            List<ImportData> importDataList = entry.getValue();
            super.convertFields(importDataList, describe);
            super.validateRelatedObject(arg.getMatchingType(), describe, importDataList);
        }
    }

    @Override
    protected void validateRelatedObject(Integer matchingType) {
    }

    @Override
    protected boolean customFilterHeader(String fieldApiName, IObjectDescribe describe) {
        if (Strings.isNullOrEmpty(fieldApiName)) {
            return true;
        }
        return getValidImportFields(describe).stream().noneMatch(f -> fieldApiName.equals(f.getApiName()));
    }

    private List<IFieldDescribe> getValidImportFields(IObjectDescribe describe) {
        if (CollectionUtils.empty(validFieldListMap) || !validFieldListMap.containsKey(describe.getApiName())) {
            List<IFieldDescribe> templateField = infraServiceFacade.getTemplateField(actionContext.getUser(), describe);
            List<IFieldDescribe> fieldDescribes = CollectionUtils.empty(templateField) ? Collections.emptyList() : templateField;
            validFieldListMap.put(describe.getApiName(), fieldDescribes);
            return fieldDescribes;
        }
        return validFieldListMap.get(describe.getApiName());
    }


    @Override
    protected void validateField(IObjectDescribe objectDescribe, List<ImportData> dataList) {
        for (Map.Entry<String, List<ImportData>> entry : importDataListMap.entrySet()) {
            String describeApiName = entry.getKey();
            IObjectDescribe describe = describeMap.get(describeApiName);
            List<ImportData> importDataList = entry.getValue();
            super.validateField(describe, importDataList);
        }
    }

    @Override
    protected List<IObjectData> importData(List<IObjectData> validDataList) {
        return Lists.newArrayList();
    }

    @Override
    protected void customValidate(List<ImportData> list) {
    }

    @Override
    protected Map<IFieldDescribe, List<String>> getFieldDefObjMap(ObjectDescribeExt objectDescribeExt, List<ImportData> dataList) {
        Map<IFieldDescribe, List<String>> fieldDefObjMap = super.getFieldDefObjMap(objectDescribeExt, dataList);
        //导入主从对象,不需要处理主从关系字段,新建会自动处理
        if (isMasterDetailImport()) {
            fieldDefObjMap.entrySet().removeIf(x -> Objects.equals(x.getKey().getType(), IFieldType.MASTER_DETAIL));
        }
        return fieldDefObjMap;
    }

    @Override
    protected void customDefaultValue(List<IObjectData> validList) {
    }

    @Override
    protected boolean skipFunction() {
        return true;
    }

    @Override
    protected void importDataByAddAction() {
        handleErrorBeforeAdd(importDataListMap, allErrorList);
        validDataListMap = filterValidDataList(importDataListMap, allErrorList);
        if (CollectionUtils.notEmpty(validDataListMap)) {
            importLogMessageBuilder.start("doImportData");
            actualDataListMap = doImportData(validDataListMap);
            stopWatch.lap("doImportData");
            importLogMessageBuilder.end("doImportData");
        }
    }

    private void handleErrorBeforeAdd(Map<String, List<ImportData>> importDataListMap, List<ImportError> allErrorList) {
        log.info("handleErrorBeforeAdd importDataListSize:{}", importDataListMap.size());
        if (importDataListMap.size() <= 1) {
            return;
        }
        log.info("handleErrorBeforeAdd allErrorList:{}", allErrorList.size());
        if (CollectionUtils.empty(allErrorList)) {
            return;
        }
        List<ImportError> masterErrors = allErrorList.stream()
                .filter(x -> Objects.equals(x.getObjectApiName(), objectDescribe.getApiName()))
                .collect(Collectors.toList());

        List<ImportError> errorList = Lists.newArrayList();
        //从同步主的错误信息
        if (CollectionUtils.notEmpty(masterErrors)) {
            importDataListMap.entrySet().stream().filter(x -> !Objects.equals(x.getKey(), objectDescribe.getApiName())).forEach(detailDataList -> {
                syncError(detailDataList, masterErrors, errorList);
            });
        }

        //主同步从的错误
        List<ImportError> detailError = allErrorList.stream()
                .filter(x -> !Objects.equals(x.getObjectApiName(), objectDescribe.getApiName()))
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(detailError)) {
            for (Map.Entry<String, List<ImportData>> entry : importDataListMap.entrySet()) {
                if (CollectionUtils.empty(entry.getValue())) {
                    continue;
                }
                if (Objects.equals(entry.getKey(), objectDescribe.getApiName())) {
                    syncError(entry, detailError, errorList);
                } else {
                    fillDetailErrorToCorrectData(entry, detailError, errorList);
                }
            }
        }

        mergeErrorList(errorList);
    }

    private void fillDetailErrorToCorrectData(Map.Entry<String, List<ImportData>> detailDataMap, List<ImportError> detailError, List<ImportError> errorList) {
        if (Objects.isNull(detailDataMap) || CollectionUtils.empty(detailDataMap.getValue()) || CollectionUtils.empty(detailError)) {
            return;
        }
        List<Integer> errorRowNoList = detailError.stream()
                .filter(x -> Objects.equals(x.getObjectApiName(), detailDataMap.getKey()))
                .map(ImportError::getRowNo)
                .collect(Collectors.toList());
        detailDataMap.getValue().stream().filter(x -> !errorRowNoList.contains(x.getRowNo())).forEach(x -> {
            ImportError error = ImportError.builder()
                    .objectApiName(detailDataMap.getKey())
                    .rowNo(x.getRowNo())
                    .errorMessage(I18NExt.getOrDefault(I18NKey.IMPORT_ADD_DATA_CORRECT, "本条数据转换正确，请修改其他主从数据的错误后导入"))// ignoreI18n
                    .build();
            errorList.add(error);
        });
    }

    private static void syncError(Map.Entry<String, List<ImportData>> dataMap, List<ImportError> errors, List<ImportError> errorList) {
        log.info("handleErrorBeforeAdd describeApiName:{},errorsSize:{},errorList:{}", dataMap.getKey(), errors.size(), errorList.size());
        List<ImportData> importDataList = dataMap.getValue();
        if (CollectionUtils.empty(importDataList)) {
            return;
        }
        for (ImportData importData : importDataList) {
            errors.forEach(detail -> {
                ImportError error = ImportError.builder()
                        .rowNo(importData.getRowNo())
                        .errorMessage(detail.getErrorMessage())
                        .objectApiName(dataMap.getKey())
                        .build();
                errorList.add(error);
            });
        }
    }

    @Override
    protected void generateResult(Result result) {
        ImportResultValue resultValue = new ImportResultValue();
        if (CollectionUtils.notEmpty(actualDataListMap)) {
            int count = actualDataListMap.values().stream().mapToInt(Collection::size).sum();
            log.info("StandardImportDataAddAction Import data size:{}", count);
            resultValue.setImportSucceedCount(count);
        }
        //所有的错误的数据结果
        resultValue.setRowErrorList(allErrorList);
        result.setValue(resultValue);
    }

    private Map<String, List<IObjectData>> doImportData(Map<String, List<ImportData>> validDataListMap) {
        Map<String, List<IObjectData>> result = Maps.newHashMap();
        List<ImportData> masterDataList = validDataListMap.get(objectDescribe.getApiName());
        if (CollectionUtils.empty(masterDataList)) {
            return result;
        }
        for (ImportData masterData : masterDataList) {
            try {
                BaseObjectSaveAction.Result saveResult = triggerAddAction(masterData.getData(), validDataListMap);
                ObjectDataDocument objectData = saveResult.getObjectData();
                result.putAll(ObjectDataExt.groupByDescribeApiName(Lists.newArrayList(objectData.toObjectData())));
                Map<String, List<ObjectDataDocument>> details = saveResult.getDetails();
                if (CollectionUtils.notEmpty(details)) {
                    result.putAll(ObjectDataDocument.ofDataMap(details));
                }
            } catch (AppBusinessException exception) {
                log.warn("AppBusinessException in doImportData of StandardImportDataAddAction,tenantId:{}, arg:{}",
                        actionContext.getTenantId(), arg, exception);
                String message = exception.getMessage();
                handleTriggerError(masterData, validDataListMap, message);
            } catch (Exception e) {
                log.error("Unexpected Exception in doImportData of StandardImportDataAddAction,tenantId:{}, arg:{}",
                        actionContext.getTenantId(), arg, e);
                Throwable rootCause = ExceptionUtils.getRootCause(e);
                String error;
                if (rootCause instanceof DuplicateKeyException) {
                    error = I18NExt.text(I18NKey.DO_NOT_INPUT_DUPLICATE_PRODUCT);
                } else {
                    error = I18NExt.text(I18NKey.UNKNOWN_EXCEPTION);
                }
                handleTriggerError(masterData, validDataListMap, error);
            }
        }
        return result;
    }

    private void handleTriggerError(ImportData masterData, Map<String, List<ImportData>> validDataListMap, String errorMessage) {
        List<ImportError> errorList = Lists.newArrayList();
        ImportError importError = ImportError.builder()
                .errorMessage(errorMessage)
                .rowNo(masterData.getRowNo())
                .objectApiName(objectDescribe.getApiName())
                .build();
        errorList.add(importError);
        validDataListMap.entrySet().stream()
                .filter(x -> !Objects.equals(x.getKey(), objectDescribe.getApiName()))
                .forEach(entry -> {
                    List<ImportData> detailDataList = entry.getValue();
                    if (CollectionUtils.empty(detailDataList)) {
                        return;
                    }
                    detailDataList.forEach(data -> {
                        ImportError detailError = ImportError.builder()
                                .errorMessage(errorMessage)
                                .rowNo(data.getRowNo())
                                .objectApiName(entry.getKey())
                                .build();
                        errorList.add(detailError);
                    });
                });
        mergeErrorList(errorList);
    }

    private BaseObjectSaveAction.Result triggerAddAction(IObjectData masterData, Map<String, List<ImportData>> validDataListMap) {
        BaseObjectSaveAction.Arg saveActionArg = new BaseObjectSaveAction.Arg();
        saveActionArg.setObjectData(ObjectDataDocument.of(masterData));
        Map<String, List<ObjectDataDocument>> detailDataList = validDataListMap.entrySet().stream()
                .filter(x -> !Objects.equals(x.getKey(), objectDescribe.getApiName()))
                .collect(Collectors.toMap(Map.Entry::getKey,
                        x -> x.getValue().stream().map(y -> ObjectDataDocument.of(y.getData())).collect(Collectors.toList()),
                        (x, y) -> x));
        if (CollectionUtils.notEmpty(detailDataList)) {
            saveActionArg.setDetails(detailDataList);
        }
        BaseObjectSaveAction.OptionInfo optionInfo = new BaseObjectSaveAction.OptionInfo();
        optionInfo.setFromImport(true);
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(actionContext.getTenantId(), objectDescribe.getApiName())
                && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FUZZY_SUPPORT_IMPORT_GRAY, actionContext.getTenantId())) {
            optionInfo.setSkipFuzzyRuleDuplicateSearch(ImportTenantSetting.FuzzyDuplicateImportStrategy.NON_BLOCK.equals(fuzzyDuplicateImportStrategy));
        } else {
            optionInfo.setSkipFuzzyRuleDuplicateSearch(true);
        }
        saveActionArg.setOptionInfo(optionInfo);
        RequestContext requestContext = RequestContextManager.getContext();
        //是否触发工作流
        if (arg.getIsWorkFlowEnabled() != null) {
            requestContext.setAttribute(RequestContext.Attributes.TRIGGER_WORK_FLOW, arg.getIsWorkFlowEnabled());
        }
        //是否触发审批流
        if (arg.getIsApprovalFlowEnabled() != null) {
            requestContext.setAttribute(RequestContext.Attributes.TRIGGER_FLOW, arg.getIsApprovalFlowEnabled());
        }
        ActionContext actionContext = ContextManager.buildActionContext(objectDescribe.getApiName(), StandardAction.Add.name());
        return serviceFacade.triggerAction(actionContext, saveActionArg, BaseObjectSaveAction.Result.class);
    }

    @Override
    protected void mergeErrorList(List<ImportError> errorList) {
        for (ImportError newError : errorList) {
            boolean isExist = false;
            for (ImportError error : allErrorList) {
                if (Objects.equals(error.getRowNo(), newError.getRowNo()) && Objects.equals(error.getObjectApiName(), newError.getObjectApiName())) {
                    isExist = true;
                    if (Objects.equals(error.getErrorMessage(), newError.getErrorMessage())) {
                        continue;
                    }
                    error.setErrorMessage(String.format("%s\n%s", error.getErrorMessage(), newError.getErrorMessage()));
                    break;
                }
            }
            if (!isExist) {
                allErrorList.add(newError);
            }
        }
    }

    /**
     * 需要保证data里面的object_describe_api_name是存在的
     */
    private Map<String, List<ImportData>> filterValidDataList(Map<String, List<ImportData>> validDataListMap, List<ImportError> allErrorList) {
        Map<String, List<ImportData>> result = Maps.newHashMap();
        for (Map.Entry<String, List<ImportData>> entry : validDataListMap.entrySet()) {
            List<ImportData> validDataList = Lists.newArrayList();
            String describeApiName = entry.getKey();
            List<ImportData> dataList = entry.getValue();
            for (ImportData importData : dataList) {
                boolean hasError = false;
                for (ImportError importError : allErrorList) {
                    if (Objects.equals(importData.getRowNo(), importError.getRowNo())
                            && Objects.equals(importData.getData().getDescribeApiName(), importError.getObjectApiName())) {
                        hasError = true;
                        break;
                    }
                }
                if (!hasError) {
                    validDataList.add(importData);
                }
            }
            if (CollectionUtils.empty(validDataList)) {
                continue;
            }
            result.put(describeApiName, validDataList);
        }
        return result;
    }

    /**
     * 只需要处理主对象的数据
     *
     * @param dataList
     */
    @Override
    protected void dealOuterOwner(List<ImportData> dataList) {
        List<ImportData> masterDataList = importDataListMap.get(objectDescribe.getApiName());
        if (CollectionUtils.empty(masterDataList)) {
            return;
        }
        super.dealOuterOwner(masterDataList);
    }

    @Override
    protected void fillOutTeamMember(List<ImportData> dataList) {
        handleOutTeamMember(dataList);
    }

    @Override
    protected void validateUniqueDataInDB() {
        List<ImportError> errorList = Lists.newArrayList();
        for (Map.Entry<String, List<ImportData>> importDataMap : importDataListMap.entrySet()) {
            for (ImportData importData : importDataMap.getValue()) {
                for (Map.Entry<String, Object> data : ObjectDataDocument.of(importData.getData()).entrySet()) {
                    String describeApiName = importDataMap.getKey();
                    IObjectDescribe describe = describeMap.get(describeApiName);
                    String fieldApiName = data.getKey();
                    Optional<IFieldDescribe> fieldDescribeSilently = ObjectDescribeExt.of(describe).getFieldDescribeSilently(fieldApiName);
                    fieldDescribeSilently.ifPresent(fieldDescribe -> {
                        String valueStr = getStringValue(importData.getData(), fieldDescribe);
                        if (fieldDescribe.isUnique() && !Strings.isNullOrEmpty(valueStr)) {
                            IObjectData validateUniqueData = buildValidateUniqueData(fieldDescribe, importData.getData());
                            if (!checkUniqueInDB(validateUniqueData, describe)) {
                                log.warn("data is not unique name:{}", importData.getData().getName());
                                //因为元数据是根据整条数据进行唯一性校验的，并非根据某个字段
                                //因此，只要校验出此条数据重复，就进行下一条数据的校验
                                ImportError error = ImportError.builder()
                                        .rowNo(importData.getRowNo())
                                        .objectApiName(describe.getApiName())
                                        .errorMessage(I18NExt.getOrDefault(I18NKey.CONTENT_NOT_UNIQUE, "{0}内容不唯一！", fieldDescribe.getLabel()))// ignoreI18n
                                        .build();
                                errorList.add(error);
                            }
                        }
                    });
                }
            }
        }
        mergeErrorList(errorList);
    }

    @Override
    protected List<IFieldDescribe> getValidImportFields() {
        return Lists.newArrayList();
    }

    @Override
    protected void validateUniqueDataInExcel() {
        for (Map.Entry<String, List<ImportData>> importDataMap : importDataListMap.entrySet()) {
            String describeApiName = importDataMap.getKey();
            IObjectDescribe describe = describeMap.get(describeApiName);
            List<ImportData> importDataList = importDataMap.getValue();
            validateUniqueDataListInExcel(describe, importDataList);
        }
    }

    private void validateUniqueDataListInExcel(IObjectDescribe objectDescribe, List<ImportData> dataList) {
        Map<String, Map<String, List<ImportData>>> uniqueMap = buildUniqueMap(objectDescribe, dataList);

        if (CollectionUtils.empty(uniqueMap)) {
            return;
        }

        List<ImportError> errorList = checkUnique(uniqueMap, objectDescribe);

        mergeErrorList(errorList);
    }

    private Map<String, Map<String, List<ImportData>>> buildUniqueMap(IObjectDescribe objectDescribe, List<ImportData> dataList) {
        Map<String, Map<String, List<ImportData>>> uniqueMap = Maps.newHashMap();
        if (CollectionUtils.empty(dataList)) {
            return uniqueMap;
        }
        for (ImportData importData : dataList) {
            Set<Map.Entry<String, Object>> entries = ObjectDataDocument.of(importData.getData()).entrySet();
            entries.forEach(entry -> {
                Optional<IFieldDescribe> fieldDescribeSilently = ObjectDescribeExt.of(objectDescribe).getFieldDescribeSilently(entry.getKey());
                fieldDescribeSilently.ifPresent(fieldDescribe -> {
                    // 处理多语字段
                    if (BooleanUtils.isTrue(fieldDescribe.getEnableMultiLang())) {
                        Map<String, Object> multiLangValue = ObjectDataExt.of(importData.getData()).getMultiLangValue(actionContext.getTenantId(), objectDescribe.getApiName(), fieldDescribe);
                        multiLangValue.forEach((key, value) -> {
                            String fieldValue = ObjectDataExt.formatValueInImport(value);
                            if (isCheckExcelUniqueField(fieldDescribe, fieldValue)) {
                                add2UniqueMap(uniqueMap, importData, key, fieldValue);
                            }
                        });
                    }
                    String fieldValue = getStringValue(importData.getData(), fieldDescribe);
                    if (isCheckExcelUniqueField(fieldDescribe, fieldValue)) {
                        add2UniqueMap(uniqueMap, importData, fieldDescribe.getApiName(), fieldValue);
                    }
                });
            });
        }
        return uniqueMap;
    }

    private void add2UniqueMap(Map<String, Map<String, List<ImportData>>> uniqueMap, ImportData importData, String fieldApiName, String fieldValue) {
        //添加到uniqueMap，判断在当前批次是否有重复的
        if (uniqueMap.containsKey(fieldApiName)) {
            Map<String, List<ImportData>> valueMap = uniqueMap.get(fieldApiName);
            if (valueMap.containsKey(fieldValue)) {
                valueMap.get(fieldValue).add(importData);
            } else {
                valueMap.put(fieldValue, Lists.newArrayList(importData));
            }
        } else {
            Map<String, List<ImportData>> valueMap = Maps.newHashMap();
            valueMap.put(fieldValue, Lists.newArrayList(importData));
            uniqueMap.put(fieldApiName, valueMap);
        }
    }

    private List<ImportError> checkUnique(Map<String, Map<String, List<ImportData>>> uniqueMap, IObjectDescribe objectDescribe) {
        List<ImportError> errorList = Lists.newArrayList();
        List<IFieldDescribe> fieldDescribes = ObjectDescribeExt.of(objectDescribe).getFieldDescribes();
        // 补充临时补充多语字段的描述
        ImportExportExt.supportMultiLangField(objectDescribe.getTenantId(), fieldDescribes, objectDescribe.getApiName());
        Map<String, IFieldDescribe> fieldDescribeMap = fieldDescribes.stream()
                .collect(Collectors.toMap(IFieldDescribe::getApiName, Function.identity()));
        Set<Map.Entry<String, Map<String, List<ImportData>>>> entrySet = uniqueMap.entrySet();
        for (Map.Entry<String, Map<String, List<ImportData>>> entry : entrySet) {
            Map<String, List<ImportData>> valueMap = entry.getValue();
            if (CollectionUtils.empty(valueMap)) {
                continue;
            }

            Set<Map.Entry<String, List<ImportData>>> valueEntrySet = valueMap.entrySet();
            for (Map.Entry<String, List<ImportData>> valueEntry : valueEntrySet) {
                List<ImportData> list = valueEntry.getValue();
                if (CollectionUtils.empty(list) || list.size() <= 1) {
                    continue;
                }

                //存在重复，在errorList中加入，在validList中去掉
                StringBuilder lineNo = new StringBuilder();
                for (ImportData importData : list) {
                    lineNo.append(importData.getRowNo()).append(",");
                }
                IFieldDescribe fieldDescribe = fieldDescribeMap.get(entry.getKey());
                String message = I18NExt.getOrDefault(I18NKey.ROW_VALUE_DUPLICATE, I18NKey.ROW_VALUE_DUPLICATE, lineNo.toString(), fieldDescribe.getLabel());
                for (ImportData importData : list) {
                    errorList.add(ImportError.builder()
                            .rowNo(importData.getRowNo())
                            .errorMessage(message)
                            .objectApiName(objectDescribe.getApiName())
                            .build());
                }
            }
        }
        return errorList;
    }


    @Override
    protected boolean checkUniqueInDB(IObjectData data, IObjectDescribe describe) {
        return infraServiceFacade.isUniqueCheck(data, describe, false);
    }

    /**
     * 构建需要校验的数据data
     * 只保留必要的字段和需要校验的字段
     * 防止一个唯一字段重复,导致别的没有重复的唯一字段也提示重复
     *
     * @return 待校验的数据
     */
    private IObjectData buildValidateUniqueData(IFieldDescribe fieldDescribe, IObjectData data) {
        IObjectData result = new ObjectData();
        result.setDescribeApiName(data.getDescribeApiName());
        result.setId(data.getId());
        result.setTenantId(data.getTenantId());
        Object fieldValue = data.get(fieldDescribe.getApiName());
        result.set(fieldDescribe.getApiName(), fieldValue);
        // 开启多语的字段,需要拷贝多语的配置
        if (BooleanUtils.isTrue(fieldDescribe.getEnableMultiLang())) {
            String multiLangExtraField = FieldDescribeExt.getMultiLangFieldFromExtraField(fieldDescribe.getApiName());
            Object value = data.get(multiLangExtraField);
            if (!ObjectDataExt.isValueEmpty(value)) {
                result.set(multiLangExtraField, value);
            }
        }
        // 单选\多选的其他选项,需要拷贝 __o 的数据
        if (FieldDescribeExt.isSelectField(fieldDescribe.getType()) && ObjectDataExt.hasOtherValue(fieldValue)) {
            String selectOtherField = FieldDescribeExt.getSelectOther(fieldDescribe.getApiName());
            Object value = data.get(selectOtherField);
            if (!ObjectDataExt.isValueEmpty(value)) {
                result.set(selectOtherField, value);
            }
        }
        return result;
    }

    @Override
    protected void startImportApprovalFlow() {
    }

    @Override
    protected void startImportWorkFlow(List<IObjectData> actualList) {
    }

    @Override
    protected void recordImportDataLog(List<IObjectData> actualList) {
        if (CollectionUtils.empty(actualDataListMap)) {
            return;
        }
        if (objectDescribeExt.isSlaveObject()) {
            return;
        }
        List<IObjectData> actualDataList = actualDataListMap.get(objectDescribe.getApiName());
        List<IObjectData> objectDataList = ObjectDataExt.copyList(actualDataList);
        objectDataList.forEach(x -> ObjectDataExt.of(x).setTeamMembers(Lists.newArrayList()));
        serviceFacade.logByActionType(actionContext.getUser(), EventType.ADD, ActionType.AddEmployee, objectDataList, actualDataList, objectDescribe);
    }

    private boolean isMasterDetailImport() {
        return CollectionUtils.notEmpty(arg.getDetailInfo()) && !ObjectDescribeExt.of(objectDescribe).isSlaveObject();
    }

    @Override
    protected void getPhoneNumberInfo(List<ImportData> dataList) {
    }

    @Override
    protected String getParentSelectOneValue(IObjectData data, String parentApiName) {
        return String.valueOf(data.get(parentApiName));
    }

    @Override
    protected IObjectData prepareData(ImportData importData) {
        return importData.getData();
    }

    @Override
    protected void validUniquenessRuleInDB() {

    }

    @Override
    protected IUdefFunction findFunction() {
        return null;
    }

    @Override
    protected Map<String, Set<String>> fillFieldValue(List<IObjectData> objectDataList) {
        return Collections.emptyMap();
    }

    @Override
    protected boolean removeOutTeamMember() {
        return false;
    }
}
