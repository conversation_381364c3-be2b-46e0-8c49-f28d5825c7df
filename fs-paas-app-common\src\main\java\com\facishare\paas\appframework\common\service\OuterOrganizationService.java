package com.facishare.paas.appframework.common.service;

import com.facishare.organization.adapter.api.model.organizationwithouter.OrganizationEmployee;
import com.facishare.paas.appframework.common.model.OuterDepartmentInfo;
import com.facishare.paas.appframework.common.service.dto.OuterEmployeeInfo;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.core.model.User;
import com.fxiaoke.enterpriserelation2.data.ErDepartmentSimpleData;
import com.fxiaoke.enterpriserelation2.data.PublicEmployeeObjOwnDepOrOrgData;
import com.fxiaoke.enterpriserelation2.result.OuterAccountVo;
import com.fxiaoke.enterpriserelation2.result.data.EmployeeSimpleData;
import com.fxiaoke.enterpriserelation2.result.data.RelationEmployeeIdInfoData;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 互联接口，获取外部人员或者企业信息
 *
 * <AUTHOR>
 * @date 2019/12/26 3:56 下午
 */
public interface OuterOrganizationService {
    /**
     * 批量获取外部人员信息
     *
     * @param tenantId   企业ID
     * @param outUserIds 外部人员ID
     * @return 外部人员信息
     */
    List<OrganizationEmployee> batchGetEmployee(String tenantId, Collection<String> outUserIds);

    List<ErDepartmentSimpleData> batchGetOutDepartment(User user, Collection<String> outDepartmentIds, QueryDeptInfoByDeptIds.DeptStatusEnum status);

    /**
     * 获取指定人员负责的和观察的部门下的人员（默认查询外部人员）
     */
    List<OrganizationEmployee> batchGetResponsibleEmployee(String tenantId, String userId, Collection<String> userIds);

    /**
     * 查询当前下游企业在上游企业的外部联系人
     *
     * @param tenantId    上游企业id
     * @param outTenantId 下游外部企业id
     * @return
     */
    List<User> getUpstreamPartnerContact(String tenantId, String outTenantId);

    Optional<User> getOwnerOutUserByOutTenant(User user, String outTenantId);

    /**
     * 批量查询下游企业的对接人信息
     *
     * @param user
     * @return 对接人信息
     */
    List<User> batchGetOutUsersByOutTenants(User user, List<String> outTenantIds);

    List<UserInfo> getOutUsersByName(User user, String describeApiName, List<Map<String, String>> employData);

    List<OuterAccountVo> listOutersByUpstreamTenantId(User user);

    /**
     * 根据下游人员名称，查询下游人员信息
     *
     * @param user  用户信息
     * @param names 人员名称
     * @return 下游人员具体信息
     */
    Map<String, List<OuterAccountVo>> listOuterOwnerByNames(User user, Collection<String> names);

    /**
     * 判断下游用户id是否为当前租户的下游对接人
     *
     * @param user         租户信息
     * @param outerUserIds 外部人员id
     * @return 人员id ： 是否下游对接人
     */
    Map<String, Boolean> isOuterUsersByTenantId(User user, Collection<String> outerUserIds);

    /**
     * 根据用户信息、外部人员信息查询外部归属部门和外部归属组织信息
     *
     * @param user            用户信息
     * @param outerAccountVos 外部企业id和外部负责人对象
     * @return
     */
    List<PublicEmployeeObjOwnDepOrOrgData> batchGetOuterOwnDepOrOrg(User user, List<OuterAccountVo> outerAccountVos);

    String getEAByOuterTenantId(User user, Long outerTenantId);

    Map<String, String> batchGetOuterTenantIdByEI(User user, Collection<String> eaList);

    /**
     * 获取对方/我方企业对接人中的负责人
     *
     * @param user
     * @param downstreamTenantId 伙伴企业帐号，为null时查询所有伙伴企业的对接人
     * @param queryType          查询类型，0：我方对接人，1：对方对接人
     * @return
     */
    List<EmployeeSimpleData> batchGetRelationOwnerByOutTenant(User user, String downstreamTenantId, Integer queryType);

    List<RelationEmployeeIdInfoData> batchGetOutUserInfoByEnterpriseAndNames(User user, Collection<String> names);

    /**
     * 根据部门ID查询互联的部门ID
     *
     * @param user    用户信息
     * @param deptIds 部门ID列表
     * @return 部门ID和互联部门ID的映射关系
     */
    Map<String, OuterDepartmentInfo> findErDepartmentIdsByDeptIds(User user, Collection<String> deptIds, QueryDeptInfoByDeptIds.DeptStatusEnum status);

    Map<String, String> findErDepartmentIdsByUserIds(User user, Collection<String> userIds);

    List<OuterEmployeeInfo> batchGetOutEmployeeInfoByName(User user, List<OuterEmployeeInfo> nameInfos, Integer status);

    /**
     * 批量根据外部部门名称查询外部部门信息
     *
     * @param user            当前用户
     * @param departmentInfos 部门信息列表
     * @param status          部门状态
     * @return 外部部门信息列表
     */
    List<OuterDepartmentInfo> batchGetOutDepartmentInfoByName(User user, List<OuterDepartmentInfo> departmentInfos, Integer status);
}
