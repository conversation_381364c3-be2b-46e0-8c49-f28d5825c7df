package com.facishare.paas.appframework.core.predef.action

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.facishare.enterprise.common.util.IdUtil
import com.facishare.paas.appframework.common.service.dto.OrganizationInfo
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds
import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.common.util.ObjectAction
import com.facishare.paas.appframework.core.exception.AcceptableValidateException
import com.facishare.paas.appframework.core.exception.RecordTypeNotFound
import com.facishare.paas.appframework.core.exception.UniqueRuleValidationException
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.domain.AddActionDomainPlugin
import com.facishare.paas.appframework.core.predef.facade.ApprovalFlowServiceFacade
import com.facishare.paas.appframework.core.predef.service.ParamsIdempotentService
import com.facishare.paas.appframework.core.predef.service.PartnerRemindOutUserService
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO
import com.facishare.paas.appframework.metadata.MultiCurrencyLogicService
import com.facishare.paas.appframework.metadata.ObjectLifeStatus
import com.facishare.paas.appframework.metadata.ObjectReferenceWrapper
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderConfig
import com.facishare.paas.appframework.metadata.dto.ConvertRuleDataContainer
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData
import com.facishare.paas.appframework.metadata.dto.UniqueRuleQuery
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException
import com.facishare.paas.appframework.metadata.state.MergeState
import com.facishare.paas.appframework.metadata.state.MergeStateContainer
import com.facishare.paas.appframework.metadata.state.MergeStateInfo
import com.facishare.paas.appframework.privilege.EnterpriseRelationServiceImpl
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.data.IUniqueRule
import com.facishare.paas.metadata.api.describe.*
import com.facishare.paas.metadata.impl.IRule
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.DepartmentFieldDescribe
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.paas.metadata.impl.describe.SignInFieldDescribe
import com.fxiaoke.bizconf.bean.ConfigPojo
import com.fxiaoke.enterpriserelation2.result.RelationDownstreamResult
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.release.GrayRule
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import org.bson.Document
import org.powermock.reflect.Whitebox
import org.redisson.api.RLock
import org.springframework.test.util.ReflectionTestUtils
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

/**
 * Created by liyiguang on 2017/7/19.
 */
class StandardAddActionGroovyTest extends Specification {

    def tenantId = "123"
    def userId = "321"
    def apiName = "whatever"
    def actionCode = "whatever"

    ServiceFacade serviceFacade
    InfraServiceFacade infraServiceFacade
    User user
    ActionContext actionContext
    StandardAddAction action

    BaseObjectSaveAction.Arg arg

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    def setup() {
        serviceFacade = Mock(ServiceFacade)
        infraServiceFacade = Mock(InfraServiceFacade)
        infraServiceFacade.getSpringBeanHolder() >> Stub(SpringBeanHolder)
        user = new User(tenantId, userId)
        actionContext = new ActionContext(RequestContext.builder().user(user).tenantId(tenantId).build(), apiName, actionCode)
        action = new StandardAddAction(
                serviceFacade: serviceFacade,
                infraServiceFacade: infraServiceFacade,
                actionContext: actionContext)

        serviceFacade.getBean(EnterpriseRelationServiceImpl.class) >> new EnterpriseRelationServiceImpl()
        serviceFacade.getBean(PartnerRemindOutUserService.class) >> new PartnerRemindOutUserService()


        arg = new BaseObjectSaveAction.Arg()
        arg.setObjectData(new ObjectDataDocument(JSON.parseObject(dataJson)))
        action.setArg(arg)
    }
    @Shared
    String describeJson = '''{"fields":{"returned_goods_amount":{"describe_api_name":"SalesOrderObj","return_type":"currency","description":"退货单金额(元)","is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"ReturnedGoodsInvoiceObj","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["normal"]}]},{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["in_change"]}]}],"define_type":"package","is_single":false,"index_name":"d_11","field_api_name":"order_id","max_length":14,"is_index":true,"is_active":true,"create_time":1562060912729,"count_type":"sum","count_field_api_name":"returned_goods_inv_amount","label":"退货单金额(元)","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"returned_goods_amount","count_field_type":"currency","_id":"5d3abb7e319d19982fcc968b","is_index_field":false,"config":{"edit":1,"enable":0,"attrs":{"wheres":0,"is_required":1,"label":1,"help_text":1,"decimal_places":1}},"round_mode":4,"status":"released"},"refund_amount":{"describe_api_name":"SalesOrderObj","return_type":"currency","description":"已退款金额（元）","is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"RefundObj","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["normal"]}]},{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["in_change"]}]}],"define_type":"package","is_single":false,"index_name":"d_12","field_api_name":"order_id","max_length":14,"is_index":true,"is_active":true,"create_time":1562060912729,"count_type":"sum","count_field_api_name":"refunded_amount","label":"已退款金额(元)","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"refund_amount","count_field_type":"currency","_id":"5d3abb7e319d19982fcc968c","is_index_field":false,"config":{"edit":1,"enable":0,"attrs":{"wheres":0,"is_required":1,"label":1,"help_text":1,"decimal_places":1}},"round_mode":4,"status":"released"},"opportunity_id":{"describe_api_name":"SalesOrderObj","description":"商机名称","is_unique":false,"type":"object_reference","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":2,"operator":"EQ","field_name":"account_id","field_values":["$account_id$"]}]}],"define_type":"package","cascade_parent_api_name":"account_id","is_single":false,"index_name":"s_11","is_index":true,"is_active":false,"create_time":*************,"label":"商机名称","target_api_name":"OpportunityObj","target_related_list_name":"opportunity_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"api_name":"opportunity_id","_id":"5da70441319d19799edf068c","is_index_field":false,"config":{"edit":1,"enable":1,"display":0,"attrs":{"target_related_list_label":1,"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"status":"new"},"new_opportunity_id":{"describe_api_name":"SalesOrderObj","description":"商机2.0","is_unique":false,"type":"object_reference","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":2,"operator":"EQ","field_name":"account_id","field_values":["$account_id$"]}]}],"define_type":"package","cascade_parent_api_name":"account_id","is_single":false,"index_name":"s_8","is_index":true,"is_active":true,"create_time":*************,"label":"商机2.0","target_api_name":"NewOpportunityObj","target_related_list_name":"new_opportunity_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"api_name":"new_opportunity_id","_id":"5da70519319d19799edf068d","is_index_field":false,"config":{"edit":1,"enable":1,"attrs":{"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"status":"new"},"lock_rule":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"锁定规则","is_unique":false,"default_value":"default_lock_rule","rules":[],"label":"锁定规则","type":"lock_rule","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"5d1b2870319d19c15d3f9c6d","is_extend":false,"is_index_field":false,"is_single":false,"index_name":"s_1","help_text":"锁定规则","status":"new"},"settle_type":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":false,"create_time":1562060912676,"description":"结算方式","is_unique":false,"label":"结算方式","type":"select_one","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"settle_type","options":[{"resource_bundle_key":"","label":"预付","value":"1","config":{"edit":0,"enable":0,"remove":0}},{"resource_bundle_key":"","label":"现付","value":"2","config":{"edit":0,"enable":0,"remove":0}},{"resource_bundle_key":"","label":"赊销","value":"3","config":{"edit":0,"enable":0,"remove":0}}],"define_type":"package","option_id":"ce581138cb14e66193091162d8e2ecc8","_id":"5d1b2870319d19c15d3f9c6e","is_index_field":false,"is_single":false,"config":{"add":0,"edit":1,"enable":0,"display":0,"attrs":{"is_required":0,"options":0,"label":1,"help_text":1}},"index_name":"s_2","status":"released"},"current_level":{"describe_api_name":"SalesOrderObj","default_is_expression":false,"description":"已审批节点数量","is_unique":false,"type":"number","decimal_places":0,"default_to_zero":true,"is_required":false,"define_type":"system","is_single":false,"index_name":"d_1","max_length":14,"is_index":false,"is_active":false,"create_time":1562060912676,"length":14,"default_value":"0","label":"已审批节点数量","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"current_level","_id":"5d1b2870319d19c15d3f9c6f","is_index_field":false,"config":{"edit":0,"enable":0,"display":0,"attrs":{"is_required":0,"help_text":1}},"round_mode":4,"status":"released"},"discount":{"describe_api_name":"SalesOrderObj","default_is_expression":false,"is_index":false,"is_active":true,"create_time":1562060912676,"pattern":"","description":"整单折扣","is_unique":false,"default_value":"100","label":"整单折扣","type":"percentile","is_abstract":null,"field_num":null,"default_to_zero":true,"is_need_convert":false,"is_required":false,"api_name":"discount","define_type":"package","_id":"5d1b2870319d19c15d3f9c70","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1}},"index_name":"d_2","status":"released"},"order_time":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912676,"description":"下单日期","is_unique":false,"label":"下单日期","time_zone":"GMT+8","type":"date","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":true,"api_name":"order_time","define_type":"package","date_format":"yyyy-MM-dd","_id":"5d1b2870319d19c15d3f9c71","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":0,"attrs":{"is_required":0,"default_value":1,"label":1,"help_text":1}},"index_name":"l_1","status":"released"},"receivable_amount":{"describe_api_name":"SalesOrderObj","expression_type":"js","return_type":"currency","description":"待回款金额（元）","is_unique":false,"type":"formula","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"package","is_single":false,"index_name":"d_3","max_length":14,"is_index":true,"expression":"$order_amount$+$refund_amount$-$payment_amount$-$returned_goods_amount$","is_active":true,"create_time":1562060912676,"label":"待回款金额（元）","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"receivable_amount","_id":"5d1b2870319d19c15d3f9c72","is_index_field":false,"config":{"edit":1,"enable":0,"attrs":{"is_required":1,"formula":1,"label":1,"help_text":1,"decimal_places":1}},"round_mode":4,"status":"released"},"logistics_status":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"发货状态","is_unique":false,"label":"发货状态","type":"select_one","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"logistics_status","options":[{"resource_bundle_key":"","label":"待发货","value":"1","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"部分发货","value":"2","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已发货","value":"3","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"部分收货","value":"4","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已收货","value":"5","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"package","option_id":"a7ad9d6143f2ea3a984dc74c3dade3fc","_id":"5d1b2870319d19c15d3f9c73","is_index_field":false,"is_single":false,"config":{"edit":0},"index_name":"s_3","status":"released"},"ship_to_id":{"describe_api_name":"SalesOrderObj","description":"收货人","is_unique":false,"type":"object_reference","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":2,"operator":"EQ","field_name":"account_id","field_values":["$account_id$"]}]}],"define_type":"package","cascade_parent_api_name":"account_id","is_single":false,"index_name":"s_4","is_index":true,"is_active":true,"create_time":*************,"label":"收货人","target_api_name":"ContactObj","target_related_list_name":"ship_to_salesorder_list","is_abstract":null,"field_num":null,"target_related_list_label":"收货人订单列表","action_on_target_delete":"cascade_delete","is_need_convert":false,"api_name":"ship_to_id","_id":"5d1b2870319d19c15d3f9c74","is_index_field":false,"config":{"edit":1,"enable":1,"attrs":{"target_related_list_label":1,"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"status":"released"},"order_status":{"describe_api_name":"SalesOrderObj","is_use_value":true,"description":"状态（原）","is_unique":false,"type":"select_one","is_required":false,"options":[{"resource_bundle_key":"","label":"确认中","value":"6","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已确认","value":"7","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已驳回","value":"8","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已撤回","value":"9","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已发货","value":"10","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"已收货","value":"11","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"作废","value":"99","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"system","option_id":"c5eec7d2d4e9a4a458a488e904ea0d84","is_single":false,"index_name":"s_5","is_index":true,"expression":"CASE($life_status$,'invalid','99','ineffective','8','under_review','6',CASE($logistics_status$,'3','10','5','11','7'))","is_active":true,"create_time":1562060912697,"label":"状态（原）","is_abstract":null,"field_num":null,"is_need_convert":false,"is_need_calculate":true,"api_name":"order_status","_id":"5d1b2870319d19c15d3f9c75","is_index_field":false,"config":{"edit":0,"enable":0,"display":1,"attrs":{"is_required":0,"help_text":1}},"status":"released"},"ship_to_add":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912698,"pattern":"","description":"收货地址","is_unique":false,"label":"收货地址","type":"text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"ship_to_add","define_type":"package","_id":"5d1b2870319d19c15d3f9c76","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1}},"index_name":"t_1","max_length":256,"status":"released"},"extend_obj_data_id":{"describe_api_name":"SalesOrderObj","default_is_expression":false,"pattern":"","description":"extend_obj_data_id","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"define_type":"system","is_extend":false,"is_single":false,"index_name":"t_2","max_length":256,"is_index":false,"is_active":true,"create_time":1562060912698,"default_value":"","label":"extend_obj_data_id","is_abstract":null,"field_num":null,"api_name":"extend_obj_data_id","_id":"5d1b2870319d19c15d3f9c77","is_index_field":false,"help_text":"","status":"released"},"life_status_before_invalid":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912698,"pattern":"","description":"作废前生命状态","is_unique":false,"label":"作废前生命状态","type":"text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"life_status_before_invalid","define_type":"package","_id":"5d1b2870319d19c15d3f9c78","is_extend":false,"is_index_field":false,"is_single":false,"index_name":"t_3","help_text":"作废前生命状态","max_length":256,"status":"new"},"order_amount":{"describe_api_name":"SalesOrderObj","default_is_expression":true,"description":"销售订单金额(元)","is_unique":false,"type":"currency","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"package","is_single":false,"index_name":"d_4","max_length":14,"is_index":true,"is_active":true,"create_time":1562060912698,"length":18,"default_value":"$product_amount$*$discount$","label":"销售订单金额(元)","currency_unit":"￥","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"order_amount","_id":"5d1b2870319d19c15d3f9c79","is_index_field":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1,"max_length":1,"decimal_places":1}},"round_mode":4,"status":"released"},"owner_department":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912698,"pattern":"","description":"负责人主属部门","is_unique":false,"label":"负责人主属部门","type":"text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"owner_department","define_type":"package","_id":"5d1b2870319d19c15d3f9c7a","is_index_field":false,"is_single":false,"index_name":"owner_dept","max_length":100,"status":"released"},"signature_attachment":{"describe_api_name":"SalesOrderObj","file_amount_limit":10,"is_index":true,"is_active":true,"create_time":1562060912698,"description":"电子签章附件","is_unique":false,"label":"电子签章附件","type":"file_attachment","is_abstract":null,"field_num":null,"file_size_limit":10485760,"is_required":false,"api_name":"signature_attachment","define_type":"package","_id":"5d1b2870319d19c15d3f9c7b","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":1,"attrs":{"is_required":0,"help_text":1}},"index_name":"a_1","support_file_types":[],"status":"released"},"plan_payment_amount":{"describe_api_name":"SalesOrderObj","return_type":"currency","description":"已计划回款金额","is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"PaymentPlanObj","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["normal"]}]}],"define_type":"package","is_single":false,"index_name":"d_5","field_api_name":"order_id","max_length":14,"is_index":true,"is_active":true,"create_time":1562060912698,"count_type":"sum","count_field_api_name":"plan_payment_amount","label":"已计划回款金额","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"plan_payment_amount","count_field_type":"currency","_id":"5d1b2870319d19c15d3f9c7c","is_index_field":false,"config":{"edit":1,"enable":1,"attrs":{"wheres":1,"is_required":0,"label":1,"help_text":1,"decimal_places":1}},"round_mode":4,"status":"released"},"lock_status":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912706,"description":"锁定状态","is_unique":false,"default_value":"0","label":"锁定状态","type":"select_one","is_abstract":null,"field_num":null,"is_required":false,"api_name":"lock_status","options":[{"label":"未锁定","value":"0","config":{"edit":1,"enable":1,"remove":1}},{"label":"锁定","value":"1","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"package","option_id":"f8f6e3b07ca18b590c9a3cf86e014576","_id":"5d1b2870319d19c15d3f9c7d","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"s_6","status":"new"},"resource":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"来源","is_unique":false,"label":"来源","type":"select_one","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"resource","options":[{"resource_bundle_key":"","label":"CRM","value":"0","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"订货通","value":"1","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"package","option_id":"38d27afa1afa05bfa3f1fbf6b1c74b60","_id":"5d1b2870319d19c15d3f9c7e","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"display":1,"attrs":{"is_required":0,"help_text":1}},"index_name":"s_7","status":"released"},"submit_time":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"提交时间","is_unique":false,"label":"提交时间","time_zone":"GMT+8","type":"date_time","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"submit_time","define_type":"package","date_format":"yyyy-MM-dd HH:mm","_id":"5d1b2870319d19c15d3f9c7f","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"attrs":{"is_required":1,"label":1,"help_text":1}},"index_name":"l_2","status":"released"},"quote_id":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"报价单","is_unique":false,"label":"报价单","target_api_name":"QuoteObj","type":"object_reference","target_related_list_name":"quote_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"is_required":false,"api_name":"quote_id","define_type":"package","_id":"5d1b2870319d19c15d3f9c81","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"index_name":"s_9","status":"new"},"payment_amount":{"describe_api_name":"SalesOrderObj","return_type":"currency","description":"已回款金额(元)","is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"OrderPaymentObj","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["normal"]}]}],"define_type":"package","is_single":false,"index_name":"d_6","field_api_name":"order_id","is_index":true,"is_active":true,"create_time":*************,"count_type":"sum","length":14,"count_field_api_name":"payment_amount","label":"已回款金额(元)","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"payment_amount","count_field_type":"currency","_id":"5d1b2870319d19c15d3f9c82","is_index_field":false,"config":{"edit":1,"enable":0,"attrs":{"wheres":0,"is_required":1,"label":1,"help_text":1,"decimal_places":1}},"round_mode":4,"status":"released"},"delivery_comment":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"pattern":"","description":"发货备注","is_unique":false,"label":"发货备注","type":"text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"delivery_comment","define_type":"package","_id":"5d1b2870319d19c15d3f9c83","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"t_4","max_length":1000,"status":"released"},"relevant_team":{"describe_api_name":"SalesOrderObj","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","is_unique":false,"define_type":"package","description":"成员员工","label":"成员员工","type":"employee","is_single":false,"index_name":"stringList_2","help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"is_unique":false,"define_type":"package","description":"成员角色","label":"成员角色","type":"select_one","index_name":"string_4","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"is_unique":false,"define_type":"package","description":"成员权限类型","label":"成员权限类型","type":"select_one","index_name":"string_5","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":*************,"is_unique":false,"description":"","label":"相关团队","type":"embedded_object_list","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"5d1b2870319d19c15d3f9c84","is_index_field":false,"is_single":false,"index_name":"s_10","help_text":"相关团队","status":"released"},"confirmed_receive_date":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"收货时间","is_unique":false,"label":"收货时间","time_zone":"GMT+8","type":"date_time","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"confirmed_receive_date","define_type":"package","date_format":"yyyy-MM-dd HH:mm","_id":"5d1b2870319d19c15d3f9c85","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"l_3","status":"released"},"delivery_date":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"交货日期","is_unique":false,"label":"交货日期","time_zone":"GMT+8","type":"date","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"delivery_date","define_type":"package","date_format":"yyyy-MM-dd","_id":"5d1b2870319d19c15d3f9c86","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1}},"index_name":"l_4","status":"released"},"price_book_id":{"describe_api_name":"SalesOrderObj","description":"价目表","is_unique":false,"type":"object_reference","is_required":false,"define_type":"package","cascade_parent_api_name":"account_id","is_single":false,"index_name":"s_12","is_index":true,"is_active":false,"create_time":*************,"label":"价目表","target_api_name":"PriceBookObj","target_related_list_name":"price_book_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"api_name":"price_book_id","_id":"5d1b2870319d19c15d3f9c88","is_index_field":false,"config":{"edit":1,"enable":0,"display":0,"attrs":{"target_related_list_label":1,"wheres":0,"is_required":1,"default_value":0,"label":1,"help_text":1}},"status":"new"},"name":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"prefix":"{yyyy}{mm}{dd}-","description":"销售订单编号","is_unique":true,"serial_number":6,"start_number":1,"label":"销售订单编号","type":"auto_number","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":true,"api_name":"name","define_type":"package","_id":"5b066c5f9e787b82577aebe4","postfix":"","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":0,"attrs":{"is_required":0,"label":1,"help_text":1}},"index_name":"name","status":"released"},"bill_money_to_confirm":{"describe_api_name":"SalesOrderObj","description":"待确认的开票金额","is_unique":false,"type":"currency","decimal_places":2,"is_required":false,"define_type":"system","is_single":false,"index_name":"d_7","max_length":14,"is_index":false,"is_active":true,"create_time":*************,"length":18,"label":"待确认的开票金额","currency_unit":"￥","is_abstract":true,"field_num":null,"is_need_convert":false,"api_name":"bill_money_to_confirm","_id":"5d1b2870319d19c15d3f9c89","is_index_field":false,"config":{"edit":0,"enable":0,"display":0,"attrs":{"is_required":0,"help_text":1}},"round_mode":4,"status":"released"},"delivered_amount_sum":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":false,"create_time":*************,"length":14,"description":"已发货金额","is_unique":false,"label":"已发货金额","type":"number","is_abstract":null,"decimal_places":2,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"delivered_amount_sum","define_type":"package","_id":"5d1b2870319d19c15d3f9c8a","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":0,"display":0,"attrs":{"is_required":0,"default_value":1,"label":1,"help_text":1}},"index_name":"d_8","round_mode":4,"status":"released"},"payment_money_to_confirm":{"describe_api_name":"SalesOrderObj","description":"待确认的回款金额","is_unique":false,"type":"currency","decimal_places":2,"is_required":false,"define_type":"system","is_single":false,"index_name":"d_9","max_length":14,"is_index":false,"is_active":true,"create_time":*************,"length":18,"label":"待确认的回款金额","currency_unit":"￥","is_abstract":true,"field_num":null,"is_need_convert":false,"api_name":"payment_money_to_confirm","_id":"5d1b2870319d19c15d3f9c8b","is_index_field":false,"config":{"edit":0,"enable":0,"display":0,"attrs":{"is_required":0,"help_text":1}},"round_mode":4,"status":"released"},"remark":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"pattern":"","description":"备注","is_unique":false,"label":"备注","type":"long_text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"remark","define_type":"package","_id":"5d1b2870319d19c15d3f9c8c","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1}},"index_name":"t_5","max_length":2000,"status":"released"},"promotion_id":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":false,"create_time":*************,"description":"促销活动","is_unique":false,"label":"促销活动","target_api_name":"PromotionObj","type":"object_reference","target_related_list_name":"promotion_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"is_required":false,"api_name":"promotion_id","define_type":"package","_id":"5d1b2870319d19c15d3f9c8d","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":0,"display":0,"attrs":{"target_related_list_label":1,"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"index_name":"s_13","status":"new"},"lock_user":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912717,"description":"加锁人","is_unique":false,"label":"加锁人","type":"employee","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"lock_user","define_type":"package","_id":"5d1b2870319d19c15d3f9c8e","is_extend":false,"is_index_field":false,"is_single":true,"index_name":"a_2","help_text":"加锁人","status":"new"},"invoice_amount":{"describe_api_name":"SalesOrderObj","return_type":"currency","description":"已开票金额(元)","is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"InvoiceApplicationObj","is_required":false,"wheres":[{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["normal"]}]},{"connector":"OR","filters":[{"value_type":0,"operator":"EQ","field_name":"life_status","field_values":["in_change"]}]}],"define_type":"package","is_single":false,"index_name":"d_10","field_api_name":"order_id","is_index":true,"is_active":true,"create_time":1562060912717,"count_type":"sum","length":0,"count_field_api_name":"invoice_applied_amount","label":"已开票金额(元)","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"invoice_amount","count_field_type":"currency","_id":"5d1b2870319d19c15d3f9c8f","is_index_field":false,"config":{"edit":1,"enable":0,"attrs":{"wheres":0,"is_required":1,"label":1,"help_text":1,"decimal_places":1}},"round_mode":4,"status":"released"},"shipping_warehouse_id":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":false,"create_time":1562060912717,"description":"订货仓库","is_unique":false,"label":"订货仓库","target_api_name":"WarehouseObj","type":"object_reference","target_related_list_name":"shipping_warehouse_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"is_required":false,"api_name":"shipping_warehouse_id","define_type":"package","_id":"5d1b2870319d19c15d3f9c90","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":0,"display":0,"attrs":{"target_related_list_label":1,"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"index_name":"s_14","status":"new"},"partner_id":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":false,"create_time":1562060912717,"description":"合作伙伴","is_unique":false,"label":"合作伙伴","target_api_name":"PartnerObj","type":"object_reference","target_related_list_name":"partner_salesorder_list","is_abstract":null,"field_num":null,"target_related_list_label":"订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"is_required":false,"api_name":"partner_id","define_type":"package","_id":"5d1b2870319d19c15d3f9c91","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"display":0,"attrs":{"target_related_list_label":1,"wheres":1,"is_required":1,"default_value":0,"label":1,"help_text":1}},"index_name":"s_15","status":"new"},"receipt_type":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912729,"pattern":"","description":"收款类型","is_unique":false,"label":"收款类型","type":"select_one","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"receipt_type","options":[{"resource_bundle_key":"","label":"保证金","value":"1","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"服务费","value":"2","config":{"edit":1,"enable":1,"remove":1}},{"resource_bundle_key":"","label":"贷款","value":"3","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"package","option_id":"cec07bca47160cb2dac1d64e3fb04751","_id":"5d1b2870319d19c15d3f9c92","is_index_field":false,"is_single":false,"config":{"add":1,"edit":1,"enable":1,"attrs":{"is_required":1,"options":1,"label":1,"help_text":1}},"index_name":"s_16","status":"released"},"out_resources":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":false,"create_time":1562060912737,"description":"外部来源","is_unique":false,"label":"外部来源","type":"select_one","is_abstract":null,"field_num":null,"is_required":false,"api_name":"out_resources","options":[{"not_usable":false,"label":"PRM","value":"partner","config":{}}],"define_type":"package","option_id":"37e59fdcc9cdb736d4c89da45675ec08","_id":"5d1b2870319d19c15d3f9c95","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"display":0,"attrs":{"options":0,"help_text":1}},"index_name":"s_17","status":"new"},"product_amount":{"describe_api_name":"SalesOrderObj","return_type":"currency","description":"产品合计","is_unique":false,"type":"count","decimal_places":2,"sub_object_describe_apiname":"SalesOrderProductObj","is_required":false,"define_type":"package","is_single":false,"index_name":"d_13","field_api_name":"order_id","max_length":14,"is_index":true,"is_active":true,"create_time":1562060912737,"count_type":"sum","count_field_api_name":"subtotal","label":"产品合计","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"product_amount","count_field_type":"currency","_id":"5d1b2870319d19c15d3f9c96","is_index_field":false,"config":{"edit":1,"enable":1,"attrs":{"wheres":1,"is_required":0,"label":1,"help_text":1,"max_length":1,"decimal_places":1}},"round_mode":4,"status":"released"},"owner":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912737,"length":8,"description":"负责人","is_unique":false,"label":"负责人","type":"employee","is_abstract":null,"decimal_places":0,"field_num":null,"is_need_convert":true,"is_required":true,"api_name":"owner","define_type":"package","_id":"5d1b2870319d19c15d3f9c97","is_index_field":false,"is_single":true,"config":{"edit":0,"enable":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"owner","round_mode":4,"status":"released"},"life_status":{"describe_api_name":"SalesOrderObj","description":"生命状态","is_unique":false,"type":"select_one","is_required":false,"options":[{"label":"未生效","value":"ineffective","config":{"edit":1,"enable":1,"remove":1}},{"label":"审核中","value":"under_review","config":{"edit":1,"enable":1,"remove":1}},{"label":"正常","value":"normal","config":{"edit":1,"enable":1,"remove":1}},{"label":"变更中","value":"in_change","config":{"edit":1,"enable":1,"remove":1}},{"label":"作废","value":"invalid","config":{"edit":1,"enable":1,"remove":1}}],"define_type":"package","option_id":"18de2c4eae8cf7a180ffb0f175010e6f","is_extend":false,"is_single":false,"index_name":"s_18","is_index":true,"is_active":true,"create_time":1562060912747,"default_value":"normal","label":"生命状态","is_abstract":null,"field_num":null,"is_need_convert":false,"api_name":"life_status","_id":"5d1b2870319d19c15d3f9c98","is_index_field":false,"config":{},"help_text":"生命状态","status":"released"},"is_user_define_work_flow":{"describe_api_name":"SalesOrderObj","is_active":true,"create_time":1562060912748,"description":"是否是自定义的工作流","is_unique":false,"default_value":false,"label":"是否是自定义的工作流","type":"true_or_false","is_abstract":true,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"is_user_define_work_flow","define_type":"system","_id":"5d1b2870319d19c15d3f9c99","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"display":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"b_1","status":"released"},"ship_to_tel":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":1562060912748,"pattern":"^[0-9+\\\\-;,]{0,100}$","description":"收货人电话","is_unique":false,"label":"收货人电话","type":"phone_number","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"ship_to_tel","define_type":"package","_id":"5d1b2870319d19c15d3f9c9a","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1}},"index_name":"p_1","max_length":100,"status":"released"},"work_flow_id":{"describe_api_name":"SalesOrderObj","is_index":false,"is_active":false,"create_time":1562060912748,"pattern":"","description":"自由流程","is_unique":false,"label":"自由流程","type":"text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"work_flow_id","define_type":"system","_id":"5d1b2870319d19c15d3f9c9b","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"display":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"t_6","max_length":100,"status":"released"},"record_type":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"record_type","is_unique":false,"label":"业务类型","type":"record_type","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":true,"api_name":"record_type","options":[{"is_active":true,"api_name":"default__c","description":"预设业务类型","label":"预设业务类型"},{"is_active":false,"api_name":"un_active__c","description":"预设业务类型","label":"预设业务类型"}],"define_type":"package","option_id":"504eae3cf17c3d7223be4a3e0e893ecb","_id":"5d1b2870319d19c15d3f9c9c","is_index_field":false,"is_single":false,"index_name":"r_type","config":{},"status":"released"},"account_id":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"客户名称","is_unique":false,"label":"客户名称","target_api_name":"AccountObj","type":"object_reference","target_related_list_name":"account_sales_order_list","is_abstract":null,"field_num":null,"target_related_list_label":"销售订单","action_on_target_delete":"cascade_delete","is_need_convert":false,"is_required":true,"api_name":"account_id","define_type":"package","_id":"5d1b2870319d19c15d3f9c9d","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":0,"attrs":{"target_related_list_label":1,"wheres":1,"is_required":0,"default_value":0,"label":1,"help_text":1}},"index_name":"s_19","status":"new"},"commision_info":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"pattern":"","description":"提成信息","is_unique":false,"label":"提成信息","type":"text","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"commision_info","define_type":"package","_id":"5d1b2870319d19c15d3f9c9e","is_index_field":false,"is_single":false,"config":{"edit":1,"enable":1,"attrs":{"is_required":1,"default_value":1,"label":1,"help_text":1}},"index_name":"t_7","max_length":100,"status":"released"},"confirmed_delivery_date":{"describe_api_name":"SalesOrderObj","is_index":true,"is_active":true,"create_time":*************,"description":"发货时间","is_unique":false,"label":"发货时间","time_zone":"GMT+8","type":"date_time","is_abstract":null,"field_num":null,"is_need_convert":false,"is_required":false,"api_name":"confirmed_delivery_date","define_type":"package","date_format":"yyyy-MM-dd HH:mm","_id":"5d1b2870319d19c15d3f9c9f","is_index_field":false,"is_single":false,"config":{"edit":0,"enable":0,"attrs":{"is_required":0,"help_text":1}},"index_name":"l_5","status":"released"},"_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"_id","api_name":"_id","description":"_id","status":"released","index_name":"_id","create_time":*************},"created_by":{"type":"employee","define_type":"system","is_index":true,"is_need_convert":true,"is_required":false,"is_unique":false,"is_single":true,"api_name":"created_by","status":"released","label":"创建人","is_active":true,"index_name":"crt_by","create_time":*************,"description":""},"last_modified_by":{"type":"employee","define_type":"system","is_index":true,"is_need_convert":true,"is_required":false,"is_unique":false,"is_single":true,"api_name":"last_modified_by","status":"released","is_active":true,"index_name":"md_by","label":"最后修改人","create_time":*************,"description":""},"package":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"package","api_name":"package","description":"package","status":"released","create_time":*************,"index_name":"pkg"},"tenant_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":true,"is_unique":false,"max_length":200,"pattern":"","label":"tenant_id","api_name":"tenant_id","description":"tenant_id","status":"released","create_time":*************,"index_name":"ei"},"object_describe_api_name":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":true,"is_unique":false,"max_length":200,"pattern":"","label":"object_describe_api_name","api_name":"object_describe_api_name","description":"object_describe_api_name","status":"released","index_name":"api_name","create_time":*************},"version":{"type":"number","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"round_mode":4,"length":8,"decimal_places":0,"label":"version","api_name":"version","description":"version","status":"released","index_name":"version","create_time":*************},"object_describe_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"object_describe_id","api_name":"object_describe_id","description":"object_describe_id","status":"released","index_name":"object_describe_id","create_time":*************},"create_time":{"type":"date_time","define_type":"system","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"time_zone":"","date_format":"yyyy-MM-dd HH:mm:ss","label":"创建时间","api_name":"create_time","description":"create_time","status":"released","index_name":"crt_time","create_time":*************},"last_modified_time":{"type":"date_time","define_type":"system","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"time_zone":"","date_format":"yyyy-MM-dd HH:mm:ss","label":"最后修改时间","api_name":"last_modified_time","description":"last_modified_time","status":"released","index_name":"md_time","create_time":*************},"is_deleted":{"type":"true_or_false","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"label":"is_deleted","api_name":"is_deleted","description":"is_deleted","default_value":false,"status":"released","index_name":"is_del","create_time":*************},"out_tenant_id":{"type":"text","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"max_length":200,"pattern":"","label":"外部企业","api_name":"out_tenant_id","description":"out_tenant_id","status":"released","index_name":"o_ei","create_time":*************,"config":{"display":0}},"out_owner":{"type":"employee","define_type":"system","is_index":false,"is_need_convert":false,"is_required":false,"is_unique":false,"is_single":true,"api_name":"out_owner","index_name":"o_owner","status":"released","label":"外部负责人","config":{"display":1},"create_time":*************,"description":""},"data_own_department":{"type":"department","define_type":"package","is_index":true,"is_need_convert":false,"is_required":false,"is_unique":false,"is_single":true,"api_name":"data_own_department","status":"released","label":"归属部门","is_active":true,"index_name":"data_owner_dept_id","create_time":*************,"description":""}},"actions":{"Lock":{"tenant_id":"-100","action_class":"LockAction","action_id":"5d1b2870319d19c15d3f9ca6","describe_id":"5b0689ca9e787b86896a1a24","action_code":"Lock","source_type":"java_spring","label":"加锁"},"ChangeOwner":{"tenant_id":"-100","action_class":"ChangeOwnerAction","action_id":"5d1b2870319d19c15d3f9ca5","describe_id":"5b0689ca9e787b86896a1a24","action_code":"ChangeOwner","source_type":"java_spring"},"Unlock":{"tenant_id":"-100","action_class":"UnlockAction","action_id":"5d1b2870319d19c15d3f9ca4","describe_id":"5b0689ca9e787b86896a1a24","action_code":"Unlock","source_type":"java_spring","label":"解锁"},"ConfirmDelivery":{"tenant_id":"-100","action_class":"ConfirmDeliveryAction","action_id":"5d1b2870319d19c15d3f9ca3","describe_id":"5b0689ca9e787b86896a1a24","action_code":"ConfirmDelivery","source_type":"java_spring"},"AddDeliveryNote":{"tenant_id":"-100","action_class":"AddDeliveryNoteAction","action_id":"5d1b2870319d19c15d3f9ca2","describe_id":"5b0689ca9e787b86896a1a24","action_code":"AddDeliveryNote","source_type":"java_spring","label":"创建发货单"},"AddTeamMember":{"tenant_id":"-100","action_class":"AddTeamMemberAction","action_id":"5d1b2870319d19c15d3f9ca1","describe_id":"5b0689ca9e787b86896a1a24","action_code":"AddTeamMember","source_type":"java_spring","label":"添加团队成员"},"ConfirmReceive":{"tenant_id":"-100","action_class":"ConfirmReceiveAction","action_id":"5d1b2870319d19c15d3f9ca0","describe_id":"5b0689ca9e787b86896a1a24","action_code":"ConfirmReceive","source_type":"java_spring"}},"index_version":1,"_id":"5b0689ca9e787b86896a1a24","tenant_id":"78586","is_udef":null,"api_name":"SalesOrderObj","created_by":"-1000","last_modified_by":"-1000","display_name":"销售订单","package":"CRM","record_type":null,"is_active":true,"icon_path":null,"version":11,"release_version":"6.4","plural_name":null,"define_type":"package","is_deleted":false,"last_modified_time":*************,"create_time":*************,"store_table_name":"biz_sales_order","module":null,"icon_index":null,"description":"","visible_scope":null}'''
    @Shared
    String dataJson = '''{"lock_rule":null,"discount":"100.0000","account_id__r":"jejej","order_time":*************,"receivable_amount":"100.00","logistics_status":"1","ship_to_id":null,"order_status":"7","ship_to_add":null,"extend_obj_data_id":null,"created_by__r":{"picAddr":null,"description":null,"dept":null,"supervisorId":null,"title":null,"modifyTime":null,"post":"","createTime":null,"phone":null,"name":"admin01","nickname":null,"tenantId":null,"id":"1001","position":null,"email":"","status":null},"life_status_before_invalid":null,"order_amount":"100.00","owner_department_id":"1000","price_book_id__relation_ids":"5d4157fca5083d7cdb50f4b4","owner_department":"研发部","signature_attachment":null,"plan_payment_amount":null,"lock_status":"0","package":"CRM","data_own_department__r":{"deptName":"研发部","leaderName":null,"leaderUserId":null,"deptId":"1000","parentId":"999999","status":0},"create_time":*************,"resource":"0","submit_time":*************,"new_opportunity_id":null,"quote_id":null,"payment_amount":"0.00","created_by":["1001"],"version":"5","delivery_comment":null,"relevant_team":[{"teamMemberEmployee":["1001"],"teamMemberRole":"1","teamMemberRoleList":["1"],"teamMemberPermissionType":"2"}],"confirmed_receive_date":null,"delivery_date":null,"data_own_department":["1000"],"object_describe_id":"5b0689ca9e787b86896a1a24","name":"20191017-000003","bill_money_to_confirm":"0","_id":"5da7dce52dc22b000137a982","payment_money_to_confirm":"0","data_own_department__l":[{"parentId":"999999","deptId":"1000","deptName":"研发部","status":0}],"tenant_id":"78586","remark":null,"invoice_amount":"0.00","lock_user":null,"is_deleted":false,"receipt_type":null,"returned_goods_amount":"0.00","object_describe_api_name":"SalesOrderObj","owner__l":[{"id":"1001","name":"admin01","email":"","post":""}],"refund_amount":"0.00","out_owner":null,"relevant_team__r":"admin01","owner__r":{"picAddr":null,"description":null,"dept":null,"supervisorId":null,"title":null,"modifyTime":null,"post":"","createTime":null,"phone":null,"name":"admin01","nickname":null,"tenantId":null,"id":"1001","position":null,"email":"","status":null},"product_amount":"100.00","owner":["1001"],"last_modified_time":1571282150477,"life_status":"normal","is_user_define_work_flow":null,"ship_to_tel":null,"last_modified_by__l":[{"id":"-10000","name":"系统"}],"created_by__l":[{"id":"1001","name":"admin01","email":"","post":""}],"last_modified_by":["-10000"],"out_tenant_id":null,"record_type":"default__c","last_modified_by__r":{"picAddr":null,"description":null,"dept":null,"supervisorId":null,"title":null,"modifyTime":null,"post":null,"createTime":null,"phone":null,"name":"系统","nickname":null,"tenantId":null,"id":"-10000","position":null,"email":null,"status":null},"account_id":"5d5b8cd47b8a5e0001349aff","account_id__relation_ids":"5d5b8cd47b8a5e0001349aff","order_by":null,"commision_info":null,"confirmed_delivery_date":null}'''


    def "test_doParamsIdempotent"() {
        given:
        ParamsIdempotentService paramsIdempotentService = Mock(ParamsIdempotentService)
        action.paramsIdempotentService = paramsIdempotentService

        and: "arg"
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg()

        and: "mock rlock"
        RLock paramsIdempotentLock = Mock(RLock)
        when:
        action.doParamsIdempotent(arg)
        then:
        1 * paramsIdempotentService.getLock(*_) >> paramsIdempotentLock
        1 * paramsIdempotentService.lock(*_) >> true
    }

    def "test_doParamsIdempotent_when_throw"() {
        given:
        ParamsIdempotentService paramsIdempotentService = Mock(ParamsIdempotentService)
        action.paramsIdempotentService = paramsIdempotentService

        and: "arg"
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg()

        and: "mock rlock"
        RLock paramsIdempotentLock = Mock(RLock)
        when:
        action.doParamsIdempotent(arg)
        then:
        1 * paramsIdempotentService.getLock(*_) >> paramsIdempotentLock
        1 * paramsIdempotentService.lock(*_) >> false
        thrown(AcceptableValidateException)
    }

    def "test_get_func_privilege_codes when fromImport is true"() {
        given:
        def arg = Mock(BaseObjectSaveAction.Arg)
        action.arg = arg
        when:
        def codes = action.getFuncPrivilegeCodes()
        then:
        1 * arg.fromImport() >> true
        codes == null || codes.isEmpty()
    }

    @Unroll
    def "test_get_func_privilege_codes_when_slave_object fromImport= #fromImport, fields= #fields"() {
        given:
        def describe = Mock(IObjectDescribe)
        def arg = Mock(BaseObjectSaveAction.Arg)
        action.arg = arg

        // 确保serviceFacade字段被正确设置，避免NullPointerException
        ReflectionTestUtils.setField(action, "serviceFacade", serviceFacade)

        when:
        def codes = action.getFuncPrivilegeCodes()
        then:
        1 * arg.fromImport() >> fromImport
        serviceFacade.findObject(actionContext.getTenantId(), actionContext.getObjectApiName()) >> describe
        serviceFacade.findObjectWithoutCopy(tenantId, apiName) >> describe
        1 * describe.getFieldDescribes() >> fields
        codes == expect
        where:
        fromImport | fields                                                           | expect
        false      | [Stub(MasterDetail)]                                             | ["Add"]
        false      | [Stub(MasterDetail), Stub(Number)]                               | ["Add"]
        null       | [Stub(MasterDetail)]                                             | ["Add"]
        null       | [Stub(MasterDetail), Stub(TrueOrFalse), Stub(Email), Stub(What)] | ["Add"]
    }

    @Unroll
    def "test_get_data_privilege_ids_change_order_object originalDescribeApiName=#originalDescribeApiName"() {
        def describe = Mock(IObjectDescribe)
        def arg = Mock(BaseObjectSaveAction.Arg)
        action.arg = arg
        ChangeOrderConfig.ChangeOrderConfigItem changeOrderConfigItem = new ChangeOrderConfig.ChangeOrderConfigItem(
                originalDescribeApiName, new GrayRule("white:*"), null, null, null, null, null, null, null, 10, null
        )
        ChangeOrderConfig.changeOrderOriginalDescribeGray = [:]
        ChangeOrderConfig.changeOrderOriginalDescribeGray[originalDescribeApiName] = changeOrderConfigItem
        when:
        def codes = action.getFuncPrivilegeCodes()
        then:
        1 * arg.fromImport() >> false
        serviceFacade.findObjectWithoutCopy(tenantId, apiName) >> describe
        serviceFacade.findObject(actionContext.getTenantId(), actionContext.getObjectApiName()) >> describe
        1 * describe.getFieldDescribes() >> [Stub(Number), Stub(TrueOrFalse), Stub(Email), Stub(What)]
        1 * describe.getOriginalDescribeApiName() >> originalDescribeApiName
        codes == expect
        where:
        originalDescribeApiName || expect
        ""                      || ["Add"]
        null                    || ["Add"]
        "123"                   || []
        "testChangeObj"         || []
    }

    def "test_check_uniqueness_rule"() {
        given:
        // actionContext
        actionContext = new ActionContext(RequestContext.builder().user(user)
                .tenantId(tenantId).peerName(peerName).build(), apiName, actionCode)
        action.actionContext = actionContext
        // uniqueRule
        IUniqueRule uniqueRule = new IUniqueRule()
        uniqueRule.setUseWhenCallOpenApi(useWhenCallOpenApi)
        uniqueRule.setEffective(effective)
        action.uniqueRule = uniqueRule
        // 描述
        action.objectDescribe = Mock(IObjectDescribe)
        IObjectData objectData = Mock(IObjectData)
        action.objectData = objectData
        when:
        action.checkUniquenessRule()
        then:
        findDuplicateData * serviceFacade.findDuplicateData(_, _, _, _) >> UniqueRuleQuery.Result.of(uniqueRuleResult)
        getIdNum * objectData.getId() >> dataId
        where:
        peerName    | useWhenCallOpenApi | effective | dataId | getIdNum | findDuplicateData | uniqueRuleResult
        "OpenAPI"   | true               | true      | "123"  | 1        | 1                 | [UniqueRuleQuery.UniqueRuleResult.of(0, Sets.newHashSet("123"))]
        "OpenAPI"   | true               | true      | "123"  | 0        | 1                 | []
        "SmartForm" | true               | true      | "123"  | 1        | 1                 | [UniqueRuleQuery.UniqueRuleResult.of(0, Sets.newHashSet("123"))]
        "SmartForm" | true               | true      | "123"  | 0        | 1                 | []


        ""          | true               | true      | "123"  | 0        | 0                 | []

        "SmartForm" | false              | true      | "123"  | 0        | 0                 | []
    }

    def "test_check_uniqueness_rule_notUseable"() {
        given:
        // actionContext
        actionContext = new ActionContext(RequestContext.builder().user(user)
                .tenantId(tenantId).peerName(peerName).build(), apiName, actionCode)
        action.actionContext = actionContext
        // uniqueRule
        IUniqueRule uniqueRule = new IUniqueRule()
        uniqueRule.setUseWhenCallOpenApi(useWhenCallOpenApi)
        uniqueRule.setEffective(effective)
        action.uniqueRule = uniqueRule
        // 描述
        action.objectDescribe = Mock(IObjectDescribe)
        IObjectData objectData = Mock(IObjectData)
        action.objectData = objectData
        when:
        action.checkUniquenessRule()
        then:
        thrown(ex)
        findDuplicateData * serviceFacade.findDuplicateData(_, _, _, _) >> UniqueRuleQuery.Result.of(uniqueRuleResult)
        getIdNum * objectData.getId() >> dataId
        where:
        peerName    | useWhenCallOpenApi | effective | dataId | getIdNum | findDuplicateData | ex                            | uniqueRuleResult
        "SmartForm" | true               | false     | "123"  | 0        | 0                 | MetaDataBusinessException     | []
        "SmartForm" | true               | true      | "123"  | 2        | 1                 | UniqueRuleValidationException | [UniqueRuleQuery.UniqueRuleResult.of(0, Sets.newHashSet("321"))]
    }

    def "test_preAction"() {

    }

    def masterDetail() {
        MasterDetail masterDetail = new MasterDetailFieldDescribe()
        masterDetail.setApiName("master_detail_field")
        return masterDetail
    }

    def createMainDeptInfoMap(String deptId) {
        def mainDeptInfo = new QueryDeptInfoByUserIds.MainDeptInfo()
        mainDeptInfo.setDeptId(deptId)
        return mainDeptInfo
    }

    def "after"() {
        given:
        def object = new ObjectData()
        action.objectData = object
        action.detailObjectData = [:]

        BaseObjectSaveAction.Result result = BaseObjectSaveAction.Result.builder()
                .objectData(ObjectDataDocument.of(new ObjectData()))
                .details(ObjectDataDocument.ofMap([:]))
                .isDuplicate(Boolean.FALSE)
                .build()
        and: "optionInfo"
        def optionInfo = new BaseObjectSaveAction.OptionInfo()
        optionInfo.fromTransform = true
        arg.setOptionInfo(optionInfo)

        and: "relatedObjectData"
        Map<String, List<SaveMasterAndDetailData.RelatedObjectData>> relatedObjectData =
                ["SalesOrderObj": [SaveMasterAndDetailData.RelatedObjectData.create("relatedFieldName", [object])]]
        action.relatedObjectData = relatedObjectData
        when:
        action.after(arg, result)
        then:
        noExceptionThrown()

    }

    def "test_postAction"() {

    }

    def "finallyDo"() {
        given:
        action.objectDescribe = Mock(IObjectDescribe)
        when:
        action.finallyDo()
        then:
        0 * serviceFacade.unLock(_, _, _)
    }

    @Unroll
    def "test_lock_and_unlock_unique_rule peerName=#peerName,useWhenCallOpenApi=#useWhenCallOpenApi"() {
        given:
        // actionContext
        actionContext = new ActionContext(RequestContext.builder().user(user)
                .tenantId(tenantId).peerName(peerName).build(), apiName, actionCode)
        action.actionContext = actionContext
        // uniqueRule
        IUniqueRule uniqueRule = new IUniqueRule()
        uniqueRule.setUseWhenCallOpenApi(useWhenCallOpenApi)
        action.uniqueRule = uniqueRule
        // 描述
        action.objectDescribe = Mock(IObjectDescribe)
        when:
        action.uniqueRuleLock()
        action.finallyDo()
        then:
        lockNum * infraServiceFacade.tryLock(_, _, _) >> uniqueLock
        unLockNum * infraServiceFacade.unlock(uniqueLock)
        where:
        peerName    | useWhenCallOpenApi | uniqueLock  | lockNum | unLockNum
        "OpenAPI"   | true               | Stub(RLock) | 1       | 1
//        "OpenAPI"   | true               | null        | 1       | 0
        "SmartForm" | true               | Stub(RLock) | 1       | 1
//        "SmartForm" | true               | null        | 1       | 0
        ""          | true               | _           | 0       | _
        "OpenAPI"   | false              | _           | 0       | _
    }

    @Unroll
    def "test_lock_and_unlock_unique_rule_when_throw_ValidateException peerName=#peerName,useWhenCallOpenApi=#useWhenCallOpenApi"() {
        given:
        // actionContext
        actionContext = new ActionContext(RequestContext.builder().user(user)
                .tenantId(tenantId).peerName(peerName).build(), apiName, actionCode)
        action.actionContext = actionContext
        // uniqueRule
        IUniqueRule uniqueRule = new IUniqueRule()
        uniqueRule.setUseWhenCallOpenApi(useWhenCallOpenApi)
        action.uniqueRule = uniqueRule
        // 描述
        action.objectDescribe = Mock(IObjectDescribe)
        when:
        action.uniqueRuleLock()
        action.finallyDo()
        then:
        lockNum * infraServiceFacade.tryLock(_, _, _) >> uniqueLock
        unLockNum * infraServiceFacade.unlock(uniqueLock)
        thrown(ValidateException)
        where:
        peerName    | useWhenCallOpenApi | uniqueLock | lockNum | unLockNum
        "OpenAPI"   | true               | null       | 1       | 0
        "SmartForm" | true               | null       | 1       | 0
    }


    def "test_get_IRule"() {
        expect:
        IRule.CREATE == action.getIRule()
    }

    def "test init"() {

    }

    def "test_set_id_for_detail_data"() {
        given: ""
        // 初始化从对象数据
        IObjectData data = Mock()
        action.objectData = data
        // 初始化主对象描述
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName(masterApiName)
        action.objectDescribe = describe
        // 初始化从对象描述
        def parseObject = JSON.parseObject(detailDescribeJson)
        Map<String, IObjectDescribe> objectDescribes = [:]
        parseObject.each { k, v -> objectDescribes[k] = ObjectDescribeDocument.of(v).toObjectDescribe() }
        action.objectDescribes = objectDescribes
        // 初始化从对象数据
        List<ObjectDataDocument> detailDocuments = JSONArray.parseArray(detailDataListJson, ObjectDataDocument)
        List<IObjectData> details = ObjectDataDocument.ofDataList(detailDocuments)
        Map<String, List<IObjectData>> detailObjectData = ["SalesOrderProductObj": details]
        action.detailObjectData = detailObjectData
        when:
        action.setIdForDetailData()
        then:
        getIdNum * data.getId() >> { "1234qwer" }
        where:
        detailDescribeJson | detailDataListJson | masterApiName || masterDetailFieldName | getIdNum
        '''{"SalesOrderProductObj":{"tenant_id":"78915","store_table_name":"biz_sales_order_product","description":null,"index_version":1,"is_deleted":false,"define_type":"package","release_version":"6.4","package":"CRM","is_active":true,"last_modified_time":1562060925360,"create_time":1527155147062,"module":null,"plural_name":null,"last_modified_by":"-1000","display_name":"订单产品","created_by":"-1000","version":10,"api_name":"SalesOrderProductObj","_id":"5b0689cb9e787b86896a1a25","fields":{"_id":{"is_index":false,"create_time":1527155147062,"pattern":"","is_unique":false,"description":"_id","label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","max_length":200,"status":"released"},"order_id":{"describe_api_name":"SalesOrderProductObj","is_index":true,"is_active":true,"create_time":1562060925399,"pattern":"","is_unique":false,"label":"订单","target_api_name":"SalesOrderObj","type":"master_detail","target_related_list_name":"order_id_list","is_abstract":null,"field_num":null,"target_related_list_label":"订单产品","is_required":true,"api_name":"order_id","define_type":"package","is_create_when_master_create":true,"_id":"5d1b287d319d19c15d3f9cbf","is_index_field":false,"is_required_when_master_create":false,"is_single":false,"config":{"edit":1,"enable":0,"attrs":{"target_related_list_label":1,"is_required":0,"is_create_when_master_create":0,"label":1,"help_text":1}},"index_name":"s_10","status":"new"}}}}''' | '''[{"lock_rule":"default_lock_rule","price_book_product_id__r":"PBProdCode20191113000001","price_book_product_id":"5dcb7c73e0b451000134183578915","discount":"100","product_price":"35.00","extend_obj_data_id":null,"life_status_before_invalid":null,"product_id":"5dcb7c73e0b4510001341835","total_num":4,"owner_department":null,"lock_status":"0","package":"CRM","create_time":*************,"is_giveaway":null,"created_by":["1000"],"version":"2","relevant_team":null,"unit":"只","data_own_department":null,"subtotal":"35.00","object_describe_id":"5b0689cb9e787b86896a1a25","name":"************","_id":"5dd23ef9db7a0f0001546d43","order_id":"5dd23ef9db7a0f0001546cbf","tenant_id":"78915","promotion_id":null,"remark":null,"delivery_amount":null,"lock_user":null,"product_id__r":"螃蟹","product_id__relation_ids":"5dcb7c73e0b4510001341835","is_deleted":false,"delivered_count":null,"object_describe_api_name":"SalesOrderProductObj","price_book_product_id__relation_ids":"5dcb7c73e0b451000134183578915","order_id__relation_ids":"5dd23ef9db7a0f0001546cbf","out_owner":null,"owner":["1000"],"quantity":"1.00","last_modified_time":*************,"unit__r":"只","life_status":"normal","last_modified_by":["1000"],"out_tenant_id":null,"record_type":"default__c","order_id__r":"********-000004","sales_price":"35.00","order_by":"10","unit__v":"3"}]''' | "SalesOrderObj" || "order_id" | 2
    }

    def "test_set_id_for_detail_data_when_get_id_is_empty"() {
        given: ""
        Map map = [:]
        // 初始化从对象数据
        IObjectData data = Mock()
        action.objectData = data
        // 初始化主对象描述
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName(masterApiName)
        action.objectDescribe = describe
        // 初始化从对象描述
        def parseObject = JSON.parseObject(detailDescribeJson)
        Map<String, IObjectDescribe> objectDescribes = [:]
        parseObject.each { k, v -> objectDescribes[k] = ObjectDescribeDocument.of(v).toObjectDescribe() }
        action.objectDescribes = objectDescribes
        // 初始化从对象数据
        List<ObjectDataDocument> detailDocuments = JSONArray.parseArray(detailDataListJson, ObjectDataDocument)
        List<IObjectData> details = ObjectDataDocument.ofDataList(detailDocuments)
        Map<String, List<IObjectData>> detailObjectData = ["SalesOrderProductObj": details]
        action.detailObjectData = detailObjectData
        when:
        action.setIdForDetailData()
        then:
        getIdNum * data.getId() >> { map["id"] }
        1 * data.setId(_) >> { map["id", _] }
        1 * serviceFacade.generateId() >> { IdUtil.generateId() }
        where:
        detailDescribeJson | detailDataListJson | masterApiName || masterDetailFieldName | getIdNum
        '''{"SalesOrderProductObj":{"tenant_id":"78915","store_table_name":"biz_sales_order_product",
"description":null,"index_version":1,"is_deleted":false,"define_type":"package","release_version":"6.4",
"package":"CRM","is_active":true,"last_modified_time":1562060925360,"create_time":1527155147062,"module":null,"plural_name":null,"last_modified_by":"-1000","display_name":"订单产品","created_by":"-1000","version":10,"api_name":"SalesOrderProductObj","_id":"5b0689cb9e787b86896a1a25","fields":{"_id":{"is_index":false,"create_time":1527155147062,"pattern":"","is_unique":false,"description":"_id","label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","max_length":200,"status":"released"},"order_id":{"describe_api_name":"SalesOrderProductObj","is_index":true,"is_active":true,"create_time":1562060925399,"pattern":"","is_unique":false,"label":"订单","target_api_name":"SalesOrderObj","type":"master_detail","target_related_list_name":"order_id_list","is_abstract":null,"field_num":null,"target_related_list_label":"订单产品","is_required":true,"api_name":"order_id","define_type":"package","is_create_when_master_create":true,"_id":"5d1b287d319d19c15d3f9cbf","is_index_field":false,"is_required_when_master_create":false,"is_single":false,"config":{"edit":1,"enable":0,"attrs":{"target_related_list_label":1,"is_required":0,"is_create_when_master_create":0,"label":1,"help_text":1}},"index_name":"s_10","status":"new"}}}}''' | '''[{"lock_rule":"default_lock_rule","price_book_product_id__r":"PBProdCode20191113000001","price_book_product_id":"5dcb7c73e0b451000134183578915","discount":"100","product_price":"35.00","extend_obj_data_id":null,"life_status_before_invalid":null,"product_id":"5dcb7c73e0b4510001341835","total_num":4,"owner_department":null,"lock_status":"0","package":"CRM","create_time":*************,"is_giveaway":null,"created_by":["1000"],"version":"2","relevant_team":null,"unit":"只","data_own_department":null,"subtotal":"35.00","object_describe_id":"5b0689cb9e787b86896a1a25","name":"************","_id":"5dd23ef9db7a0f0001546d43","order_id":"5dd23ef9db7a0f0001546cbf","tenant_id":"78915","promotion_id":null,"remark":null,"delivery_amount":null,"lock_user":null,"product_id__r":"螃蟹","product_id__relation_ids":"5dcb7c73e0b4510001341835","is_deleted":false,"delivered_count":null,"object_describe_api_name":"SalesOrderProductObj","price_book_product_id__relation_ids":"5dcb7c73e0b451000134183578915","order_id__relation_ids":"5dd23ef9db7a0f0001546cbf","out_owner":null,"owner":["1000"],"quantity":"1.00","last_modified_time":*************,"unit__r":"只","life_status":"normal","last_modified_by":["1000"],"out_tenant_id":null,"record_type":"default__c","order_id__r":"********-000004","sales_price":"35.00","order_by":"10","unit__v":"3"}]''' | "SalesOrderObj" || "order_id" | 2
    }

    def "test_add_master_detail_field_into_detail_data_list_md_field_not_find"() {
        given: "初始化描述、dataList"
        // 初始化从对象描述
        IObjectDescribe detailDescribe = new ObjectDescribe()
        detailDescribe.fromJsonString(detailDescribeJson)
        // 初始化主对象描述
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName(masterApiName)
        action.objectDescribe = describe
        // 初始化从对象描述
        List<ObjectDataDocument> detailDocuments = JSONArray.parseArray(detailDataListJson, ObjectDataDocument)
        List<IObjectData> details = ObjectDataDocument.ofDataList(detailDocuments)
        when: "执行 addMasterDetailFieldIntoDetailDataList"
        action.addMasterDetailFieldIntoDetailDataList(masterId, detailDescribe, details)
        then: "抛出 ValidateException 异常"
        thrown(ValidateException)
        where:
        masterId     | detailDescribeJson | detailDataListJson | masterApiName || masterDetailFieldName
        "12345abcde" | '''{"tenant_id":"78915","store_table_name":"biz_sales_order_product","description":null,"index_version":1,"is_deleted":false,"define_type":"package","release_version":"6.4","package":"CRM","is_active":true,"last_modified_time":1562060925360,"create_time":1527155147062,"module":null,"plural_name":null,"last_modified_by":"-1000","display_name":"订单产品","created_by":"-1000","version":10,"api_name":"SalesOrderProductObj","_id":"5b0689cb9e787b86896a1a25","fields":{"_id":{"is_index":false,"create_time":1527155147062,"pattern":"","is_unique":false,"description":"_id","label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","max_length":200,"status":"released"},"order_id":{"describe_api_name":"SalesOrderProductObj","is_index":true,"is_active":true,"create_time":1562060925399,"pattern":"","is_unique":false,"label":"订单","target_api_name":"SalesOrderObj","type":"master_detail","target_related_list_name":"order_id_list","is_abstract":null,"field_num":null,"target_related_list_label":"订单产品","is_required":true,"api_name":"order_id","define_type":"package","is_create_when_master_create":true,"_id":"5d1b287d319d19c15d3f9cbf","is_index_field":false,"is_required_when_master_create":false,"is_single":false,"config":{"edit":1,"enable":0,"attrs":{"target_related_list_label":1,"is_required":0,"is_create_when_master_create":0,"label":1,"help_text":1}},"index_name":"s_10","status":"new"}}}''' | '''[{"lock_rule":"default_lock_rule","price_book_product_id__r":"PBProdCode20191113000001","price_book_product_id":"5dcb7c73e0b451000134183578915","discount":"100","product_price":"35.00","extend_obj_data_id":null,"life_status_before_invalid":null,"product_id":"5dcb7c73e0b4510001341835","total_num":4,"owner_department":null,"lock_status":"0","package":"CRM","create_time":*************,"is_giveaway":null,"created_by":["1000"],"version":"2","relevant_team":null,"unit":"只","data_own_department":null,"subtotal":"35.00","object_describe_id":"5b0689cb9e787b86896a1a25","name":"************","_id":"5dd23ef9db7a0f0001546d43","order_id":"5dd23ef9db7a0f0001546cbf","tenant_id":"78915","promotion_id":null,"remark":null,"delivery_amount":null,"lock_user":null,"product_id__r":"螃蟹","product_id__relation_ids":"5dcb7c73e0b4510001341835","is_deleted":false,"delivered_count":null,"object_describe_api_name":"SalesOrderProductObj","price_book_product_id__relation_ids":"5dcb7c73e0b451000134183578915","order_id__relation_ids":"5dd23ef9db7a0f0001546cbf","out_owner":null,"owner":["1000"],"quantity":"1.00","last_modified_time":*************,"unit__r":"只","life_status":"normal","last_modified_by":["1000"],"out_tenant_id":null,"record_type":"default__c","order_id__r":"********-000004","sales_price":"35.00","order_by":"10","unit__v":"3"}]''' | "AccountObj" || "order_id"
        "12345abcde" | "{}" | '''[{"lock_rule":"default_lock_rule","price_book_product_id__r":"PBProdCode20191113000001","price_book_product_id":"5dcb7c73e0b451000134183578915","discount":"100","product_price":"35.00","extend_obj_data_id":null,"life_status_before_invalid":null,"product_id":"5dcb7c73e0b4510001341835","total_num":4,"owner_department":null,"lock_status":"0","package":"CRM","create_time":*************,"is_giveaway":null,"created_by":["1000"],"version":"2","relevant_team":null,"unit":"只","data_own_department":null,"subtotal":"35.00","object_describe_id":"5b0689cb9e787b86896a1a25","name":"************","_id":"5dd23ef9db7a0f0001546d43","order_id":"5dd23ef9db7a0f0001546cbf","tenant_id":"78915","promotion_id":null,"remark":null,"delivery_amount":null,"lock_user":null,"product_id__r":"螃蟹","product_id__relation_ids":"5dcb7c73e0b4510001341835","is_deleted":false,"delivered_count":null,"object_describe_api_name":"SalesOrderProductObj","price_book_product_id__relation_ids":"5dcb7c73e0b451000134183578915","order_id__relation_ids":"5dd23ef9db7a0f0001546cbf","out_owner":null,"owner":["1000"],"quantity":"1.00","last_modified_time":*************,"unit__r":"只","life_status":"normal","last_modified_by":["1000"],"out_tenant_id":null,"record_type":"default__c","order_id__r":"********-000004","sales_price":"35.00","order_by":"10","unit__v":"3"}]''' | "AccountObj" || "order_id"
    }

    def "test_add_master_detail_field_into_detail_data_list"() {
        given: "初始化描述、dataList"
        // 初始化从对象描述
        IObjectDescribe detailDescribe = new ObjectDescribe()
        detailDescribe.fromJsonString(detailDescribeJson)
        // 初始化主对象描述
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName(masterApiName)
        action.objectDescribe = describe
        // 初始化从对象描述
        List<ObjectDataDocument> detailDocuments = JSONArray.parseArray(detailDataListJson, ObjectDataDocument)
        List<IObjectData> details = ObjectDataDocument.ofDataList(detailDocuments)
        when: "执行 addMasterDetailFieldIntoDetailDataList"
        action.addMasterDetailFieldIntoDetailDataList(masterId, detailDescribe, details)
        then: "从数据中的主从字段 id 正确"
        details.every { it ->
            it.get(masterDetailFieldName) == masterId
        }
        where:
        masterId     | detailDescribeJson | detailDataListJson | masterApiName || masterDetailFieldName
        "12345abcde" | '''{"tenant_id":"78915","store_table_name":"biz_sales_order_product","description":null,"index_version":1,"is_deleted":false,"define_type":"package","release_version":"6.4","package":"CRM","is_active":true,"last_modified_time":1562060925360,"create_time":1527155147062,"module":null,"plural_name":null,"last_modified_by":"-1000","display_name":"订单产品","created_by":"-1000","version":10,"api_name":"SalesOrderProductObj","_id":"5b0689cb9e787b86896a1a25","fields":{"_id":{"is_index":false,"create_time":1527155147062,"pattern":"","is_unique":false,"description":"_id","label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","max_length":200,"status":"released"},"order_id":{"describe_api_name":"SalesOrderProductObj","is_index":true,"is_active":true,"create_time":1562060925399,"pattern":"","is_unique":false,"label":"订单","target_api_name":"SalesOrderObj","type":"master_detail","target_related_list_name":"order_id_list","is_abstract":null,"field_num":null,"target_related_list_label":"订单产品","is_required":true,"api_name":"order_id","define_type":"package","is_create_when_master_create":true,"_id":"5d1b287d319d19c15d3f9cbf","is_index_field":false,"is_required_when_master_create":false,"is_single":false,"config":{"edit":1,"enable":0,"attrs":{"target_related_list_label":1,"is_required":0,"is_create_when_master_create":0,"label":1,"help_text":1}},"index_name":"s_10","status":"new"}}}''' | '''[{"lock_rule":"default_lock_rule","price_book_product_id__r":"PBProdCode20191113000001","price_book_product_id":"5dcb7c73e0b451000134183578915","discount":"100","product_price":"35.00","extend_obj_data_id":null,"life_status_before_invalid":null,"product_id":"5dcb7c73e0b4510001341835","total_num":4,"owner_department":null,"lock_status":"0","package":"CRM","create_time":*************,"is_giveaway":null,"created_by":["1000"],"version":"2","relevant_team":null,"unit":"只","data_own_department":null,"subtotal":"35.00","object_describe_id":"5b0689cb9e787b86896a1a25","name":"************","_id":"5dd23ef9db7a0f0001546d43","order_id":"5dd23ef9db7a0f0001546cbf","tenant_id":"78915","promotion_id":null,"remark":null,"delivery_amount":null,"lock_user":null,"product_id__r":"螃蟹","product_id__relation_ids":"5dcb7c73e0b4510001341835","is_deleted":false,"delivered_count":null,"object_describe_api_name":"SalesOrderProductObj","price_book_product_id__relation_ids":"5dcb7c73e0b451000134183578915","order_id__relation_ids":"5dd23ef9db7a0f0001546cbf","out_owner":null,"owner":["1000"],"quantity":"1.00","last_modified_time":*************,"unit__r":"只","life_status":"normal","last_modified_by":["1000"],"out_tenant_id":null,"record_type":"default__c","order_id__r":"********-000004","sales_price":"35.00","order_by":"10","unit__v":"3"}]''' | "SalesOrderObj" || "order_id"
        "12345abcde" | '''{"tenant_id":"78915","store_table_name":"biz_sales_order_product","description":null,"index_version":1,"is_deleted":false,"define_type":"package","release_version":"6.4","package":"CRM","is_active":true,"last_modified_time":1562060925360,"create_time":1527155147062,"module":null,"plural_name":null,"last_modified_by":"-1000","display_name":"订单产品","created_by":"-1000","version":10,"api_name":"SalesOrderProductObj","_id":"5b0689cb9e787b86896a1a25","fields":{"_id":{"is_index":false,"create_time":1527155147062,"pattern":"","is_unique":false,"description":"_id","label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","max_length":200,"status":"released"},"order_id":{"describe_api_name":"SalesOrderProductObj","is_index":true,"is_active":true,"create_time":1562060925399,"pattern":"","is_unique":false,"label":"订单","target_api_name":"SalesOrderObj","type":"master_detail","target_related_list_name":"order_id_list","is_abstract":null,"field_num":null,"target_related_list_label":"订单产品","is_required":true,"api_name":"order_id","define_type":"package","is_create_when_master_create":true,"_id":"5d1b287d319d19c15d3f9cbf","is_index_field":false,"is_required_when_master_create":false,"is_single":false,"config":{"edit":1,"enable":0,"attrs":{"target_related_list_label":1,"is_required":0,"is_create_when_master_create":0,"label":1,"help_text":1}},"index_name":"s_10","status":"new"}}}''' | "[]" | "SalesOrderObj" || "order_id"
    }


    def "set order value to every detail data"() {
        when:
        action.setOrderForDetailData(givenDataMap)

        then:
        givenDataMap.get("a").get(index).get("order_by") == expectDataMap.get("a").get(index).get("order_by")

        where:
        givenDataMap | index || expectDataMap
        makeGive()   | 0     || makeExpect()
        makeGive()   | 1     || makeExpect()
        makeGive()   | 2     || makeExpect()
        makeGive()   | 3     || makeExpect()

    }

    def makeGive() {
        Map<String, List<IObjectData>> result = Maps.newHashMap()
        List<IObjectData> list = Lists.newArrayList()
        IObjectData data = new ObjectData()
        data.set("order_by", 50)
        list.add(data)
        data = new ObjectData()
        data.set("order_by", 20)
        list.add(data)
        data = new ObjectData()
        list.add(data)
        data = new ObjectData()
        data.set("order_by", 40)
        list.add(data)
        result.put("a", list)
        return result;
    }

    def makeExpect() {
        Map<String, List<IObjectData>> result = Maps.newHashMap()
        List<IObjectData> list = Lists.newArrayList()
        IObjectData data = new ObjectData()
        data.set("order_by", 10)
        list.add(data)
        data = new ObjectData()
        data.set("order_by", 20)
        list.add(data)
        data = new ObjectData()
        data.set("order_by", 30)
        list.add(data)
        data = new ObjectData()
        data.set("order_by", 40)
        list.add(data)
        result.put("a", list)
        return result;
    }

    @Unroll
    def "test_buildDomainPluginArg method:#method, startApprovalFlowResult:#startApprovalFlowResult"() {
        given: "A method and recordTypeList parameters"
        List<String> recordTypeList = []

        and: "A detailObjectData that requires ids to be filled"
        IObjectData objectData = new ObjectData()
        Map<String, List<IObjectData>> detailObjectData = ["abc": [objectData]]
        action.detailObjectData = detailObjectData
        action.objectData = new ObjectData()

        and: "set startApprovalFlowResult"
        action.startApprovalFlowResult = startApprovalFlowResult

        when: "buildDomainPluginArg is called"
        AddActionDomainPlugin.Arg arg = action.buildDomainPluginArg(method, recordTypeList)

        then: "noIdDataList is populated"
        if (expectObjectDataId) {
            objectData.id != null
        }

        and: "An Arg instance is created with the correct data"
        assert arg.objectData == ObjectDataDocument.of(action.objectData)
        assert arg.detailObjectData == ObjectDataDocument.ofMap(action.detailObjectData)

        and: "ApprovalFlowStartSuccess is called and its result is included"
        assert arg.isApprovalFlowStartSuccess == isApprovalFlowStartSuccess

        where:
        method    | startApprovalFlowResult                         || isApprovalFlowStartSuccess | expectObjectDataId
        "before"  | ["123": ApprovalFlowStartResult.SUCCESS]        || true                       | true
        "before"  | ["1234": ApprovalFlowStartResult.ALREADY_EXIST] || false                      | true
        "before"  | null                                            || false                      | true
        "after"   | ["123": ApprovalFlowStartResult.SUCCESS]        || true                       | false
        "postAct" | ["1234": ApprovalFlowStartResult.ALREADY_EXIST] || false                      | false
        "preAct"  | null                                            || false                      | false
    }

    @Unroll
    def "test_setDefaultSystemInfo tenant_id:#tenant_id"() {
        given: "build user and actionContext"
        def user = new User(tenant_id, user_id, out_tenant_id, out_user_id)
        def actionContext = new ActionContext(RequestContext.builder().user(user).tenantId(tenant_id).build(), apiName, actionCode)
        action.setActionContext(actionContext)
        and: "build objectData and describe"
        def objectData = new ObjectData()
        def describe = new ObjectDescribe(Document.parse(describeJson))
        describe.setVisibleScope(visible_scope)
        describe.setUpstreamTenantId(upstream_tenant_id)
        action.objectDescribe = describe

        when: "setDefaultSystemInfo is invoked"
        action.setDefaultSystemInfo(objectData)

        then: "correct defaults are set on objectData"
        assert objectData.getTenantId() == tenant_id
        assert objectData.get("life_status") == expectLifeStatu
        where:
        tenant_id | user_id   | out_tenant_id | out_user_id    | visible_scope | upstream_tenant_id   || expectLifeStatu | expectPublicDataType | expectDTenantId
        "tenant1" | "user234" | "345234123"   | "456345234123" | "big"         | null                 || null            | null                 | null
        "tenant2" | "user234" | "1000000000"  | "1000000000"   | "public_big"  | "upstream_tenant_id" || null            | "public"             | "-99999"
        "tenant3" | "user234" | "1000000000"  | "1000000000"   | "public"      | "upstream_tenant_id" || "normal"        | "public"             | "-99999"
        "tenant4" | "user234" | "1000000000"  | "1000000000"   | "public"      | null                 || "ineffective"   | null                 | null
        "tenant5" | "user234" | "1000000000"  | "1000000000"   | null          | null                 || "ineffective"   | null                 | null

    }

    @Unroll
    def "setDefaultRecordType sets RECORD_TYPE_DEFAULT on null or certain values #recordType"() {
        given:
        def objectData = new ObjectData()
        objectData.setRecordType(recordType)
        def describe = new ObjectDescribe(Document.parse(describeJson))
        when:
        action.setDefaultRecordType(objectData, describe)

        then: "Default record type is set when recordType is null or 'default' or 'sail'"
        assert objectData.getRecordType() == expectRecordType

        where:
        recordType || expectRecordType
        null       || "default__c"
        "default"  || "default__c"
        "sail"     || "default__c"
    }

    def "validateRecordType:#{recordType} throws exception if record type is not found or not active"() {
        given:
        def objectData = new ObjectData()
        objectData.setRecordType(recordType)
        def describe = new ObjectDescribe(Document.parse(describeJson))
        when:
        action.setDefaultRecordType(objectData, describe)

        then:
        thrown(RecordTypeNotFound)

        where:
        recordType << ["invalidRecordType", "inactiveRecordType", "un_active__c"]
    }

    @Unroll
    def "test_modifySystemAndPackageFieldsOfObjectDataBeforeCreate"() {
        given:
        def objectData = new ObjectData()
        objectData.set("lock_user", "1234")
        def describe = new ObjectDescribe(Document.parse(describeJson))
        and:
        action.objectDescribe = describe
        when:
        action.modifySystemAndPackageFieldsOfObjectDataBeforeCreate(objectData, describe)

        then:
        assert !objectData.containsField("lock_user")
    }

    def "test_setDefaultForSignIn"() {
        given:
        def objectData = new ObjectData()
        and:
        def signIn = new SignInFieldDescribe()
        signIn.setApiName("sigin__c")
        signIn.setFieldApiName("visit_status_field", visit_status_field)
        signIn.setFieldApiName("sign_in_status_field", sign_in_status_field)
        signIn.setFieldApiName("sign_out_status_field", sign_out_status_field)
        def describe = new ObjectDescribe(Document.parse(describeJson))
        describe.addFieldDescribe(signIn)
        when:
        action.setDefaultForSignIn(objectData, describe)
        then:
        assert objectData.get("visit_status_field") == "incomplete"
        assert objectData.get("sign_in_status_field") == "incomplete"
        assert objectData.get("sign_out_status_field") == "incomplete"


//        assert objectData.containsField("sign_in_info_list_field")
//        assert objectData.containsField("interval_field")
//        assert objectData.containsField("sign_in_time_field")
//        assert objectData.containsField("sign_out_time_field")
//        assert objectData.containsField("sign_in_location_field")
//        assert objectData.containsField("sign_out_location_field")

        where:
        visit_status_field   | sign_in_status_field   | sign_out_status_field
        "visit_status_field" | "sign_in_status_field" | "sign_out_status_field"
    }

    def "test_getObjectAction"() {
        expect:
        action.getObjectAction() == ObjectAction.CREATE
    }

    def "test_getRecordTypes"() {
        given:
        def objectData = new ObjectData()
        objectData.setRecordType(recordType)
        action.objectData = objectData
        when:
        def recordTypes = action.getRecordTypes()
        then:
        recordTypes == [recordType]
        where:
        recordType << ["record__c", "ssnncc"]
    }

    def "test_findDbMasterData"() {
        expect:
        action.findDbMasterData() == null
    }

    def "test_validateConvertRulesExcessCheck_not_execute_doConvertRulesExcessCheck"() {
        when:
        action.validateConvertRulesExcessCheck()
        then:
        0 * infraServiceFacade.doConvertRulesExcessCheck(*_)
    }

    def "test_validate_master_detail_currency_with_slave_object"() {
        given:
        def objectData = new ObjectData()
        objectData.set("mc_currency", "CNY")
        action.objectData = objectData
        and: "describe"
        def objectDescribe = new ObjectDescribe(Document.parse(describeJson))
        action.objectDescribe = objectDescribe
        and: "add field"
        def masterDetailField = new MasterDetailFieldDescribe()
        masterDetailField.setApiName("master_detail__c")
        objectDescribe.addFieldDescribe(masterDetailField)
        when:
        action.validateMasterDetailCurrency()
        then:
        1 * serviceFacade.findObjectDataIgnoreAll(*_) >> objectData
    }

    def "test_validate_master_detail_currency"() {
        given:
        def objectData = new ObjectData()
        objectData.set("mc_currency", "CNY")
        action.objectData = objectData
        and: "describe"
        def objectDescribe = new ObjectDescribe(Document.parse(describeJson))
        action.objectDescribe = objectDescribe
        and: "MultiCurrencyLogicService"
        def multiCurrencyLogicService = Mock(MultiCurrencyLogicService)
        serviceFacade.getMultiCurrencyLogicService() >> multiCurrencyLogicService
        when:
        action.validateMasterDetailCurrency()
        then:
        1 * multiCurrencyLogicService.findPersonCurrencyCode(*_) >> "USD"
        1 * multiCurrencyLogicService.findCurrencyList(*_)
    }

    def "test_validateChangeRuleWithChangeData_when_change_rule_empty"() {
        given:
        def objectData = new ObjectData()
        action.objectData = objectData
        when:
        action.validateChangeRuleWithChangeData()
        then:
        0 * infraServiceFacade.validateChangeRuleWithChangeData(*_)
    }

    def "test_validateChangeRuleWithChangeData"() {
        given:
        def objectData = new ObjectData()
        objectData.set("change_order_rule", "234")
        action.objectData = objectData
        when:
        action.validateChangeRuleWithChangeData()
        then:
        1 * infraServiceFacade.validateChangeRuleWithChangeData(*_)
    }

    def "test_validateLookupData"() {
        given:
        def data = new ObjectData(Document.parse(dataJson))
        def describe = new ObjectDescribe(Document.parse(describeJson))
        Whitebox.setInternalState(action, "isCurrencyEmpty", true)
        when:
        action.validateLookupData(data, describe)
        then:
        serviceFacade.validateLookupData(_, _, _) >> {
            User user, IObjectData objectData, ObjectReferenceWrapper referenceField -> return
        }
    }

    def "test_validateConvertRulesExcessCheck"() {
        given:
        def convertRuleDataContainer = Mock(ConvertRuleDataContainer)
        Whitebox.setInternalState(action, "convertRuleDataContainer", convertRuleDataContainer)
        when:
        action.validateConvertRulesExcessCheck()
        then:
        1 * infraServiceFacade.doConvertRulesExcessCheck(*_)
    }

    def "test_triggerApprovalFlow"() {
        given:
        def objectData = new ObjectData(Document.parse(dataJson))
        objectData.set("life_status", "normal")
        action.lastLifeStatus = ObjectLifeStatus.of("normal")
        action.objectData = objectData

        and:
        def objectDescribe = new ObjectDescribe(Document.parse(describeJson))
        action.objectDescribe = objectDescribe

        and:
        def approvalFlowServiceFacade = Mock(ApprovalFlowServiceFacade)
        when:
        action.triggerApprovalFlow()
        then:
        1 * serviceFacade.getBean(ApprovalFlowServiceFacade) >> approvalFlowServiceFacade
    }

    def "test_tryTriggerMasterEditApproval"() {
        given:
        def objectData = new ObjectData(Document.parse(dataJson))
        objectData.set("life_status", "normal")
        action.lastLifeStatus = ObjectLifeStatus.of("normal")
        action.objectData = objectData

        and:
        def objectDescribe = new ObjectDescribe(Document.parse(describeJson))
        action.objectDescribe = objectDescribe

        and:
        def approvalFlowServiceFacade = Mock(ApprovalFlowServiceFacade)
        serviceFacade.getBean(ApprovalFlowServiceFacade) >> approvalFlowServiceFacade

        when:
        action.tryTriggerMasterEditApproval()
        then:
        1 * serviceFacade.getBean(ApprovalFlowServiceFacade) >> approvalFlowServiceFacade
    }

    def "test_doSaveData_when_result_is_empty"() {
        given:
        when:
        action.doSaveData()
        then:
        1 * serviceFacade.saveMasterAndDetailData(_, _, _)
        thrown(MetaDataBusinessException)
    }

    def "test_doSaveData"() {
        given:
        def saveResult = Mock(SaveMasterAndDetailData.Result)
        and:
        def describe = new ObjectDescribe(Document.parse(describeJson))
        action.objectDescribe = describe
        when:
        action.doSaveData()
        then:
        1 * serviceFacade.saveMasterAndDetailData(_, _, _) >> saveResult
    }

    def "test_changePartnerAndOwner_when_partnerId_is_empty"() {
        given:
        def dataIds = Sets.newHashSet("123", "321")
        when:
        def ret = action.changePartnerAndOwner(user, apiName, dataIds, partnerId)
        then:
        assert Objects.equals(expect, ret)
        where:
        partnerId || expect
        ""        || new com.facishare.paas.appframework.common.util.Tuple<Integer, Long>()
    }

    def "test_changePartnerAndOwner"() {
        given:
        def dataIds = Sets.newHashSet("123", "321")
        when:
        action.changePartnerAndOwner(user, apiName, dataIds, partnerId)
        then:
        1 * infraServiceFacade.getRelationDownstreamInfo(tenantId, _) >> downstreamMap
        where:
        partnerId || downstreamMap
        "112"     || ["112": new RelationDownstreamResult()]
        "112"     || ["1123": new RelationDownstreamResult()]
    }

    def "test_needFillOutOwner"() {
        given:
        def arg = new BaseObjectSaveAction.Arg()
        arg.fillOutOwner = fillOutOwner
        action.arg = arg
        when:
        def ret = action.needFillOutOwner()
        then:
        expect == ret
        where:
        fillOutOwner || expect
        null         || false
        true         || true
        false        || false
    }

    def "test_calculateCountAndFormulaFields"() {
        when:
        action.calculateCountAndFormulaFields()
        then:
        1 * serviceFacade.calculateForAddAction(*_)
    }

    def "test_modifyRelatedObjectDataBeforeCreate_related_team_not_open"() {
        given:
        def describe = new ObjectDescribe(Document.parse(describeJson))
        action.objectDescribe = describe
        def object = new ObjectData(Document.parse(dataJson))
        action.objectData = object
        and: "relatedObjectData"
        Map<String, List<SaveMasterAndDetailData.RelatedObjectData>> relatedObjectData =
                ["SalesOrderObj": [SaveMasterAndDetailData.RelatedObjectData.create("relatedFieldName", [object])]]
        action.relatedObjectData = relatedObjectData
        and: "relatedDescribes"
        Map<String, IObjectDescribe> relatedDescribes = ["SalesOrderObj": describe]
        action.relatedDescribes = relatedDescribes
        and: "OptionalFeaturesSwitchDTO"
        def optionalFeaturesSwitchDTO = OptionalFeaturesSwitchDTO.builder()
                .isRelatedTeamEnabled(true)
                .build()
        and: ""
        def configCode = describe.getApiName() + "_" + "support_team_add_creator"
        def configPojo = ConfigPojo.builder().key(configCode).configValue(configValue).build()
        when:
        action.modifyRelatedObjectDataBeforeCreate()
        then:
        1 * serviceFacade.processData(*_)
        1 * infraServiceFacade.findOptionalFeaturesSwitch(*_) >> optionalFeaturesSwitchDTO
        infraServiceFacade.queryConfigData("78586", configCode, ["SalesOrderObj"]) >> [configPojo]
        where:
        isRelatedTeamEnabled | configValue
        false                | (String) _
        true                 | "true"
        true                 | "1"
        true                 | "2"
    }

    def "test_modifyDetailObjectDataBeforeCreate_when_objectDataDetail_is_empty"() {
        given:
        def object = new ObjectData(Document.parse(dataJson))
        when:
        action.modifyDetailObjectDataBeforeCreate(object, [:])
        then:
        noExceptionThrown()
    }

    def "test_modifyDetailObjectDataBeforeCreate"() {
        given:
        def object = new ObjectData(Document.parse(dataJson))
        object.setOwner(["1001"])
        object.setOutOwner(["100001001"])
        def describe = new ObjectDescribe(Document.parse(describeJson))
        action.objectDescribes = ["test": describe]
        action.objectDescribe = describe
        when:
        action.modifyDetailObjectDataBeforeCreate(object, ["test": [object]])
        then:
        noExceptionThrown()
    }

    def "test_getButtonApiName"() {
        given:
        Whitebox.setInternalState(AppFrameworkConfig, "addEditUiActionGrayTenant", addEditUiActionGrayTenant)
        Whitebox.setInternalState(AppFrameworkConfig, "addEditUiActionGrayObject", addEditUiActionGrayObject)
        when:
        def buttonName = action.getButtonApiName()
        then:
        expect == buttonName
        where:
        addEditUiActionGrayTenant | addEditUiActionGrayObject        || expect
        Sets.newHashSet()         | Sets.newHashSet()                || "Add_button_default"
        Sets.newHashSet("ALL")    | Sets.newHashSet("SalesOrderObj") || "Add_button_default"
        Sets.newHashSet("ALL")    | Sets.newHashSet("whatever")      || "Add_Save_button_default"
        Sets.newHashSet("123")    | Sets.newHashSet("whatever")      || "Add_Save_button_default"
    }

    def "test_replaceDetailDataMergeResult"() {
        given:
        def describe = new ObjectDescribe(Document.parse(describeJson))
        action.detailDescribeMap = ["objApiName": describe]

        def objectData = new ObjectData(Document.parse(dataJson))
        objectData.setDescribeApiName("objApiName")

        def mergeStateInfo = MergeStateInfo.of(MergeState.ADD, objectData)
        def sourceMergeStateContainer = new MergeStateContainer([mergeStateInfo])
        when:
        action.replaceDetailDataMergeResult(sourceMergeStateContainer)
        sourceMergeStateContainer.detailsToAdd() >> [objectData]
        then:
        noExceptionThrown()
    }

    def "test_modifyDataOwnOrgAndDept_when_is_slave_object"() {
        given:
        def objectData = new ObjectData(Document.parse(dataJson))
        def describe = new ObjectDescribe(Document.parse(describeJson))
        and:
        def masterDetail = new MasterDetailFieldDescribe()
        masterDetail.setApiName("field_master_detail__c")
        masterDetail.setTargetApiName("object_master__c")
        describe.addFieldDescribe(masterDetail)
        objectData.set("field_master_detail__c", "1111222233334455")
        and:
        def data = Mock(ObjectData)
        and:
        def data_own_organization = new DepartmentFieldDescribe()
        data_own_organization.setApiName("data_own_organization")
        describe.addFieldDescribe(data_own_organization)

        when:
        Whitebox.invokeMethod(action, "modifyDataOwnOrgAndDept", objectData, describe)
        then:
        serviceFacade.findObjectData(user, _, "object_master__c") >> data
    }

    def "test_modifyDataOwnOrgAndDept_when_throw"() {
        given:
        def describe = new ObjectDescribe(Document.parse(describeJson))
        and: "objectData"
        def objectData = new ObjectData(Document.parse(dataJson))
        objectData.setOwner(["1000"])
        objectData.setDataOwnDepartment(dataOwnDepartment)
        objectData.setDataOwnOrganization(dataOwnOrganization)
        and:
        def data_own_organization = new DepartmentFieldDescribe()
        data_own_organization.setApiName("data_own_organization")
        describe.addFieldDescribe(data_own_organization)
        when:
        Whitebox.invokeMethod(action, "modifyDataOwnOrgAndDept", objectData, describe)
        then:
        1 * serviceFacade.fillDataOwnDeptAndOrgByOutUser(user, _, _) >> true
        thrown(ValidateException)
        where:
        dataOwnDepartment | dataOwnOrganization
        ["1001"]          | null
        null              | ["1007"]
    }

    def "test_modifyDataOwnOrgAndDept_fillData_is_false"() {
        given:
        def describe = new ObjectDescribe(Document.parse(describeJson))
        and: "objectData"
        def objectData = new ObjectData(Document.parse(dataJson))
        objectData.setOwner(["1000"])
        objectData.setDataOwnDepartment(null)
        and:
        def data_own_organization = new DepartmentFieldDescribe()
        data_own_organization.setApiName("data_own_organization")
        describe.addFieldDescribe(data_own_organization)
        and:
        OrganizationInfo organizationInfo = Mock(OrganizationInfo)
        when:
        Whitebox.invokeMethod(action, "modifyDataOwnOrgAndDept", objectData, describe)
        then:
        1 * serviceFacade.fillDataOwnDeptAndOrgByOutUser(user, _, _) >> false
        1 * serviceFacade.findMainOrgAndDeptByUserId(*_) >> organizationInfo
    }

    def "test_setDefaultForChangeOrder"() {
        given:
        def ruleName = "rule_name"
        and: "object"
        def objectData = new ObjectData(Document.parse(dataJson))
        objectData.set("change_order_rule", ruleName)
        action.objectData = objectData
        and: "describe"
        def describe = new ObjectDescribe(Document.parse(describeJson))
        describe.setOriginalDescribeApiName("original_describe")
        and: "gray"
        def item = Mock(ChangeOrderConfig.ChangeOrderConfigItem)
        Whitebox.setInternalState(ChangeOrderConfig, "changeOrderOriginalDescribeGray", ["original_describe": item])
        when:
        action.setDefaultForChangeOrder(objectData, describe)
        then:
        1 * item.match(_, _) >> true
    }
}
