package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.function.dto.FuncBizExtendParam;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataMiscService;
import com.facishare.paas.appframework.metadata.QuoteValueService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PostFuncAction单元测试 - JUnit5版本
 * 
 * GenerateByAI
 * 
 * 测试PostFuncAction后置函数动作的核心功能：
 * - getType: 返回POST_FUNCTION类型
 * - validateResult: 验证结果（空实现）
 * - needAsyncInvoke: 异步调用判断逻辑
 * - handleResult: 结果处理（返回null）
 * - 继承自AbstractFuncAction的通用功能
 * 
 * 覆盖场景：
 * - 类型标识验证
 * - 异步调用灰度配置
 * - 结果验证和处理
 * - 后置执行特殊逻辑
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("PostFuncAction - 后置函数动作测试")
class PostFuncActionTest {

    @Mock
    private FunctionLogicService functionLogicService;
    
    @Mock
    private ParseVarService parseVarService;
    
    @Mock
    private MetaDataMiscService metaDataMiscService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private QuoteValueService quoteValueService;
    
    @Mock
    private ArgumentProcessorService argumentProcessorService;

    @Spy
    @InjectMocks
    private PostFuncAction postFuncAction;

    // 测试常量
    private static final String TENANT_ID = "74255";
    private static final String USER_ID = "1000";
    private static final String FUNCTION_API_NAME = "testPostFunction";
    private static final String DESCRIBE_API_NAME = "TestObject";
    private static final String BUTTON_API_NAME = "testPostButton";

    private User testUser;
    private IObjectData testObjectData;
    private IObjectDescribe testDescribe;
    private IUdefButton testButton;
    private IUdefAction testAction;
    private IUdefFunction testFunction;
    private ActionExecutorContext testContext;
    private ButtonExecutor.Arg testArg;

    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser = new User(TENANT_ID, USER_ID);
        
        // 创建测试对象数据
        testObjectData = new ObjectData();
        testObjectData.set("id", "123");
        testObjectData.set("name", "test");
        
        // 创建Mock对象
        testDescribe = mock(IObjectDescribe.class);
        testButton = mock(IUdefButton.class);
        testAction = mock(IUdefAction.class);
        testFunction = mock(IUdefFunction.class);
        testContext = mock(ActionExecutorContext.class);
        testArg = mock(ButtonExecutor.Arg.class);
        
        // 配置基础Mock行为
        when(testDescribe.getApiName()).thenReturn(DESCRIBE_API_NAME);
        when(testButton.getApiName()).thenReturn(BUTTON_API_NAME);
        when(testFunction.getApiName()).thenReturn(FUNCTION_API_NAME);
        when(testContext.getUser()).thenReturn(testUser);
        when(testContext.getDescribe()).thenReturn(testDescribe);
        when(testContext.getButton()).thenReturn(testButton);
        when(testContext.getAction()).thenReturn(testAction);
        when(testContext.getIgnoreFields()).thenReturn(Collections.emptySet());
        when(testArg.getObjectData()).thenReturn(testObjectData);
        when(testArg.toDetails()).thenReturn(Collections.emptyMap());
        when(testArg.getArgs()).thenReturn(Collections.emptyMap());
        when(testArg.getActionParams()).thenReturn(Collections.emptyMap());
        when(testArg.getRelatedDataList()).thenReturn(Collections.emptyMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getType方法返回POST_FUNCTION类型
     */
    @Test
    @DisplayName("getType - 返回POST_FUNCTION类型")
    void testGetType_ReturnsPostFunctionType() {
        // When
        ActionExecutorType result = postFuncAction.getType();
        
        // Then
        assertNotNull(result, "类型不应为null");
        assertEquals(ActionExecutorType.POST_FUNCTION, result, "应返回POST_FUNCTION类型");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试validateResult方法的空实现
     */
    @Test
    @DisplayName("validateResult - 空实现验证")
    void testValidateResult_EmptyImplementation() {
        // Given
        RunResult runResult = new RunResult();
        runResult.setSuccess(false);
        runResult.setErrorInfo("Test error");
        
        // When & Then - 应该不抛出任何异常
        assertDoesNotThrow(() -> {
            postFuncAction.validateResult(runResult);
        }, "validateResult空实现不应抛出异常");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试needAsyncInvoke方法在灰度开启时返回true
     */
    @Test
    @DisplayName("needAsyncInvoke - 灰度开启时返回true")
    void testNeedAsyncInvoke_GrayEnabled() {
        try (MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            // Given
            mockedConfig.when(() -> AppFrameworkConfig.buttonPostFuncActionAsyncGray(
                    TENANT_ID, DESCRIBE_API_NAME, BUTTON_API_NAME))
                    .thenReturn(true);
            
            // When
            boolean result = postFuncAction.needAsyncInvoke(testUser, DESCRIBE_API_NAME, BUTTON_API_NAME);
            
            // Then
            assertTrue(result, "灰度开启时应返回true");
            
            // 验证配置方法被调用
            mockedConfig.verify(() -> AppFrameworkConfig.buttonPostFuncActionAsyncGray(
                    TENANT_ID, DESCRIBE_API_NAME, BUTTON_API_NAME));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试needAsyncInvoke方法在灰度关闭时返回false
     */
    @Test
    @DisplayName("needAsyncInvoke - 灰度关闭时返回false")
    void testNeedAsyncInvoke_GrayDisabled() {
        try (MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            // Given
            mockedConfig.when(() -> AppFrameworkConfig.buttonPostFuncActionAsyncGray(
                    TENANT_ID, DESCRIBE_API_NAME, BUTTON_API_NAME))
                    .thenReturn(false);
            
            // When
            boolean result = postFuncAction.needAsyncInvoke(testUser, DESCRIBE_API_NAME, BUTTON_API_NAME);
            
            // Then
            assertFalse(result, "灰度关闭时应返回false");
            
            // 验证配置方法被调用
            mockedConfig.verify(() -> AppFrameworkConfig.buttonPostFuncActionAsyncGray(
                    TENANT_ID, DESCRIBE_API_NAME, BUTTON_API_NAME));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：参数化测试不同租户ID的异步调用判断
     */
    @ParameterizedTest
    @ValueSource(strings = {"74255", "12345", "67890"})
    @DisplayName("needAsyncInvoke - 不同租户ID测试")
    void testNeedAsyncInvoke_DifferentTenantIds(String tenantId) {
        try (MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            // Given
            User userWithDifferentTenant = new User(tenantId, USER_ID);
            mockedConfig.when(() -> AppFrameworkConfig.buttonPostFuncActionAsyncGray(
                    tenantId, DESCRIBE_API_NAME, BUTTON_API_NAME))
                    .thenReturn(true);
            
            // When
            boolean result = postFuncAction.needAsyncInvoke(userWithDifferentTenant, DESCRIBE_API_NAME, BUTTON_API_NAME);
            
            // Then
            assertTrue(result, "不同租户ID应正确调用灰度配置");
            
            // 验证配置方法被正确调用
            mockedConfig.verify(() -> AppFrameworkConfig.buttonPostFuncActionAsyncGray(
                    tenantId, DESCRIBE_API_NAME, BUTTON_API_NAME));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试handleResult方法返回null
     */
    @Test
    @DisplayName("handleResult - 返回null")
    void testHandleResult_ReturnsNull() {
        // Given
        RunResult runResult = new RunResult();
        runResult.setSuccess(true);
        runResult.setFunctionResult("test result");
        
        // When
        ButtonExecutor.Result result = postFuncAction.handleResult(testUser, testArg, runResult);
        
        // Then
        assertNull(result, "handleResult应返回null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试handleResult方法处理不同类型的RunResult
     */
    @Test
    @DisplayName("handleResult - 处理不同类型的RunResult")
    void testHandleResult_DifferentRunResultTypes() {
        // Given - 成功结果
        RunResult successResult = new RunResult();
        successResult.setSuccess(true);
        successResult.setFunctionResult("success");
        
        // Given - 失败结果
        RunResult failureResult = new RunResult();
        failureResult.setSuccess(false);
        failureResult.setErrorInfo("error");
        
        // Given - 空结果
        RunResult nullResult = new RunResult();
        nullResult.setFunctionResult(null);
        
        // When & Then
        assertNull(postFuncAction.handleResult(testUser, testArg, successResult), 
                "成功结果应返回null");
        assertNull(postFuncAction.handleResult(testUser, testArg, failureResult), 
                "失败结果应返回null");
        assertNull(postFuncAction.handleResult(testUser, testArg, nullResult), 
                "空结果应返回null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试完整的同步执行流程
     */
    @Test
    @DisplayName("invoke - 同步执行流程")
    void testInvoke_SynchronousExecution() {
        try (MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            // Given
            String actionParameterJson = "{\n"
                    + "    \"func_api_name\": \"" + FUNCTION_API_NAME + "\",\n"
                    + "    \"func_args\": []\n"
                    + "}";
            when(testAction.getActionParamter()).thenReturn(actionParameterJson);
            when(functionLogicService.findUDefFunction(testUser, FUNCTION_API_NAME, DESCRIBE_API_NAME))
                    .thenReturn(testFunction);
            
            RunResult runResult = new RunResult();
            runResult.setSuccess(true);
            runResult.setFunctionResult("post function result");
            when(functionLogicService.executeUDefFunction(
                    any(User.class), any(IUdefFunction.class), any(Map.class), any(IObjectData.class),
                    any(Map.class), any(Map.class), any(Map.class), any(FuncBizExtendParam.Arg.class)))
                    .thenReturn(runResult);
            
            when(argumentProcessorService.processArguments(any(), any(), any(), any(), any(), any()))
                    .thenReturn(Collections.emptyList());
            
            // 配置灰度关闭，确保同步执行
            mockedConfig.when(() -> AppFrameworkConfig.buttonPostFuncActionAsyncGray(
                    TENANT_ID, DESCRIBE_API_NAME, BUTTON_API_NAME))
                    .thenReturn(false);
            
            // When
            ButtonExecutor.Result result = postFuncAction.invoke(testArg, testContext);
            
            // Then
            assertNull(result, "PostFuncAction的handleResult返回null，所以invoke也应返回null");
            
            // 验证关键方法被调用
            verify(functionLogicService).findUDefFunction(testUser, FUNCTION_API_NAME, DESCRIBE_API_NAME);
            verify(functionLogicService).executeUDefFunction(
                    any(User.class), any(IUdefFunction.class), any(Map.class), any(IObjectData.class),
                    any(Map.class), any(Map.class), any(Map.class), any(FuncBizExtendParam.Arg.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步执行流程
     */
    @Test
    @DisplayName("invoke - 异步执行流程")
    void testInvoke_AsynchronousExecution() {
        try (MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            // Given
            String actionParameterJson = "{\n"
                    + "    \"func_api_name\": \"" + FUNCTION_API_NAME + "\",\n"
                    + "    \"func_args\": []\n"
                    + "}";
            when(testAction.getActionParamter()).thenReturn(actionParameterJson);
            
            // 配置灰度开启，触发异步执行
            mockedConfig.when(() -> AppFrameworkConfig.buttonPostFuncActionAsyncGray(
                    TENANT_ID, DESCRIBE_API_NAME, BUTTON_API_NAME))
                    .thenReturn(true);
            
            // When
            ButtonExecutor.Result result = postFuncAction.invoke(testArg, testContext);
            
            // Then
            assertNull(result, "异步执行应返回null");
            
            // 验证synchronizeData被调用
            verify(testArg).synchronizeData();
            
            // 验证灰度配置被调用
            mockedConfig.verify(() -> AppFrameworkConfig.buttonPostFuncActionAsyncGray(
                    TENANT_ID, DESCRIBE_API_NAME, BUTTON_API_NAME));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试灰度配置的边界条件
     */
    @Test
    @DisplayName("needAsyncInvoke - 灰度配置边界条件")
    void testNeedAsyncInvoke_EdgeCases() {
        try (MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            // Given - null参数测试
            mockedConfig.when(() -> AppFrameworkConfig.buttonPostFuncActionAsyncGray(
                    anyString(), anyString(), anyString()))
                    .thenReturn(false);
            
            // When & Then - 测试null参数不会导致异常
            assertDoesNotThrow(() -> {
                boolean result = postFuncAction.needAsyncInvoke(testUser, null, null);
                assertFalse(result, "null参数应返回false");
            });
            
            // 测试空字符串参数
            assertDoesNotThrow(() -> {
                boolean result = postFuncAction.needAsyncInvoke(testUser, "", "");
                assertFalse(result, "空字符串参数应返回false");
            });
        }
    }
}
