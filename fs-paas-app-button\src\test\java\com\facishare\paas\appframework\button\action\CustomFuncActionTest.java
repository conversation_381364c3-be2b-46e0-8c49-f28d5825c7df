package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataMiscService;
import com.facishare.paas.appframework.metadata.QuoteValueService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CustomFuncAction单元测试 - JUnit5版本
 * 
 * GenerateByAI
 * 
 * 测试CustomFuncAction自定义函数动作的核心功能：
 * - getType: 返回CURRENT_FUNCTION类型
 * - invoke: 继承自AbstractFuncAction的函数执行逻辑
 * - 自定义函数的执行流程和参数传递
 * - 函数执行结果的处理
 * 
 * 覆盖场景：
 * - 正常自定义函数执行流程
 * - 函数不存在的异常处理
 * - 函数执行失败的异常处理
 * - 参数传递和结果返回验证
 * - 不同返回类型的处理
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("CustomFuncAction - 自定义函数动作测试")
class CustomFuncActionTest {

    @Mock
    private FunctionLogicService functionLogicService;
    
    @Mock
    private ParseVarService parseVarService;
    
    @Mock
    private MetaDataMiscService metaDataMiscService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private QuoteValueService quoteValueService;
    
    @Mock
    private ArgumentProcessorService argumentProcessorService;

    @InjectMocks
    private CustomFuncAction customFuncAction;

    // 测试常量
    private static final String TENANT_ID = "74255";
    private static final String USER_ID = "1000";
    private static final String DESCRIBE_API_NAME = "TestObject";
    private static final String BUTTON_API_NAME = "testCustomFuncButton";
    private static final String FUNCTION_API_NAME = "testCustomFunction";

    private User testUser;
    private IObjectData testObjectData;
    private IObjectDescribe testDescribe;
    private IUdefButton testButton;
    private IUdefAction testAction;
    private IUdefFunction testFunction;
    private ActionExecutorContext testContext;
    private ButtonExecutor.Arg testArg;

    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser = new User(TENANT_ID, USER_ID);
        
        // 创建测试对象数据
        testObjectData = new ObjectData();
        testObjectData.set("id", "123");
        testObjectData.set("name", "test custom function");
        
        // 创建Mock对象
        testDescribe = mock(IObjectDescribe.class);
        testButton = mock(IUdefButton.class);
        testAction = mock(IUdefAction.class);
        testFunction = mock(IUdefFunction.class);
        testContext = mock(ActionExecutorContext.class);
        testArg = mock(ButtonExecutor.Arg.class);
        
        // 配置基础Mock行为
        when(testDescribe.getApiName()).thenReturn(DESCRIBE_API_NAME);
        when(testButton.getApiName()).thenReturn(BUTTON_API_NAME);
        when(testFunction.getApiName()).thenReturn(FUNCTION_API_NAME);
        when(testContext.getUser()).thenReturn(testUser);
        when(testContext.getDescribe()).thenReturn(testDescribe);
        when(testContext.getButton()).thenReturn(testButton);
        when(testContext.getAction()).thenReturn(testAction);
        when(testContext.getIgnoreFields()).thenReturn(Collections.emptySet());
        when(testArg.getObjectData()).thenReturn(testObjectData);
        when(testArg.toDetails()).thenReturn(Collections.emptyMap());
        when(testArg.getArgs()).thenReturn(Collections.emptyMap());
        when(testArg.getActionParams()).thenReturn(Collections.emptyMap());
        when(testArg.getRelatedDataList()).thenReturn(Collections.emptyMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getType方法返回CURRENT_FUNCTION类型
     */
    @Test
    @DisplayName("getType - 返回CURRENT_FUNCTION类型")
    void testGetType_ReturnsCurrentFunctionType() {
        // When
        ActionExecutorType result = customFuncAction.getType();
        
        // Then
        assertNotNull(result, "类型不应为null");
        assertEquals(ActionExecutorType.CURRENT_FUNCTION, result, "应返回CURRENT_FUNCTION类型");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法的正常自定义函数执行流程
     */
    @Test
    @DisplayName("invoke - 正常自定义函数执行流程")
    void testInvoke_NormalCustomFunctionFlow() {
        // Given
        String actionParameterJson = "{\n"
                + "    \"func_api_name\": \"" + FUNCTION_API_NAME + "\",\n"
                + "    \"func_args\": [\n"
                + "        {\n"
                + "            \"name\": \"param1\",\n"
                + "            \"type\": \"text\",\n"
                + "            \"value\": \"value1\"\n"
                + "        }\n"
                + "    ]\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        
        // 配置函数查找
        when(functionLogicService.findUDefFunction(testUser, FUNCTION_API_NAME, DESCRIBE_API_NAME))
                .thenReturn(testFunction);
        
        // 配置参数处理
        AbstractFuncAction.FunctionArg processedArg = new AbstractFuncAction.FunctionArg();
        processedArg.setName("param1");
        processedArg.setRealValue("processed_value1");
        when(argumentProcessorService.processArguments(any(), any(), any(), any(), any(), any()))
                .thenReturn(Collections.singletonList(processedArg));
        
        // 配置函数执行结果
        RunResult runResult = new RunResult();
        runResult.setSuccess(true);
        runResult.setFunctionResult("function executed successfully");
        runResult.setReturnType("UIAction");
        
        when(functionLogicService.executeUDefFunction(eq(testUser), eq(testFunction), any(Map.class),
                eq(testObjectData), any(Map.class)))
                .thenReturn(runResult);
        
        // When
        ButtonExecutor.Result result = customFuncAction.invoke(testArg, testContext);
        
        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isHasReturnValue(), "应有返回值");
        assertEquals("UIAction", result.getReturnType(), "返回类型应正确");
        
        // 验证关键方法被调用
        verify(functionLogicService).findUDefFunction(testUser, FUNCTION_API_NAME, DESCRIBE_API_NAME);
        verify(argumentProcessorService).processArguments(any(), any(), any(), any(), any(), any());
        verify(functionLogicService).executeUDefFunction(eq(testUser), eq(testFunction), any(Map.class),
                eq(testObjectData), any(Map.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法处理函数不存在的异常
     */
    @Test
    @DisplayName("invoke - 函数不存在异常处理")
    void testInvoke_FunctionNotExistException() {
        // Given
        String actionParameterJson = "{\n"
                + "    \"func_api_name\": \"nonExistentFunction\",\n"
                + "    \"func_args\": []\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        
        // 配置函数不存在
        when(functionLogicService.findUDefFunction(testUser, "nonExistentFunction", DESCRIBE_API_NAME))
                .thenReturn(null);
        
        // When & Then
        FunctionException exception = assertThrows(FunctionException.class, () -> {
            customFuncAction.invoke(testArg, testContext);
        });
        
        assertNotNull(exception.getMessage(), "异常消息不应为null");
        verify(functionLogicService).findUDefFunction(testUser, "nonExistentFunction", DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法处理函数执行失败的异常
     */
    @Test
    @DisplayName("invoke - 函数执行失败异常处理")
    void testInvoke_FunctionExecutionFailedException() {
        // Given
        String actionParameterJson = "{\n"
                + "    \"func_api_name\": \"" + FUNCTION_API_NAME + "\",\n"
                + "    \"func_args\": []\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        
        // 配置函数查找
        when(functionLogicService.findUDefFunction(testUser, FUNCTION_API_NAME, DESCRIBE_API_NAME))
                .thenReturn(testFunction);
        
        // 配置参数处理
        when(argumentProcessorService.processArguments(any(), any(), any(), any(), any(), any()))
                .thenReturn(Collections.emptyList());
        
        // 配置函数执行失败 - 返回null来模拟执行失败
        when(functionLogicService.executeUDefFunction(eq(testUser), eq(testFunction), any(Map.class),
                eq(testObjectData), any(Map.class)))
                .thenReturn(null);

        // When & Then
        FunctionException exception = assertThrows(FunctionException.class, () -> {
            customFuncAction.invoke(testArg, testContext);
        });

        assertNotNull(exception.getMessage(), "异常消息不应为null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：参数化测试不同的函数API名称
     */
    @ParameterizedTest
    @ValueSource(strings = {
        "simpleFunction", 
        "complex_function_name", 
        "function-with-dash", 
        "function123", 
        "MyCustomFunction"
    })
    @DisplayName("invoke - 不同函数API名称处理")
    void testInvoke_DifferentFunctionApiNames(String functionApiName) {
        // Given
        String actionParameterJson = "{\n"
                + "    \"func_api_name\": \"" + functionApiName + "\",\n"
                + "    \"func_args\": []\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        
        // 配置函数查找
        when(functionLogicService.findUDefFunction(testUser, functionApiName, DESCRIBE_API_NAME))
                .thenReturn(testFunction);
        
        // 配置参数处理
        when(argumentProcessorService.processArguments(any(), any(), any(), any(), any(), any()))
                .thenReturn(Collections.emptyList());
        
        // 配置函数执行结果
        RunResult runResult = new RunResult();
        runResult.setSuccess(true);
        runResult.setFunctionResult("success");
        
        when(functionLogicService.executeUDefFunction(eq(testUser), eq(testFunction), any(Map.class),
                eq(testObjectData), any(Map.class)))
                .thenReturn(runResult);

        // When
        ButtonExecutor.Result result = customFuncAction.invoke(testArg, testContext);

        // Then
        assertNotNull(result, "结果不应为null");
        verify(functionLogicService).findUDefFunction(testUser, functionApiName, DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法处理复杂的函数参数
     */
    @Test
    @DisplayName("invoke - 复杂函数参数处理")
    void testInvoke_ComplexFunctionParameters() {
        // Given
        String actionParameterJson = "{\n"
                + "    \"func_api_name\": \"" + FUNCTION_API_NAME + "\",\n"
                + "    \"func_args\": [\n"
                + "        {\n"
                + "            \"name\": \"stringParam\",\n"
                + "            \"type\": \"text\",\n"
                + "            \"value\": \"string value\"\n"
                + "        },\n"
                + "        {\n"
                + "            \"name\": \"numberParam\",\n"
                + "            \"type\": \"number\",\n"
                + "            \"value\": \"42\"\n"
                + "        },\n"
                + "        {\n"
                + "            \"name\": \"booleanParam\",\n"
                + "            \"type\": \"boolean\",\n"
                + "            \"value\": \"true\"\n"
                + "        }\n"
                + "    ]\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        
        // 配置函数查找
        when(functionLogicService.findUDefFunction(testUser, FUNCTION_API_NAME, DESCRIBE_API_NAME))
                .thenReturn(testFunction);
        
        // 配置复杂参数处理
        AbstractFuncAction.FunctionArg stringArg = new AbstractFuncAction.FunctionArg();
        stringArg.setName("stringParam");
        stringArg.setRealValue("processed string value");
        
        AbstractFuncAction.FunctionArg numberArg = new AbstractFuncAction.FunctionArg();
        numberArg.setName("numberParam");
        numberArg.setRealValue(42);
        
        AbstractFuncAction.FunctionArg booleanArg = new AbstractFuncAction.FunctionArg();
        booleanArg.setName("booleanParam");
        booleanArg.setRealValue(true);
        
        when(argumentProcessorService.processArguments(any(), any(), any(), any(), any(), any()))
                .thenReturn(java.util.Arrays.asList(stringArg, numberArg, booleanArg));
        
        // 配置函数执行结果
        RunResult runResult = new RunResult();
        runResult.setSuccess(true);
        runResult.setFunctionResult("complex function executed");
        
        when(functionLogicService.executeUDefFunction(eq(testUser), eq(testFunction), any(Map.class),
                eq(testObjectData), any(Map.class)))
                .thenReturn(runResult);

        // When
        ButtonExecutor.Result result = customFuncAction.invoke(testArg, testContext);

        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isHasReturnValue(), "应有返回值");
        assertEquals("complex function executed", result.getReturnValue(), "返回值应正确");

        // 验证参数处理被调用
        verify(argumentProcessorService).processArguments(any(), any(), any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法处理不同的返回类型
     */
    @Test
    @DisplayName("invoke - 不同返回类型处理")
    void testInvoke_DifferentReturnTypes() {
        // Given
        String actionParameterJson = "{\n"
                + "    \"func_api_name\": \"" + FUNCTION_API_NAME + "\",\n"
                + "    \"func_args\": []\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        
        // 配置函数查找
        when(functionLogicService.findUDefFunction(testUser, FUNCTION_API_NAME, DESCRIBE_API_NAME))
                .thenReturn(testFunction);
        
        // 配置参数处理
        when(argumentProcessorService.processArguments(any(), any(), any(), any(), any(), any()))
                .thenReturn(Collections.emptyList());
        
        // 配置UIAction类型的返回结果
        RunResult runResult = new RunResult();
        runResult.setSuccess(true);
        runResult.setReturnType("UIAction");
        
        Map<String, Object> uiActionResult = new HashMap<>();
        uiActionResult.put("action", "WebAction");
        uiActionResult.put("url", "https://example.com");
        runResult.setFunctionResult(uiActionResult);
        
        when(functionLogicService.executeUDefFunction(eq(testUser), eq(testFunction), any(Map.class),
                eq(testObjectData), any(Map.class)))
                .thenReturn(runResult);

        // When
        ButtonExecutor.Result result = customFuncAction.invoke(testArg, testContext);

        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isHasReturnValue(), "应有返回值");
        assertEquals("UIAction", result.getReturnType(), "返回类型应为UIAction");
        assertEquals(uiActionResult, result.getReturnValue(), "返回值应为UIAction对象");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法处理空返回值
     */
    @Test
    @DisplayName("invoke - 空返回值处理")
    void testInvoke_NullReturnValue() {
        // Given
        String actionParameterJson = "{\n"
                + "    \"func_api_name\": \"" + FUNCTION_API_NAME + "\",\n"
                + "    \"func_args\": []\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        
        // 配置函数查找
        when(functionLogicService.findUDefFunction(testUser, FUNCTION_API_NAME, DESCRIBE_API_NAME))
                .thenReturn(testFunction);
        
        // 配置参数处理
        when(argumentProcessorService.processArguments(any(), any(), any(), any(), any(), any()))
                .thenReturn(Collections.emptyList());
        
        // 配置空返回值
        RunResult runResult = new RunResult();
        runResult.setSuccess(true);
        runResult.setFunctionResult(null);
        
        when(functionLogicService.executeUDefFunction(eq(testUser), eq(testFunction), any(Map.class),
                eq(testObjectData), any(Map.class)))
                .thenReturn(runResult);
        
        // When
        ButtonExecutor.Result result = customFuncAction.invoke(testArg, testContext);
        
        // Then
        assertNotNull(result, "结果不应为null");
        assertFalse(result.isHasReturnValue(), "不应有返回值");
        assertNull(result.getReturnValue(), "返回值应为null");
    }
}
