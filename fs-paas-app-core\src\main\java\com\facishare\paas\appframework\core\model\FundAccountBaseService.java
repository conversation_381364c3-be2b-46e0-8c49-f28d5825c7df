package com.facishare.paas.appframework.core.model;

import com.facishare.crm.openapi.Utils;
import com.facishare.enterprise.common.util.JsonUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.button.action.ButtonExecutorContext;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.release.FsGrayRelease;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.TypeUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class FundAccountBaseService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private InfraServiceFacade infraServiceFacade;
    @Autowired
    private ConfigService configService;
    @Autowired
    private FAccountAuthorizationService fAccountAuthorizationService;

    public void checkEnterAccount(List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        for (IObjectData objectData : dataList) {
            String enterIntoAccountFieldName = Const.ENTER_INTO_ACCOUNT;
            Boolean enterIntoAccount = objectData.get(enterIntoAccountFieldName, Boolean.class, Boolean.FALSE);
            if (enterIntoAccount) {
                throw new ValidateException(I18N.text(Const.NOT_DUPLICATE_ENTRY, objectData.getName()));
            }
            checkLifeStatus(objectData);
        }
    }

    public void checkCancelEntry(User user, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        boolean accountCheckRuleEnable = accountCheckRuleEnable(user);
        Map<String, IObjectData> key2CustomerAccountMap = Maps.newHashMap();
        Map<String, BigDecimal> key2AmountMap = Maps.newHashMap();
        for (IObjectData objectData : dataList) {
            String objectApiName = objectData.getDescribeApiName();
            String enterIntoAccountFieldName = Const.ENTER_INTO_ACCOUNT;
            Boolean enterIntoAccount = objectData.get(enterIntoAccountFieldName, Boolean.class, Boolean.FALSE);
            if (!enterIntoAccount) {
                throw new ValidateException(I18N.text(Const.UN_ENTRY_NOT_CANCEL, objectData.getName()));
            }
            checkLifeStatus(objectData);
            FundAuthConfigModel fundAuthConfigModel = getAccessAuthInfo(user, objectApiName);
            String enterIntoAccountAmountFieldName = fundAuthConfigModel.getEnterAccountAmountFieldName();
            String customerFieldName = fundAuthConfigModel.getCustomerFieldName();
            String fundAccountFieldName = Const.FUND_ACCOUNT;
            BigDecimal enterIntoAccountAmount = objectData.get(enterIntoAccountAmountFieldName, BigDecimal.class, BigDecimal.ZERO);
            String customerId = objectData.get(customerFieldName, String.class);
            String fundAccountId = objectData.get(fundAccountFieldName, String.class);
            String key = String.format("%s-%s", fundAccountId, customerId);
            IObjectData customerAccountData = key2CustomerAccountMap.get(key);
            if (Objects.isNull(customerAccountData)) {
                customerAccountData = getOrCreateNewCustomerAccountData(user, fundAccountId, customerId);
                key2CustomerAccountMap.put(key, customerAccountData);
            }
            BigDecimal amount = key2AmountMap.computeIfAbsent(key, k -> BigDecimal.ZERO);
            key2AmountMap.put(key, amount.add(enterIntoAccountAmount));
        }
        key2CustomerAccountMap.forEach((k, customerAccountData) -> {
            BigDecimal availableAmount = customerAccountData.get(accountCheckRuleEnable ? Const.AVAILABLE_BALANCE : Const.ACCOUNT_BALANCE, BigDecimal.class, BigDecimal.ZERO);
            BigDecimal amount = key2AmountMap.get(k);
            if (amount.compareTo(availableAmount) > 0) {
                log.warn("user:{},amount:{},customerAccountData:{}", user, amount, customerAccountData);
                throw new ValidateException(I18N.text(Const.CUSTOMER_ACCOUNT_NOT_ENOUGH, customerAccountData.getName()));
            }
        });
    }

    public boolean accountCheckRuleEnable(User user) {
        String value = configService.findTenantConfig(user, "is_account_check_enable");
        return "2".equals(value);
    }

    public boolean enableCustomerAccountExceed(User user) {
        String value = configService.findTenantConfig(user, "is_customer_account_exceed_enable");
        return "2".equals(value);
    }

    public IObjectData getOrCreateNewCustomerAccountData(User user, String fundAccountId, String customerId) {
        User queryUser = user.isOutUser() ? User.systemUser(user.getTenantId()) : user;
        IObjectData resultData;
        String key = String.format("NCA_%s_%s_%s", user.getTenantId(), customerId, fundAccountId);
        RLock rLock = null;
        do {
            try {
                rLock = infraServiceFacade.tryLock(3L, 30L, TimeUnit.SECONDS, key);
                if (rLock != null) {
                    //加锁成功，先查一次，没有再新建
                    log.info("NCALock success key:{}", key);
                    List<IFilter> filterList = Lists.newArrayList();
                    fillFilterEq(filterList, Const.CUSTOMER, customerId);
                    fillFilterEq(filterList, Const.FUND_ACCOUNT, fundAccountId);
                    SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
                    searchTemplateQuery.setSearchSource("db");
                    searchTemplateQuery.setFilters(filterList);
                    searchTemplateQuery.setOffset(0);
                    searchTemplateQuery.setLimit(10);
                    QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(queryUser, Const.NEW_CUSTOMER_ACCOUNT_OBJ, searchTemplateQuery);
                    if (CollectionUtils.notEmpty(queryResult.getData())) {
                        resultData = queryResult.getData().get(0);
                        log.info("NCALock success, data exist,key:{},data:{}", key, queryResult.getData());
                    } else {
                        IObjectData objectData = new ObjectData();
                        objectData.setOwner(Lists.newArrayList(user.getUserId()));
                        objectData.setDescribeApiName(Const.NEW_CUSTOMER_ACCOUNT_OBJ);
                        objectData.setTenantId(user.getTenantId());
                        objectData.setRecordType("default__c");
                        objectData.setDeleted(false);
                        objectData.setCreatedBy(user.getUserId());
                        objectData.setCreateTime(System.currentTimeMillis());
                        objectData.set(Const.FUND_ACCOUNT, fundAccountId);
                        objectData.set(Const.CUSTOMER, customerId);
                        objectData.set(Const.ACCOUNT_BALANCE, BigDecimal.ZERO);
                        // 支持外部身份创建 客户账户余额数据
                        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
                        objectDataExt.setOutUser(user);
                        resultData = serviceFacade.saveObjectData(user, objectData);
                        resultData = triggerFunctionByInnerAdd(user, Lists.newArrayList(resultData), "post").get(0);
                    }
                    break;
                } else {
                    //加锁失败
                    log.info("NCALock fail key:{}", key);
                }
            } finally {
                if (rLock != null) {
                    log.info("NCAUnlock:{}", key);
                    infraServiceFacade.unlock(rLock);
                }
            }
        } while (true);
        return resultData;
    }

    public List<IObjectData> triggerFunctionByInnerAdd(User user, List<IObjectData> objectDataList, String stage) {
        if (CollectionUtils.empty(objectDataList)) {
            return objectDataList;
        }
        String describeApiName = objectDataList.get(0).getDescribeApiName();
        IObjectDescribe objectDescribe = serviceFacade.findObject(user.getTenantId(), describeApiName);
        IUdefButton udefButton = this.serviceFacade.findButtonByApiName(user, ObjectAction.CREATE.getButtonApiName(), describeApiName);
        if (CollectionUtils.empty(udefButton.getActions())) {
            return objectDataList;
        }
        List<String> dataIds = com.google.common.collect.Lists.newArrayList();
        objectDataList.forEach(objectData -> {
            ButtonExecutor.Arg executorArg = ButtonExecutor.Arg.ofTriggerFunction(objectData, Maps.newHashMap(), Maps.newHashMap(), Maps.newHashMap());
            ButtonExecutorContext executorContext = ButtonExecutorContext.builder().user(user).describe(objectDescribe).button(udefButton).stage(stage).build();
            this.infraServiceFacade.triggerFunctionAction(executorArg, executorContext);
            dataIds.add(objectData.getId());
        });
        if ("post".equals(stage)) {
            return serviceFacade.findObjectDataByIds(user.getTenantId(), dataIds, describeApiName);
        }
        return objectDataList;
    }

    public void addLedgerData(User user, Map<String, Map<String, Object>> dataColumnMap, boolean isCredit) {
        if (isCredit || !allowReconciliation(user.getTenantId())) {
            return;
        }

        List<IObjectData> ledgerDataList = Lists.newArrayList();
        dataColumnMap.forEach((dataId, columnMap) -> {
            ObjectDataExt columnDataExt = ObjectDataExt.of(columnMap);
            IObjectData data = getBaseObjectData(user, "ReconciliationLedgerObj");
            data.set("biz_type", "0");
            data.set("biz_status", "0");
            data.set("biz_module", "customer_account_reconciliation");
            data.set("biz_object_api_name", Const.NEW_CUSTOMER_ACCOUNT_OBJ);
            data.set("biz_object_data_id", dataId);
            data.set("occur_time", System.currentTimeMillis());
            data.set("amount_change", columnDataExt.get(Const.ACCOUNT_BALANCE, BigDecimal.class, BigDecimal.ZERO));
            data.set("available_amount_change", columnDataExt.get(Const.AVAILABLE_BALANCE, BigDecimal.class, BigDecimal.ZERO));
            data.set("occupied_amount_change", columnDataExt.get("occupied_amount", BigDecimal.class, BigDecimal.ZERO));
            ledgerDataList.add(data);
        });
        serviceFacade.bulkSaveObjectData(ledgerDataList, user);
    }

    public static IObjectData getBaseObjectData(User user, String objectApiName) {
        IObjectData objectData = new ObjectData();
        objectData.setTenantId(user.getTenantId());
        objectData.setDescribeApiName(objectApiName);
        objectData.setCreateTime(System.currentTimeMillis());
        if (user.isOutUser()) {
            objectData.setOutTenantId(user.getOutTenantId());
            objectData.setOutOwner(Lists.newArrayList(user.getOutUserId()));
            objectData.setCreatedBy(user.getOutUserId());
            if (StringUtils.isNotEmpty(user.getUpstreamOwnerId())) {
                objectData.setOwner(Lists.newArrayList(user.getUpstreamOwnerId()));
            } else {
                objectData.setOwner(Lists.newArrayList(User.SUPPER_ADMIN_USER_ID));
            }
        } else {
            objectData.setOwner(Lists.newArrayList(user.getUserId()));
            objectData.setCreatedBy(user.getUserId());
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        objectDataExt.setDefaultTeamMember();
        objectDataExt.setDefaultOutOwner2TeamMember();
        return objectData;
    }

    public FundAuthConfigModel getAccessAuthInfo(User user, String objectApiName) {
        String tenantId = user.getTenantId();
        FundAuthConfigModel accessAuthModel = new FundAuthConfigModel();
        accessAuthModel.setObjectApiName(objectApiName);
        accessAuthModel.setTenantId(tenantId);
        if ("TPMDealerActivityCostObj".equals(objectApiName)) {
            accessAuthModel.setEnterAccountAmountFieldName("confirmed_amount");
            accessAuthModel.setCustomerFieldName("dealer_id");
            return accessAuthModel;
        }

        IObjectData fAccountAuthorizationData = fAccountAuthorizationService.getFAccountAuthorizationData(user, objectApiName, "income");
        if (fAccountAuthorizationData == null) {
            throw new ValidateException(I18N.text(FAccountAuthorizationService.Const.FACCOUNT_AUTH_NOT_EXIST, objectApiName));
        }
        String status = fAccountAuthorizationData.get("status", String.class);
        if (!Objects.equals(status, "has_init")) {
            throw new ValidateException(I18N.text(FAccountAuthorizationService.Const.FACCOUNT_AUTH_NOT_INIT, objectApiName));
        }
        String enterAccountAmountFieldName = fAccountAuthorizationData.get("entry_amount_fieldapiname", String.class);
        String customerFieldName = fAccountAuthorizationData.get("entry_customer_fieldapiname", String.class);

        accessAuthModel.setEnterAccountAmountFieldName(enterAccountAmountFieldName);
        accessAuthModel.setCustomerFieldName(customerFieldName);
        return accessAuthModel;

       /* String configKey = String.format("EnterAccount_%s", objectApiName);
        String configValue = configService.findTenantConfig(user, configKey);
        if (StringUtils.isEmpty(configValue)) {
            String key = String.format("%s-%s", tenantId, objectApiName);
            accessAuthModel = FUND_ACCOUNT_ACCESS_AUTH_CONFIG_MAP.get(key);
            if (Objects.nonNull(accessAuthModel)) {
                return accessAuthModel;
            } else {
                return FUND_ACCOUNT_ACCESS_AUTH_CONFIG_MAP.get(objectApiName);
            }
        }
        accessAuthModel = JsonUtil.fromJson(configValue, FundAuthConfigModel.class);
        accessAuthModel.setTenantId(tenantId);
        accessAuthModel.setObjectApiName(objectApiName);
        return accessAuthModel;*/
    }

    /**
     * 校验：fundAccountId是否在入账授权的授权明细里面
     */
    public void checkFundAccountId(String tenantId, String authorizedObject, String fundAccountId, String fundAccountName) {
        List<IObjectData> authDetails = query(tenantId, "income", authorizedObject);
        if (CollectionUtils.empty(authDetails)) {
            throw new ValidateException(I18N.text(FAccountAuthorizationService.Const.FUND_ACCOUNT_NOT_IN_AUTH_DETAIL, fundAccountName));
        }

        for (IObjectData authDetail : authDetails) {
            String authorizeAccountId = authDetail.get("authorize_account_id", String.class);
            if (Objects.equals(authorizeAccountId, fundAccountId)) {
                return;
            }
        }

        throw new ValidateException(I18N.text(FAccountAuthorizationService.Const.FUND_ACCOUNT_NOT_IN_AUTH_DETAIL, fundAccountName));
    }

    /**
     * 查询入账授权明细
     */
    public List<IObjectData> query(String tenantId, String authorizedType, String authorizedObject) {
        User admin = new User(tenantId, "-10000");
        return query(admin, authorizedType, authorizedObject);
    }

    public List<IObjectData> query(User user, String authorizedType, String authorizedObjectApiName) {
        List<IFilter> filterList = Lists.newArrayList();

        IFilter authorizedTypeFilter = new Filter();
        authorizedTypeFilter.setFieldName("authorized_type");
        authorizedTypeFilter.setFieldValues(Lists.newArrayList(authorizedType));
        authorizedTypeFilter.setOperator(Operator.EQ);
        //  authorizedTypeFilter.setValueType(7);
        authorizedTypeFilter.setIsMasterField(true);
        filterList.add(authorizedTypeFilter);

        IFilter authorizedObjectFilter = new Filter();
        authorizedObjectFilter.setFieldName("authorized_object_api_name");
        authorizedObjectFilter.setFieldValues(Lists.newArrayList(authorizedObjectApiName));
        authorizedObjectFilter.setOperator(Operator.IN);
        //  authorizedObjectFilter.setValueType(7);
        authorizedObjectFilter.setIsMasterField(true);
        filterList.add(authorizedObjectFilter);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(200);

        return serviceFacade.findBySearchQuery(user, "AuthorizationDetailObj", query).getData();
    }

    public static void fillFilterEq(List<IFilter> filters, String fieldName, String value) {
        IFilter filter = new Filter();
        filter.setOperator(Operator.EQ);
        filter.setFieldName(fieldName);
        filter.setFieldValues(Lists.newArrayList(value));
        filters.add(filter);
    }

    public static void fillFilterIn(List<IFilter> filters, String fieldName, List<String> values) {
        IFilter filter = new Filter();
        filter.setOperator(Operator.IN);
        filter.setFieldName(fieldName);
        filter.setFieldValues(values);
        filters.add(filter);
    }

    public static Map<String, Map<String, Object>> buildCustomerUpdateMapAndCheck(IObjectData customerAccountData, BigDecimal changedAccountBalance, boolean isCreditFundAccount, boolean accountCheckRuleEnable, boolean enableCustomerAccountExceed) {
        Map<String, Map<String, Object>> dataColumnMap = Maps.newHashMap();
        Map<String, Object> columnMap = Maps.newHashMap();
        columnMap.put(FundAccountBaseService.Const.ACCOUNT_BALANCE, changedAccountBalance);
        String compareAmountField;
        if (isCreditFundAccount) {
            compareAmountField = FundAccountBaseService.Const.ACCOUNT_BALANCE;
            columnMap.put(FundAccountBaseService.Const.CREDIT_OCCUPIED_AMOUNT, changedAccountBalance.negate());
        } else if (accountCheckRuleEnable) {
            compareAmountField = FundAccountBaseService.Const.AVAILABLE_BALANCE;
            columnMap.put(FundAccountBaseService.Const.AVAILABLE_BALANCE, changedAccountBalance);
        } else {
            compareAmountField = FundAccountBaseService.Const.ACCOUNT_BALANCE;
        }
        dataColumnMap.put(customerAccountData.getId(), columnMap);
        BigDecimal availableAmount = customerAccountData.get(compareAmountField, BigDecimal.class, BigDecimal.ZERO);
        if (availableAmount.compareTo(changedAccountBalance.negate()) < 0 && !enableCustomerAccountExceed) {
            log.warn("customerAccountNotEnough:{},revenueAmount:{}", customerAccountData, changedAccountBalance);
            throw new ValidateException(I18N.text(FundAccountBaseService.Const.CUSTOMER_ACCOUNT_NOT_ENOUGH, customerAccountData.getName()));
        }
        return dataColumnMap;
    }

    public static void checkLifeStatus(IObjectData objectData) {
        String lifeStatus = ObjectDataExt.of(objectData).getLifeStatusText();
        if (!ObjectLifeStatus.NORMAL.getCode().equals(lifeStatus)) {
            throw new ValidateException(I18N.text(Const.NOT_NORMAL_NOT_SUPPORT_ACTION));
        }
    }

    public static IObjectData toIncomeFlowData(User user, IObjectData fundAccountData, String customerId, IObjectData customerAccountData, IObjectData sourceObjectData, BigDecimal enterAccountAmount, FundAuthConfigModel fundAuthConfigModel, Boolean isAutoEnterAccount) {
        IObjectData accountFlowData = new ObjectData();
        accountFlowData.setTenantId(user.getTenantId());
        accountFlowData.setDescribeApiName(Const.ACCOUNT_TRANSACTION_FLOW_OBJ);
        accountFlowData.setCreateTime(System.currentTimeMillis());
        List<String> owner = Lists.newArrayList();
        if (user.isOutUser()) {
            accountFlowData.setOutTenantId(user.getOutTenantId());
            accountFlowData.setOutOwner(Lists.newArrayList(user.getOutUserId()));
            accountFlowData.setCreatedBy(user.getOutUserId());
            if (StringUtils.isNotEmpty(user.getUpstreamOwnerId())) {
                owner.add(user.getUpstreamOwnerId());
            }
        } else {
            owner.add(user.getUserId());
            accountFlowData.setCreatedBy(user.getUserId());
        }
        List<String> sourceOwner = sourceObjectData.getOwner();
        if ((owner.isEmpty() || owner.contains(User.SUPPER_ADMIN_USER_ID)) && CollectionUtils.notEmpty(sourceOwner)) {
            owner = sourceOwner;
        }
        if (CollectionUtils.empty(owner)) {
            owner.add(User.SUPPER_ADMIN_USER_ID);
        }
        accountFlowData.setOwner(owner);

        List<AccessFieldMapping> fieldMappingList = fundAuthConfigModel.getFieldMappingList();
        fieldMappingList.forEach(x -> {
            String targetApiName = x.getTargetFieldName();
            if (!BooleanUtils.isTrue(x.getIsCustomerField()) && !BooleanUtils.isTrue(x.getIsEnterAccountAmountField()) && StringUtils.isNotEmpty(targetApiName)) {
                String srcApiName = x.getSourceFieldName();
                accountFlowData.set(targetApiName, sourceObjectData.get(srcApiName));
            }
        });
        String fundAccountId = fundAccountData.getId();
        String accessModule = fundAccountData.get(Const.FIELD_ACCESS_MODULE, String.class);
        accountFlowData.set(Const.FUND_ACCOUNT, fundAccountId);
        accountFlowData.set(Const.FIELD_ACCESS_MODULE, accessModule);
        accountFlowData.setRecordType(Const.FLOW_INCOME_RECORD_TYPE);
        accountFlowData.set(Const.TRANSACTION_DATE, System.currentTimeMillis());
        accountFlowData.set(Const.CUSTOMER_ACCOUNT, customerAccountData.getId());
        accountFlowData.set(Const.RELATE_RECORD_OBJECT, sourceObjectData.getDescribeApiName());
        accountFlowData.set(Const.RELATE_RECORD_DATA_ID, sourceObjectData.getId());
        accountFlowData.set(Const.REVENUE_TYPE, getRevenueType(sourceObjectData.getDescribeApiName()));
        accountFlowData.set(Const.REVENUE_SOURCE, isAutoEnterAccount ? RevenueSourceConst.AUTOMATIC_POSTING : RevenueSourceConst.MANUAL_POSTING);
        accountFlowData.set(Const.ENTRY_STATUS, Const.ENTRY_STATUS_ENTERED);
        accountFlowData.set(Const.CUSTOMER, customerId);
        accountFlowData.set(Const.REVENUE_AMOUNT, enterAccountAmount);
        if (Utils.CUSTOMER_PAYMENT_API_NAME.equals(sourceObjectData.getDescribeApiName())) {
            accountFlowData.set(Const.PAYMENT_ID, sourceObjectData.getId());
        }
        BigDecimal accountBalance = customerAccountData.get(Const.ACCOUNT_BALANCE, BigDecimal.class, BigDecimal.ZERO);
        accountFlowData.set(Const.ACCOUNT_BALANCE, accountBalance.add(enterAccountAmount));
        ObjectDataExt objectDataExt = ObjectDataExt.of(accountFlowData);
        objectDataExt.setDefaultTeamMember();
        objectDataExt.setDefaultOutOwner2TeamMember();
        return accountFlowData;
    }

    public static String getRevenueType(String objectApiName) {
        String revenueType;
        if (ObjectDescribeExt.isCustomObject(objectApiName)) {
            //自定义对象接入，统一用一个通用枚举
            revenueType = Const.FLOW_REVENUE_UDF_TYPE;
        } else if (Const.TPM_ACTIVITY_COST_API_NAME.equals(objectApiName)) {
            revenueType = Const.FLOW_REVENUE_FEE_TYPE;
            //其他预设对象接入，新增其他
        } else if (Utils.CUSTOMER_PAYMENT_API_NAME.equals(objectApiName)) {
            //回款入账
            revenueType = Const.FLOW_REVENUE_PAYMENT_CHARGE_TYPE;
        } else if ("RebateObj".equals(objectApiName)) {
            revenueType = Const.FLOW_REVENUE_REBATE_TYPE;
        } else {
            revenueType = Const.FLOW_REVENUE_UDF_TYPE;
        }
        return revenueType;
    }

    public static boolean enableNegativeEnterAccount(String tenantId, String objectApiName) {
        return FsGrayRelease.isAllow("sail", "enableNegativeEnterAccount", objectApiName + "." + tenantId);
    }

    public static boolean allowReconciliation(String tenantId) {
        return FsGrayRelease.isAllow("customeraccount", "enableBizReconciliationEis", tenantId);
    }

    /**
     * 是否为货补数量账户，true货补数量
     */
    public static boolean isReplenishmentQuantity(IObjectData fundAccountData) {
        //1：默认，2：返利
        String accessModule = fundAccountData.get("access_module", String.class, "1");
        //1：金额，2：货补金额，3：货补数量
        String accountType = fundAccountData.get("account_type", String.class, "1");
        return "2".equals(accessModule) && "3".equals(accountType);
    }

    private static final Map<String, FundAuthConfigModel> FUND_ACCOUNT_ACCESS_AUTH_CONFIG_MAP = Maps.newHashMap();
    private static final Set<String> ENABLE_CUSTOMER_ACCOUNT_EXCEED_TENANT_IDS = Sets.newHashSet();

    static {
        ConfigFactory.getConfig("fs-crm-customeraccount", config -> {
            String grayTenantIdStr = config.get("enableCustomerAccountExceedTenantIds", "");
            if (StringUtils.isNotEmpty(grayTenantIdStr)) {
                ENABLE_CUSTOMER_ACCOUNT_EXCEED_TENANT_IDS.addAll(Arrays.stream(grayTenantIdStr.split(",")).collect(Collectors.toList()));
            }
            String fundAuthConfig = config.get("fundAuthConfig");
            Type type = TypeUtils.parameterize(List.class, FundAuthConfigModel.class);
            List<FundAuthConfigModel> configList = JsonUtil.fromJson(fundAuthConfig, type);
            if (Objects.nonNull(configList)) {
                configList.forEach(x -> {
                    x.init();
                    if (x.getIsDefault()) {
                        FUND_ACCOUNT_ACCESS_AUTH_CONFIG_MAP.put(x.getObjectApiName(), x);
                    } else if (StringUtils.isNotEmpty(x.getTenantId())) {
                        FUND_ACCOUNT_ACCESS_AUTH_CONFIG_MAP.put(String.format("%s-%s", x.getTenantId(), x.getObjectApiName()), x);
                    }
                });
            }
        });
    }

    @Data
    public static class FundAuthConfigModel {
        private String tenantId;
        private String objectApiName;
        private Boolean isDefault = Boolean.FALSE;
        private List<AccessFieldMapping> fieldMappingList = Lists.newArrayList();

        private String customerFieldName;
        private String enterAccountAmountFieldName;

        private void init() {
            for (AccessFieldMapping fieldMapping : fieldMappingList) {
                if (BooleanUtils.isTrue(fieldMapping.getIsCustomerField())) {
                    customerFieldName = fieldMapping.getSourceFieldName();
                }
                if (BooleanUtils.isTrue(fieldMapping.getIsEnterAccountAmountField())) {
                    enterAccountAmountFieldName = fieldMapping.getSourceFieldName();
                }
            }
        }
    }

    @Data
    public static class AccessFieldMapping {
        private String sourceFieldName;
        private String targetFieldName;
        private Boolean isRequired;
        private Boolean isCustomerField;
        private Boolean isEnterAccountAmountField;
    }

    public interface Const {
        String NEW_CUSTOMER_ACCOUNT_OBJ = "NewCustomerAccountObj";
        String ACCOUNT_TRANSACTION_FLOW_OBJ = "AccountTransactionFlowObj";
        String TPM_ACTIVITY_COST_API_NAME = "TPMDealerActivityCostObj";

        String PAYMENT_ID = "payment_id";
        String FUND_ACCOUNT = "fund_account_id";
        String IS_AUTO_ENTER_ACCOUNT = "is_auto_enter_account";
        String CUSTOMER = "customer_id";
        String ACCOUNT_BALANCE = "account_balance";
        String AVAILABLE_BALANCE = "available_balance";

        String CREDIT_OCCUPIED_AMOUNT = "credit_occupied_amount";

        String CUSTOMER_ACCOUNT = "customer_account_id";
        String RELATE_RECORD_OBJECT = "relate_object_api_name";
        String RELATE_RECORD_DATA_ID = "relate_object_data_id";
        String REVENUE_TYPE = "revenue_type";
        //RevenueSource("revenue_source", "收入来源"),
        String REVENUE_SOURCE = "revenue_source";
        String ENTRY_STATUS = "entry_status";
        String REVENUE_AMOUNT = "revenue_amount";
        String TRANSACTION_DATE = "transaction_date";

        String ENTRY_STATUS_ENTERED = "1";
        String ENTRY_STATUS_CANCEL = "2";
        String FLOW_INCOME_RECORD_TYPE = "income_record_type__c";
        String FLOW_REVENUE_FEE_TYPE = "4";
        String FLOW_REVENUE_PAYMENT_CHARGE_TYPE = "1";
        String FLOW_REVENUE_REBATE_TYPE = "rebate_entry";
        //自定义对象接入时，通用的入账类型枚举
        String FLOW_REVENUE_UDF_TYPE = "biz_entry";
        //接入模块字段apiName，FundAccountObj与AccountTransactionFlowObj字段apiName相同
        String FIELD_ACCESS_MODULE = "access_module";
        String ENTER_INTO_ACCOUNT = "enter_into_account";

        String NOT_DUPLICATE_ENTRY = "fundaccountobj.action.validate.not_duplicate_entry";
        String UN_ENTRY_NOT_CANCEL = "fundaccountobj.action.validate.un_entry_not_cancel";
        String CUSTOMER_ACCOUNT_NOT_ENOUGH = "accounttransactionflowobj.action.validate.customerAccountNotEnough";
        String ENTER_AMOUNT_MUST_GT_ZERO = "fundaccountobj.service.validate.enter_account_amount_must_gt_zero";
        String NOT_NORMAL_NOT_SUPPORT_ACTION = "fundaccountobj.action.validate.not_normal_not_action";
        String NO_CUSTOMER_NOT_ENTRY = "payment.noSelectAccountCannotEnterIntoAccount";

        String CHARGE_OFF_NOT_CANCEL_ENTRY = "fundaccountobj.action.validate.charge_off_not_cancel_entry";
    }

    /**
     * 收支流水的RevenueSource("revenue_source", "收入来源"),
     */
    public interface RevenueSourceConst {
        //手动入账
        String MANUAL_POSTING = "manual_posting";
        //自动入账
        String AUTOMATIC_POSTING = "automatic_posting";
    }
}
