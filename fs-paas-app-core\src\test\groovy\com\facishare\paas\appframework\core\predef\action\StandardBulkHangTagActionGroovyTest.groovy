package com.facishare.paas.appframework.core.predef.action

import com.facishare.paas.I18N
import com.facishare.paas.appframework.common.util.ObjectAction
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.i18n.I18NKey
import com.facishare.paas.appframework.core.model.ActionContext
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * StandardBulkHangTagAction的单元测试类
 */
class StandardBulkHangTagActionGroovyTest extends Specification {
    def bulkHangTagAction = new StandardBulkHangTagAction(
            serviceFacade: Mock(ServiceFacade),
            actionContext: new ActionContext(RequestContext.builder()
                    .tenantId("testTenantId")
                    .user(new User("testTenantId", "testUserId"))
                    .build(), "testObjectApiName", "testBulkHangTag"),
            objectDescribe: Mock(IObjectDescribe)
    )

    /**
     * GenerateByAI
     * 测试内容描述：测试getFuncPrivilegeCodes方法返回null
     */
    def "getFuncPrivilegeCodesTest"() {
        expect:
        bulkHangTagAction.getFuncPrivilegeCodes() == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDataIdByParam方法正确返回数据ID
     */
    def "getDataIdByParamTest"() {
        given:
        def param = StandardBatchUpdateTagAction.Arg.builder()
                .dataId("testDataId")
                .build()

        expect:
        bulkHangTagAction.getDataIdByParam(param) == "testDataId"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getButtonParams方法正确转换参数
     */
    def "getButtonParamsTest"() {
        given:
        def arg = StandardBulkHangTagAction.Arg.builder()
                .dataIds(["id1", "id2"])
                .tagIds(["tag1", "tag2"])
                .append(true)
                .build()
        bulkHangTagAction.arg = arg

        when:
        def result = bulkHangTagAction.getButtonParams()

        then:
        result.size() == 2
        result[0].dataId == "id1"
        result[0].tagIds == ["tag1", "tag2"]
        result[0].append
        result[1].dataId == "id2"
        result[1].tagIds == ["tag1", "tag2"]
        result[1].append
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getButtonApiName方法返回正确的按钮API名称
     */
    def "getButtonApiNameTest"() {
        expect:
        bulkHangTagAction.getButtonApiName() == ObjectAction.BULK_HANG_TAG.getButtonApiName()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getActionCode方法返回正确的动作代码
     */
    def "getActionCodeTest"() {
        expect:
        bulkHangTagAction.getActionCode() == "BatchUpdateTag"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试before方法的基本功能
     */
    def "beforeTest"() {
        given:

        def arg = StandardBulkHangTagAction.Arg.builder()
                .dataIds(["id1", "id2"])
                .tagIds(["tag1", "tag2"])
                .append(true)
                .build()

        when:
        bulkHangTagAction.validateArg(arg)

        then:
        noExceptionThrown()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试validateArg方法对空参数的处理
     */
    @Unroll
    def "validateArgErrorWithEmptyParam"() {
        when:
        bulkHangTagAction.validateArg(arg)

        then:
        def exception = thrown(ValidateException)
        exception.message == I18N.text(I18NKey.PARAM_EMPTY)

        where:
        arg << [
                null,  // 参数为null
                StandardBulkHangTagAction.Arg.builder().build(),  // dataIds为null
                StandardBulkHangTagAction.Arg.builder().dataIds([]).build(),  // dataIds为空列表
                StandardBulkHangTagAction.Arg.builder().dataIds([null]).build(),  // dataIds只包含null
                StandardBulkHangTagAction.Arg.builder().dataIds([null, null]).build()  // dataIds包含多个null
        ]
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试validateArg方法对dataIds中包含null值的过滤处理
     */
    def "validateArgWithMixedNullValues"() {
        given:
        def arg = StandardBulkHangTagAction.Arg.builder()
                .dataIds(["id1", null, "id2", null])
                .tagIds(["tag1", "tag2"])
                .append(true)
                .build()

        when:
        bulkHangTagAction.validateArg(arg)
        
        then:
        noExceptionThrown()
        arg.dataIds == ["id1", "id2"]  // null值应该被过滤掉
    }
} 