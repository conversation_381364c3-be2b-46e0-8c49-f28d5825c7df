package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.button.dto.UpdatesPojo;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.DateTimeUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.GlobalVarService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.dataconvert.DataConvertContext;
import com.facishare.paas.appframework.metadata.dataconvert.FieldDataConverter;
import com.facishare.paas.appframework.metadata.dataconvert.FieldDataConverterManager;
import com.facishare.paas.appframework.metadata.expression.Expression;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.*;
import com.facishare.paas.timezone.DateTimeFormatUtils;
import com.facishare.paas.timezone.TimeZoneContextHolder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.ZoneId;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ParseVarService单元测试 - JUnit5版本
 * 
 * GenerateByAI
 * 
 * 测试ParseVarService类的核心功能：
 * - parseVar: 解析各种类型的变量
 * - parseVarForDisplay: 为显示目的解析变量
 * - getData/getDisplayData: 单个变量数据获取
 * - getVar/getVarList: 变量对象获取
 * - getDataList: 批量数据获取
 * 
 * 覆盖场景：
 * - 全局常量变量解析
 * - 对象字段变量解析
 * - 表单字段变量解析（部门、员工、单选、多选等）
 * - 按钮变量解析（var_executor、var_execution_time）
 * - 当前对象变量解析
 * - 异常处理和边界条件
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("ParseVarService - 变量解析服务测试")
class ParseVarServiceJUnit5Test {

    @Mock
    private GlobalVarService globalVarService;
    
    @Mock
    private FieldDataConverterManager fieldDataConverterManager;
    
    @Mock
    private OrgService orgService;

    @InjectMocks
    private ParseVarService parseVarService;

    // 测试常量
    private static final String TENANT_ID = "74255";
    private static final String USER_ID = "1000";
    private static final String TEST_GLOBAL_VAR = "testVar__g";
    private static final String TEST_FIELD_NAME = "name";
    private static final String TEST_FORM_FIELD = "form_test";

    private User testUser;
    private IObjectData testObjectData;
    private IObjectDescribe testDescribe;
    private IUdefButton testButton;

    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser = User.systemUser(TENANT_ID);
        
        // 创建测试对象数据
        testObjectData = createMockObjectData();
        testObjectData.set(TEST_FIELD_NAME, "test name");
        testObjectData.set("status", "active");
        
        // 创建测试对象描述
        testDescribe = createMockObjectDescribe();
        
        // 创建测试按钮
        testButton = createMockButton();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析全局常量变量的正常场景
     */
    @Test
    @DisplayName("parseVar - 解析全局常量变量")
    void testParseVar_GlobalConstant() {
        // 准备测试数据
        IGlobalVariableDescribe globalVar = createMockGlobalVariable(TEST_GLOBAL_VAR, IFieldType.TEXT, "test value");
        Variable variable = createVariable("$" + TEST_GLOBAL_VAR + "$", Variable.Type.GLOBAL_CONSTANT);
        List<Variable> variables = Arrays.asList(variable);
        
        // 配置Mock行为
        Map<String, IGlobalVariableDescribe> globalVarMap = new HashMap<>();
        globalVarMap.put(TEST_GLOBAL_VAR, globalVar);
        when(globalVarService.findGlobalVariables(TENANT_ID, Arrays.asList(TEST_GLOBAL_VAR)))
                .thenReturn(globalVarMap);
        when(globalVarService.parseValue(globalVar, false)).thenReturn("parsed value");
        
        // 执行测试
        List<Variable> result = parseVarService.parseVar(variables, testUser);
        
        // 验证结果
        assertNotNull(result, "结果不应为null");
        assertEquals(1, result.size(), "应返回1个变量");
        assertEquals("parsed value", result.get(0).getValue(), "变量值应正确解析");
        assertEquals(IFieldType.TEXT, result.get(0).getFieldType(), "字段类型应正确设置");
        
        // 验证Mock调用
        verify(globalVarService).findGlobalVariables(TENANT_ID, Arrays.asList(TEST_GLOBAL_VAR));
        verify(globalVarService).parseValue(globalVar, false);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析对象字段变量的正常场景
     */
    @Test
    @DisplayName("parseVar - 解析对象字段变量")
    void testParseVar_ObjectField() {
        // 准备测试数据
        IFieldDescribe fieldDescribe = createMockFieldDescribe(TEST_FIELD_NAME, IFieldType.TEXT);
        testDescribe.setFieldDescribes(Arrays.asList(fieldDescribe));
        
        Variable variable = createVariable("$" + TEST_FIELD_NAME + "$", Variable.Type.OBJECT_FIELD);
        List<Variable> variables = Arrays.asList(variable);
        
        // 配置Mock行为
        when(globalVarService.findGlobalVariables(TENANT_ID, Collections.emptyList()))
                .thenReturn(Collections.emptyMap());
        
        // 执行测试
        List<Variable> result = parseVarService.parseVar(variables, testUser);
        
        // 验证结果
        assertNotNull(result, "结果不应为null");
        assertEquals(1, result.size(), "应返回1个变量");
        assertEquals("test name", result.get(0).getValue(), "变量值应从对象数据中获取");
        assertEquals(IFieldType.TEXT, result.get(0).getFieldType(), "字段类型应正确设置");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析表单字段变量的正常场景
     */
    @Test
    @DisplayName("parseVar - 解析表单字段变量")
    void testParseVar_VarField() {
        // 准备测试数据
        Map<String, Object> args = new HashMap<>();
        args.put(TEST_FORM_FIELD, "form value");

        Map<String, Object> paramFormItem = new HashMap<>();
        paramFormItem.put(IFieldDescribe.API_NAME, TEST_FORM_FIELD);
        paramFormItem.put(IFieldDescribe.TYPE, IFieldType.TEXT);
        List<Map> paramForm = Arrays.asList(paramFormItem);
        when(testButton.getParamForm()).thenReturn(paramForm);
        
        Variable variable = createVariableWithArgs("$" + TEST_FORM_FIELD + "$", Variable.Type.VAR_FIELD, args);
        List<Variable> variables = Arrays.asList(variable);
        
        // 配置Mock行为
        when(globalVarService.findGlobalVariables(TENANT_ID, Collections.emptyList()))
                .thenReturn(Collections.emptyMap());
        
        // 执行测试
        List<Variable> result = parseVarService.parseVar(variables, testUser);
        
        // 验证结果
        assertNotNull(result, "结果不应为null");
        assertEquals(1, result.size(), "应返回1个变量");
        assertEquals("form value", result.get(0).getValue(), "变量值应从表单参数中获取");
        assertEquals(IFieldType.TEXT, result.get(0).getFieldType(), "字段类型应正确设置");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析按钮变量var_executor的正常场景
     */
    @Test
    @DisplayName("parseVar - 解析按钮变量var_executor")
    void testParseVar_ButtonVariables_Executor() {
        // 准备测试数据
        User userWithId = new User(TENANT_ID, USER_ID);
        Variable variable = createVariable("$var_executor$", Variable.Type.BUTTON_VARIABLES);
        List<Variable> variables = Arrays.asList(variable);
        
        // 配置Mock行为
        when(globalVarService.findGlobalVariables(TENANT_ID, Collections.emptyList()))
                .thenReturn(Collections.emptyMap());
        
        // 执行测试
        List<Variable> result = parseVarService.parseVar(variables, userWithId);
        
        // 验证结果
        assertNotNull(result, "结果不应为null");
        assertEquals(1, result.size(), "应返回1个变量");
        assertEquals(Arrays.asList(USER_ID), result.get(0).getValue(), "变量值应为用户ID列表");
        assertEquals(IFieldType.EMPLOYEE, result.get(0).getFieldType(), "字段类型应为员工类型");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析当前对象变量的正常场景
     */
    @Test
    @DisplayName("parseVar - 解析当前对象变量")
    void testParseVar_CurrentObject() {
        // 准备测试数据
        testObjectData.set("id", "123");
        testObjectData.set("name", "test");
        
        Variable variable = createVariable("$__current_object__$", Variable.Type.CURRENT_OBJECT);
        List<Variable> variables = Arrays.asList(variable);
        
        // 配置Mock行为
        when(globalVarService.findGlobalVariables(TENANT_ID, Collections.emptyList()))
                .thenReturn(Collections.emptyMap());
        
        // 执行测试
        List<Variable> result = parseVarService.parseVar(variables, testUser);
        
        // 验证结果
        assertNotNull(result, "结果不应为null");
        assertEquals(1, result.size(), "应返回1个变量");
        assertTrue(result.get(0).getValue() instanceof Map, "变量值应为Map类型");
        
        @SuppressWarnings("unchecked")
        Map<String, Object> objectMap = (Map<String, Object>) result.get(0).getValue();
        assertTrue(objectMap.containsKey("id"), "对象Map应包含id字段");
        assertTrue(objectMap.containsKey("name"), "对象Map应包含name字段");
    }

    /**
     * GenerateByAI
     * 测试内容描述：参数化测试所有Variable.Type枚举值
     */
    @ParameterizedTest
    @EnumSource(Variable.Type.class)
    @DisplayName("parseVar - 参数化测试所有变量类型")
    void testParseVar_AllVariableTypes(Variable.Type variableType) {
        // 准备测试数据
        String variableName = getVariableNameByType(variableType);
        Variable variable = createVariable(variableName, variableType);
        List<Variable> variables = Arrays.asList(variable);
        
        // 配置通用Mock行为
        setupCommonMockBehaviors(variableType);
        
        // 执行测试
        List<Variable> result = parseVarService.parseVar(variables, testUser);
        
        // 验证结果
        assertNotNull(result, "结果不应为null");
        assertEquals(1, result.size(), "应返回1个变量");
        // 不同类型的具体验证在专门的测试方法中进行
    }

    // 辅助方法：创建Mock对象数据
    private IObjectData createMockObjectData() {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("object_describe_api_name", "TestObj");
        return new ObjectData(dataMap);
    }

    // 辅助方法：创建Mock对象描述
    private IObjectDescribe createMockObjectDescribe() {
        ObjectDescribe describe = new ObjectDescribe();
        describe.setApiName("TestObj");
        describe.setFieldDescribes(Collections.emptyList());
        return describe;
    }

    // 辅助方法：创建Mock按钮
    private IUdefButton createMockButton() {
        IUdefButton button = mock(IUdefButton.class);
        when(button.getApiName()).thenReturn("testButton");
        when(button.getParamForm()).thenReturn(Collections.emptyList());
        return button;
    }

    // 辅助方法：创建Mock全局变量
    private IGlobalVariableDescribe createMockGlobalVariable(String apiName, String type, String value) {
        GlobalVariableDescribe globalVar = new GlobalVariableDescribe();
        globalVar.setApiName(apiName);
        globalVar.setType(type);
        globalVar.setValue(value);
        return globalVar;
    }

    // 辅助方法：创建Mock字段描述
    private IFieldDescribe createMockFieldDescribe(String apiName, String type) {
        Map<String, Object> fieldMap = new HashMap<>();
        fieldMap.put(IFieldDescribe.API_NAME, apiName);
        fieldMap.put(IFieldDescribe.TYPE, type);
        return FieldDescribeFactory.newInstance(fieldMap);
    }

    // 辅助方法：创建变量对象
    private Variable createVariable(String variableName, Variable.Type type) {
        return createVariableWithArgs(variableName, type, Collections.emptyMap());
    }

    // 辅助方法：创建带参数的变量对象
    private Variable createVariableWithArgs(String variableName, Variable.Type type, Map<String, Object> args) {
        String parsedName = Expression.of(variableName).parseVariableNames().get(0);
        Variable variable = new Variable(parsedName, testDescribe, testObjectData, testButton, args);
        variable.setType(type);
        return variable;
    }

    // 辅助方法：根据变量类型获取变量名
    private String getVariableNameByType(Variable.Type type) {
        switch (type) {
            case GLOBAL_CONSTANT:
                return "$testVar__g$";
            case OBJECT_FIELD:
                return "$name$";
            case VAR_FIELD:
                return "$form_test$";
            case BUTTON_VARIABLES:
                return "$var_executor$";
            case CURRENT_OBJECT:
                return "$__current_object__$";
            default:
                return "$unknown$";
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseVarForDisplay方法处理全局常量的正常场景
     */
    @Test
    @DisplayName("parseVarForDisplay - 解析全局常量变量")
    void testParseVarForDisplay_GlobalConstant() {
        // 准备测试数据
        IGlobalVariableDescribe globalVar = createMockGlobalVariable(TEST_GLOBAL_VAR, IFieldType.TEXT, "test value");
        Variable variable = createVariable("$" + TEST_GLOBAL_VAR + "$", Variable.Type.GLOBAL_CONSTANT);
        List<Variable> variables = Arrays.asList(variable);

        // 配置Mock行为
        Map<String, IGlobalVariableDescribe> globalVarMap = new HashMap<>();
        globalVarMap.put(TEST_GLOBAL_VAR, globalVar);
        when(globalVarService.findGlobalVariables(TENANT_ID, Arrays.asList(TEST_GLOBAL_VAR)))
                .thenReturn(globalVarMap);
        when(globalVarService.parseValue(globalVar, false)).thenReturn("parsed value");

        // 执行测试
        List<Variable> result = parseVarService.parseVarForDisplay(variables, testUser);

        // 验证结果
        assertNotNull(result, "结果不应为null");
        assertEquals(1, result.size(), "应返回1个变量");
        assertNotNull(result.get(0).getValue(), "变量值不应为null");
        assertEquals(IFieldType.TEXT, result.get(0).getFieldType(), "字段类型应正确设置");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseVarForDisplay方法处理对象字段的正常场景
     */
    @Test
    @DisplayName("parseVarForDisplay - 解析对象字段变量")
    void testParseVarForDisplay_ObjectField() {
        // 准备测试数据
        IFieldDescribe fieldDescribe = createMockFieldDescribe(TEST_FIELD_NAME, IFieldType.TEXT);
        testDescribe.setFieldDescribes(Arrays.asList(fieldDescribe));

        Variable variable = createVariable("$" + TEST_FIELD_NAME + "$", Variable.Type.OBJECT_FIELD);
        List<Variable> variables = Arrays.asList(variable);

        FieldDataConverter fieldDataConverter = mock(FieldDataConverter.class);

        // 配置Mock行为
        when(globalVarService.findGlobalVariables(TENANT_ID, Collections.emptyList()))
                .thenReturn(Collections.emptyMap());
        when(fieldDataConverterManager.getFieldDataConverter(IFieldType.TEXT))
                .thenReturn(fieldDataConverter);
        when(fieldDataConverter.convertFieldData(eq(testObjectData), eq(fieldDescribe), any(DataConvertContext.class)))
                .thenReturn("converted value");

        // 执行测试
        List<Variable> result = parseVarService.parseVarForDisplay(variables, testUser);

        // 验证结果
        assertNotNull(result, "结果不应为null");
        assertEquals(1, result.size(), "应返回1个变量");
        assertEquals("converted value", result.get(0).getValue(), "变量值应为转换后的显示值");
        assertEquals(IFieldType.TEXT, result.get(0).getFieldType(), "字段类型应正确设置");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseVarForDisplay方法处理表单字段-部门类型的正常场景
     */
    @Test
    @DisplayName("parseVarForDisplay - 解析表单字段部门类型")
    void testParseVarForDisplay_VarField_Department() {
        // 准备测试数据
        User userWithId = new User(TENANT_ID, USER_ID);
        Map<String, Object> args = new HashMap<>();
        args.put("form_dept", Arrays.asList("dept1", "dept2"));

        Map<String, Object> paramFormItem = new HashMap<>();
        paramFormItem.put(IFieldDescribe.API_NAME, "form_dept");
        paramFormItem.put(IFieldDescribe.TYPE, IFieldType.DEPARTMENT);
        List<Map> paramForm = Arrays.asList(paramFormItem);
        when(testButton.getParamForm()).thenReturn(paramForm);

        Variable variable = createVariableWithArgs("$form_dept$", Variable.Type.VAR_FIELD, args);
        List<Variable> variables = Arrays.asList(variable);

        QueryDeptInfoByDeptIds.DeptInfo deptInfo = new QueryDeptInfoByDeptIds.DeptInfo();
        deptInfo.setDeptName("部门名称");

        // 配置Mock行为
        when(globalVarService.findGlobalVariables(TENANT_ID, Collections.emptyList()))
                .thenReturn(Collections.emptyMap());
        when(orgService.getDeptInfoNameByIds(TENANT_ID, USER_ID, Arrays.asList("dept1", "dept2")))
                .thenReturn(Arrays.asList(deptInfo));

        // 执行测试
        List<Variable> result = parseVarService.parseVarForDisplay(variables, userWithId);

        // 验证结果
        assertNotNull(result, "结果不应为null");
        assertEquals(1, result.size(), "应返回1个变量");
        assertEquals("部门名称", result.get(0).getValue(), "变量值应为部门名称");
        assertEquals(IFieldType.DEPARTMENT, result.get(0).getFieldType(), "字段类型应为部门类型");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseVarForDisplay方法处理表单字段-员工类型的正常场景
     */
    @Test
    @DisplayName("parseVarForDisplay - 解析表单字段员工类型")
    void testParseVarForDisplay_VarField_Employee() {
        // 准备测试数据
        User userWithId = new User(TENANT_ID, USER_ID);
        Map<String, Object> args = new HashMap<>();
        args.put("form_emp", Arrays.asList("emp1", "emp2"));

        Map<String, Object> paramFormItem = new HashMap<>();
        paramFormItem.put(IFieldDescribe.API_NAME, "form_emp");
        paramFormItem.put(IFieldDescribe.TYPE, IFieldType.EMPLOYEE);
        List<Map> paramForm = Arrays.asList(paramFormItem);
        when(testButton.getParamForm()).thenReturn(paramForm);

        Variable variable = createVariableWithArgs("$form_emp$", Variable.Type.VAR_FIELD, args);
        List<Variable> variables = Arrays.asList(variable);

        UserInfo userInfo = new UserInfo();
        userInfo.setName("用户名称");

        // 配置Mock行为
        when(globalVarService.findGlobalVariables(TENANT_ID, Collections.emptyList()))
                .thenReturn(Collections.emptyMap());
        when(orgService.getUserNameByIds(TENANT_ID, USER_ID, Arrays.asList("emp1", "emp2")))
                .thenReturn(Arrays.asList(userInfo));

        // 执行测试
        List<Variable> result = parseVarService.parseVarForDisplay(variables, userWithId);

        // 验证结果
        assertNotNull(result, "结果不应为null");
        assertEquals(1, result.size(), "应返回1个变量");
        assertEquals("用户名称", result.get(0).getValue(), "变量值应为用户名称");
        assertEquals(IFieldType.EMPLOYEE, result.get(0).getFieldType(), "字段类型应为员工类型");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseVarForDisplay方法处理按钮变量var_executor的正常场景
     */
    @Test
    @DisplayName("parseVarForDisplay - 解析按钮变量var_executor")
    void testParseVarForDisplay_ButtonVariables_Executor() {
        // 准备测试数据
        User userWithId = new User(TENANT_ID, USER_ID);
        Variable variable = createVariable("$var_executor$", Variable.Type.BUTTON_VARIABLES);
        List<Variable> variables = Arrays.asList(variable);

        UserInfo userInfo = new UserInfo();
        userInfo.setName("执行者名称");

        // 配置Mock行为
        when(globalVarService.findGlobalVariables(TENANT_ID, Collections.emptyList()))
                .thenReturn(Collections.emptyMap());
        when(orgService.getUserNameByIds(TENANT_ID, USER_ID, Arrays.asList(USER_ID)))
                .thenReturn(Arrays.asList(userInfo));

        // 执行测试
        List<Variable> result = parseVarService.parseVarForDisplay(variables, userWithId);

        // 验证结果
        assertNotNull(result, "结果不应为null");
        assertEquals(1, result.size(), "应返回1个变量");
        assertEquals("执行者名称", result.get(0).getValue(), "变量值应为执行者名称");
        assertEquals(IFieldType.EMPLOYEE, result.get(0).getFieldType(), "字段类型应为员工类型");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseVarForDisplay方法处理按钮变量var_execution_time的正常场景
     */
    @Test
    @DisplayName("parseVarForDisplay - 解析按钮变量var_execution_time")
    void testParseVarForDisplay_ButtonVariables_ExecutionTime() {
        // 准备测试数据
        Variable variable = createVariable("$var_execution_time$", Variable.Type.BUTTON_VARIABLES);
        List<Variable> variables = Arrays.asList(variable);

        // 配置Mock行为
        when(globalVarService.findGlobalVariables(TENANT_ID, Collections.emptyList()))
                .thenReturn(Collections.emptyMap());

        // 执行测试
        List<Variable> result = parseVarService.parseVarForDisplay(variables, testUser);

        // 验证结果
        assertNotNull(result, "结果不应为null");
        assertEquals(1, result.size(), "应返回1个变量");
        assertNotNull(result.get(0).getValue(), "变量值不应为null");
        assertEquals(IFieldType.DATE_TIME, result.get(0).getFieldType(), "字段类型应为日期时间类型");
    }

    // 辅助方法：设置通用Mock行为
    private void setupCommonMockBehaviors(Variable.Type type) {
        // 全局变量服务Mock
        when(globalVarService.findGlobalVariables(anyString(), anyList()))
                .thenReturn(Collections.emptyMap());

        // 根据类型设置特定Mock行为
        if (type == Variable.Type.GLOBAL_CONSTANT) {
            IGlobalVariableDescribe globalVar = createMockGlobalVariable("testVar__g", IFieldType.TEXT, "test");
            Map<String, IGlobalVariableDescribe> globalVarMap = new HashMap<>();
            globalVarMap.put("testVar__g", globalVar);
            when(globalVarService.findGlobalVariables(TENANT_ID, Arrays.asList("testVar__g")))
                    .thenReturn(globalVarMap);
            when(globalVarService.parseValue(any(), anyBoolean())).thenReturn("parsed value");
        }

        if (type == Variable.Type.OBJECT_FIELD) {
            IFieldDescribe fieldDescribe = createMockFieldDescribe("name", IFieldType.TEXT);
            testDescribe.setFieldDescribes(Arrays.asList(fieldDescribe));
        }
    }
}
