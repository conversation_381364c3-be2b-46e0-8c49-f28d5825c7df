package com.facishare.paas.appframework.button.dto;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SendCrmRemindPojo DTO单元测试 - JUnit5版本
 * 
 * GenerateByAI
 * 
 * 测试SendCrmRemindPojo CRM提醒发送数据传输对象：
 * - 字段设置和获取
 * - 数据验证和约束
 * - JSON序列化和反序列化
 * - 边界条件和null值处理
 * - 收件人结构验证
 * 
 * 覆盖场景：
 * - 正常提醒数据设置
 * - 空值和null值处理
 * - 复杂收件人结构
 * - JSON数据转换
 * - 数据完整性验证
 */
@DisplayName("SendCrmRemindPojo - CRM提醒发送DTO测试")
class SendCrmRemindPojoTest {

    private Gson gson;
    private SendCrmRemindPojo sendCrmRemindPojo;

    @BeforeEach
    void setUp() {
        gson = new GsonBuilder().create();
        sendCrmRemindPojo = new SendCrmRemindPojo();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试基本字段的设置和获取
     */
    @Test
    @DisplayName("基本字段设置和获取测试")
    void testBasicFieldsSetterAndGetter() {
        // Given
        String content = "这是一条重要的CRM提醒消息";
        String template = "crm_remind_template_001";
        String title = "重要提醒";

        // When
        sendCrmRemindPojo.setContent(content);
        sendCrmRemindPojo.setTemplate(template);
        sendCrmRemindPojo.setTitle(title);

        // Then
        assertEquals(content, sendCrmRemindPojo.getContent(), "内容应正确设置");
        assertEquals(template, sendCrmRemindPojo.getTemplate(), "模板应正确设置");
        assertEquals(title, sendCrmRemindPojo.getTitle(), "标题应正确设置");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试收件人映射的设置和获取
     */
    @Test
    @DisplayName("收件人映射设置和获取测试")
    void testRecipientsMapSetterAndGetter() {
        // Given
        Map<String, List<String>> recipients = new HashMap<>();
        recipients.put("users", Arrays.asList("user1", "user2", "user3"));
        recipients.put("departments", Arrays.asList("sales", "marketing"));
        recipients.put("roles", Arrays.asList("manager", "supervisor"));

        // When
        sendCrmRemindPojo.setRecipients(recipients);

        // Then
        assertEquals(recipients, sendCrmRemindPojo.getRecipients(), "收件人映射应正确设置");
        assertEquals(3, sendCrmRemindPojo.getRecipients().size(), "收件人类型数量应为3");
        
        List<String> userList = sendCrmRemindPojo.getRecipients().get("users");
        assertEquals(3, userList.size(), "用户数量应为3");
        assertTrue(userList.contains("user1"), "应包含user1");
        assertTrue(userList.contains("user2"), "应包含user2");
        assertTrue(userList.contains("user3"), "应包含user3");
        
        List<String> departmentList = sendCrmRemindPojo.getRecipients().get("departments");
        assertEquals(2, departmentList.size(), "部门数量应为2");
        assertTrue(departmentList.contains("sales"), "应包含sales部门");
        assertTrue(departmentList.contains("marketing"), "应包含marketing部门");
        
        List<String> roleList = sendCrmRemindPojo.getRecipients().get("roles");
        assertEquals(2, roleList.size(), "角色数量应为2");
        assertTrue(roleList.contains("manager"), "应包含manager角色");
        assertTrue(roleList.contains("supervisor"), "应包含supervisor角色");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试null值处理
     */
    @Test
    @DisplayName("null值处理测试")
    void testNullValueHandling() {
        // When
        sendCrmRemindPojo.setContent(null);
        sendCrmRemindPojo.setTemplate(null);
        sendCrmRemindPojo.setTitle(null);
        sendCrmRemindPojo.setRecipients(null);

        // Then
        assertNull(sendCrmRemindPojo.getContent(), "内容应为null");
        assertNull(sendCrmRemindPojo.getTemplate(), "模板应为null");
        assertNull(sendCrmRemindPojo.getTitle(), "标题应为null");
        assertNull(sendCrmRemindPojo.getRecipients(), "收件人映射应为null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空字符串和空集合处理
     */
    @Test
    @DisplayName("空字符串和空集合处理测试")
    void testEmptyValueHandling() {
        // Given
        String emptyContent = "";
        String emptyTemplate = "";
        String emptyTitle = "";
        Map<String, List<String>> emptyRecipientsMap = new HashMap<>();

        // When
        sendCrmRemindPojo.setContent(emptyContent);
        sendCrmRemindPojo.setTemplate(emptyTemplate);
        sendCrmRemindPojo.setTitle(emptyTitle);
        sendCrmRemindPojo.setRecipients(emptyRecipientsMap);

        // Then
        assertEquals("", sendCrmRemindPojo.getContent(), "内容应为空字符串");
        assertEquals("", sendCrmRemindPojo.getTemplate(), "模板应为空字符串");
        assertEquals("", sendCrmRemindPojo.getTitle(), "标题应为空字符串");
        
        assertNotNull(sendCrmRemindPojo.getRecipients(), "收件人映射不应为null");
        assertTrue(sendCrmRemindPojo.getRecipients().isEmpty(), "收件人映射应为空");
    }

    /**
     * GenerateByAI
     * 测试内容描述：参数化测试不同的提醒模板
     */
    @ParameterizedTest
    @ValueSource(strings = {
        "urgent_reminder", 
        "daily_notification", 
        "weekly_report", 
        "monthly_summary",
        "custom_alert_template"
    })
    @DisplayName("不同提醒模板测试")
    void testDifferentRemindTemplates(String template) {
        // When
        sendCrmRemindPojo.setTemplate(template);

        // Then
        assertEquals(template, sendCrmRemindPojo.getTemplate(), "模板应正确设置");
        assertNotNull(sendCrmRemindPojo.getTemplate(), "模板不应为null");
        assertFalse(sendCrmRemindPojo.getTemplate().isEmpty(), "模板不应为空字符串");
    }

    /**
     * GenerateByAI
     * 测试内容描述：参数化测试不同的提醒标题
     */
    @ParameterizedTest
    @ValueSource(strings = {
        "紧急提醒",
        "系统通知",
        "任务提醒",
        "会议通知",
        "Important Alert"
    })
    @DisplayName("不同提醒标题测试")
    void testDifferentRemindTitles(String title) {
        // When
        sendCrmRemindPojo.setTitle(title);

        // Then
        assertEquals(title, sendCrmRemindPojo.getTitle(), "标题应正确设置");
        assertNotNull(sendCrmRemindPojo.getTitle(), "标题不应为null");
        assertFalse(sendCrmRemindPojo.getTitle().isEmpty(), "标题不应为空字符串");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试JSON序列化
     */
    @Test
    @DisplayName("JSON序列化测试")
    void testJsonSerialization() {
        // Given
        sendCrmRemindPojo.setContent("测试提醒内容");
        sendCrmRemindPojo.setTemplate("test_template");
        sendCrmRemindPojo.setTitle("测试标题");
        
        Map<String, List<String>> recipients = new HashMap<>();
        recipients.put("users", Arrays.asList("user1", "user2"));
        recipients.put("departments", Arrays.asList("sales"));
        sendCrmRemindPojo.setRecipients(recipients);

        // When
        String json = gson.toJson(sendCrmRemindPojo);

        // Then
        assertNotNull(json, "JSON字符串不应为null");
        assertTrue(json.contains("测试提醒内容"), "JSON应包含提醒内容");
        assertTrue(json.contains("test_template"), "JSON应包含模板名称");
        assertTrue(json.contains("测试标题"), "JSON应包含标题");
        assertTrue(json.contains("user1"), "JSON应包含用户");
        assertTrue(json.contains("sales"), "JSON应包含部门");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试JSON反序列化
     */
    @Test
    @DisplayName("JSON反序列化测试")
    void testJsonDeserialization() {
        // Given
        String json = "{\n" +
                "  \"content\": \"测试提醒内容\",\n" +
                "  \"template\": \"test_template\",\n" +
                "  \"title\": \"测试标题\",\n" +
                "  \"recipients\": {\n" +
                "    \"users\": [\"user1\", \"user2\"],\n" +
                "    \"departments\": [\"sales\", \"marketing\"]\n" +
                "  }\n" +
                "}";

        // When
        SendCrmRemindPojo deserializedPojo = gson.fromJson(json, SendCrmRemindPojo.class);

        // Then
        assertNotNull(deserializedPojo, "反序列化对象不应为null");
        assertEquals("测试提醒内容", deserializedPojo.getContent(), "内容应正确");
        assertEquals("test_template", deserializedPojo.getTemplate(), "模板应正确");
        assertEquals("测试标题", deserializedPojo.getTitle(), "标题应正确");
        
        assertNotNull(deserializedPojo.getRecipients(), "收件人映射不应为null");
        assertEquals(2, deserializedPojo.getRecipients().size(), "收件人类型数量应为2");
        
        assertTrue(deserializedPojo.getRecipients().containsKey("users"), "应包含users键");
        assertTrue(deserializedPojo.getRecipients().containsKey("departments"), "应包含departments键");
        
        List<String> users = deserializedPojo.getRecipients().get("users");
        assertEquals(2, users.size(), "用户数量应为2");
        assertTrue(users.contains("user1"), "应包含user1");
        assertTrue(users.contains("user2"), "应包含user2");
        
        List<String> departments = deserializedPojo.getRecipients().get("departments");
        assertEquals(2, departments.size(), "部门数量应为2");
        assertTrue(departments.contains("sales"), "应包含sales");
        assertTrue(departments.contains("marketing"), "应包含marketing");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试复杂收件人结构
     */
    @Test
    @DisplayName("复杂收件人结构测试")
    void testComplexRecipientsStructure() {
        // Given
        Map<String, List<String>> complexRecipients = new HashMap<>();
        complexRecipients.put("direct_users", Arrays.asList(
                "user001", "user002", "user003", "user004", "user005"
        ));
        complexRecipients.put("departments", Arrays.asList(
                "sales", "marketing", "development", "hr", "finance"
        ));
        complexRecipients.put("roles", Arrays.asList(
                "manager", "supervisor", "team_lead", "senior_developer"
        ));
        complexRecipients.put("groups", Arrays.asList(
                "project_team_a", "project_team_b", "management_group"
        ));

        // When
        sendCrmRemindPojo.setRecipients(complexRecipients);

        // Then
        assertEquals(complexRecipients, sendCrmRemindPojo.getRecipients(), "复杂收件人结构应正确设置");
        assertEquals(4, sendCrmRemindPojo.getRecipients().size(), "收件人类型数量应为4");
        
        // 验证各类型收件人数量
        assertEquals(5, sendCrmRemindPojo.getRecipients().get("direct_users").size(), "直接用户数量应为5");
        assertEquals(5, sendCrmRemindPojo.getRecipients().get("departments").size(), "部门数量应为5");
        assertEquals(4, sendCrmRemindPojo.getRecipients().get("roles").size(), "角色数量应为4");
        assertEquals(3, sendCrmRemindPojo.getRecipients().get("groups").size(), "组数量应为3");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试长文本内容处理
     */
    @Test
    @DisplayName("长文本内容处理测试")
    void testLongTextContentHandling() {
        // Given
        StringBuilder longContent = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longContent.append("这是一条很长的提醒消息内容，包含重要信息。");
        }
        String longContentString = longContent.toString();
        
        String longTitle = "这是一个非常非常非常非常非常非常非常非常非常非常长的标题，用于测试长标题的处理能力";

        // When
        sendCrmRemindPojo.setContent(longContentString);
        sendCrmRemindPojo.setTitle(longTitle);

        // Then
        assertEquals(longContentString, sendCrmRemindPojo.getContent(), "长内容应正确设置");
        assertEquals(longTitle, sendCrmRemindPojo.getTitle(), "长标题应正确设置");
        assertTrue(sendCrmRemindPojo.getContent().length() > 10000, "内容长度应大于10000字符");
        assertTrue(sendCrmRemindPojo.getTitle().length() > 50, "标题长度应大于50字符");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试数据完整性验证
     */
    @Test
    @DisplayName("数据完整性验证测试")
    void testDataIntegrityValidation() {
        // Given
        String content = "重要的CRM提醒消息";
        String template = "urgent_notification";
        String title = "紧急通知";
        Map<String, List<String>> recipients = new HashMap<>();
        recipients.put("users", Arrays.asList("user1", "user2"));
        recipients.put("departments", Arrays.asList("sales", "marketing"));

        // When
        sendCrmRemindPojo.setContent(content);
        sendCrmRemindPojo.setTemplate(template);
        sendCrmRemindPojo.setTitle(title);
        sendCrmRemindPojo.setRecipients(recipients);

        // Then - 验证所有数据都正确设置
        assertEquals(content, sendCrmRemindPojo.getContent(), "内容应正确");
        assertEquals(template, sendCrmRemindPojo.getTemplate(), "模板应正确");
        assertEquals(title, sendCrmRemindPojo.getTitle(), "标题应正确");
        assertEquals(recipients, sendCrmRemindPojo.getRecipients(), "收件人映射应正确");
        
        // 验证数据一致性
        assertNotNull(sendCrmRemindPojo.getContent(), "内容不应为null");
        assertNotNull(sendCrmRemindPojo.getTemplate(), "模板不应为null");
        assertNotNull(sendCrmRemindPojo.getTitle(), "标题不应为null");
        assertNotNull(sendCrmRemindPojo.getRecipients(), "收件人映射不应为null");
        
        // 验证集合不为空
        assertFalse(sendCrmRemindPojo.getRecipients().isEmpty(), "收件人映射不应为空");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象相等性
     */
    @Test
    @DisplayName("对象相等性测试")
    void testObjectEquality() {
        // Given
        SendCrmRemindPojo pojo1 = new SendCrmRemindPojo();
        pojo1.setContent("测试内容");
        pojo1.setTemplate("template1");
        pojo1.setTitle("测试标题");
        
        SendCrmRemindPojo pojo2 = new SendCrmRemindPojo();
        pojo2.setContent("测试内容");
        pojo2.setTemplate("template1");
        pojo2.setTitle("测试标题");

        // When & Then
        // 注意：由于使用了@Data注解，Lombok会自动生成equals和hashCode方法
        assertEquals(pojo1, pojo2, "相同数据的对象应相等");
        assertEquals(pojo1.hashCode(), pojo2.hashCode(), "相同数据的对象hashCode应相等");
        
        // 修改一个对象的数据
        pojo2.setTitle("不同标题");
        assertNotEquals(pojo1, pojo2, "不同数据的对象应不相等");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试特殊字符处理
     */
    @Test
    @DisplayName("特殊字符处理测试")
    void testSpecialCharacterHandling() {
        // Given
        String contentWithSpecialChars = "包含特殊字符的内容：@#$%^&*()_+-=[]{}|;':\",./<>?";
        String titleWithSpecialChars = "特殊标题：【重要】★紧急★";
        String templateWithSpecialChars = "template_with_special-chars_123";

        // When
        sendCrmRemindPojo.setContent(contentWithSpecialChars);
        sendCrmRemindPojo.setTitle(titleWithSpecialChars);
        sendCrmRemindPojo.setTemplate(templateWithSpecialChars);

        // Then
        assertEquals(contentWithSpecialChars, sendCrmRemindPojo.getContent(), "特殊字符内容应正确设置");
        assertEquals(titleWithSpecialChars, sendCrmRemindPojo.getTitle(), "特殊字符标题应正确设置");
        assertEquals(templateWithSpecialChars, sendCrmRemindPojo.getTemplate(), "特殊字符模板应正确设置");
    }
}
