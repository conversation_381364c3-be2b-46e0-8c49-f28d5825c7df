package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.function.FunctionPluginConfReq;
import com.facishare.paas.appframework.metadata.plugin.FunctionPluginConfLogicService;
import com.facishare.paas.appframework.metadata.repository.model.MtFunctionPluginConf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 对象管理后台 扩展 tab
 */
@Slf4j
@Service
@ServiceModule("object_extension")
public class ObjectExtensionService {

	@Resource(name = "functionPluginConfLogicService")
	private FunctionPluginConfLogicService functionPluginConfLogicService;

	@ServiceMethod("list")
	public FunctionPluginConfReq.BatchResult list(FunctionPluginConfReq.FindArg arg, ServiceContext context) {
		List<MtFunctionPluginConf> mtConfigs = functionPluginConfLogicService.findAllForList(arg.getRefObjApiName(), context.getUser());
		return FunctionPluginConfReq.BatchResult.builder()
				.configs(CollectionUtils.nullToEmpty(mtConfigs))
				.build();
	}

	@ServiceMethod("find")
	public FunctionPluginConfReq.Result find(FunctionPluginConfReq.FindArg arg, ServiceContext context) {
		MtFunctionPluginConf config = functionPluginConfLogicService.findByApiName(context.getUser(), arg.getApiName())
				.orElseThrow(() -> new ValidateException(I18N.text(I18NKey.EXTENSION_API_NAME_NOT_EXIST, arg.getApiName())));
		return FunctionPluginConfReq.Result.builder()
				.config(config)
				.build();
	}

	@ServiceMethod("create")
	public FunctionPluginConfReq.Result create(FunctionPluginConfReq.Arg arg, ServiceContext context) {
		MtFunctionPluginConf mtConfig = arg.getConfig();
		MtFunctionPluginConf resultMtConfig = functionPluginConfLogicService.create(context.getUser(), mtConfig);
		return FunctionPluginConfReq.Result.builder()
				.config(resultMtConfig)
				.build();
	}

	@ServiceMethod("update")
	public FunctionPluginConfReq.Result update(FunctionPluginConfReq.Arg arg, ServiceContext context) {
		MtFunctionPluginConf mtConfig = arg.getConfig();
		MtFunctionPluginConf resultMtConfig = functionPluginConfLogicService.update(context.getUser(), mtConfig);
		return FunctionPluginConfReq.Result.builder()
				.config(resultMtConfig)
				.build();
	}

	@ServiceMethod("enable")
	public FunctionPluginConfReq.Result enable(FunctionPluginConfReq.FindArg arg, ServiceContext context) {
		MtFunctionPluginConf mtConfig = functionPluginConfLogicService.enable(context.getUser(), arg.getApiName());
		return FunctionPluginConfReq.Result.builder()
				.config(mtConfig)
				.build();
	}

	@ServiceMethod("disable")
	public FunctionPluginConfReq.Result disable(FunctionPluginConfReq.FindArg arg, ServiceContext context) {
		MtFunctionPluginConf mtConfig = functionPluginConfLogicService.disable(context.getUser(), arg.getApiName());
		return FunctionPluginConfReq.Result.builder()
				.config(mtConfig)
				.build();
	}

	@ServiceMethod("delete")
	public FunctionPluginConfReq.Result delete(FunctionPluginConfReq.FindArg arg, ServiceContext context) {
		functionPluginConfLogicService.delete(context.getUser(), arg.getApiName());
		return FunctionPluginConfReq.Result.builder()
				.config(null)
				.build();
	}
} 