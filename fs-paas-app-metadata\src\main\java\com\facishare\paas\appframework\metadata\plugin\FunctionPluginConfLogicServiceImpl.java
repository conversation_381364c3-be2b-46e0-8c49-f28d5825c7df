package com.facishare.paas.appframework.metadata.plugin;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.LanguageClientUtil;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.layout.I18nInfo;
import com.facishare.paas.appframework.metadata.relation.SourceTypes;
import com.facishare.paas.appframework.metadata.relation.TargetTypes;
import com.facishare.paas.appframework.metadata.repository.api.IRepository;
import com.facishare.paas.appframework.metadata.repository.model.MtFunctionPluginConf;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.repository.model.MtFunctionPluginConf.*;
import static com.facishare.paas.metadata.exception.ErrorCode.FS_PAAS_SPECIAL_TABLE_INSERT_EXCEPTION;

/**
 * <AUTHOR> ZhenHui
 * @Data : 2025/4/25
 * @Description :
 */
@Slf4j
@Service("functionPluginConfLogicService")
public class FunctionPluginConfLogicServiceImpl implements FunctionPluginConfLogicService {
    @Resource
    private IRepository<MtFunctionPluginConf> repository;
    @Resource
    private FunctionLogicService functionLogicService;
    @Resource
    private LogService logService;

    private List<MtFunctionPluginConf> setI18n(List<MtFunctionPluginConf> datas, String tenantId) {
        datas = CollectionUtils.nullToEmpty(datas);
        if (!LanguageClientUtil.hasLanguage(tenantId)) {
            return datas;
        }
        String languageTag = RequestUtil.getLanguageTag();
        for (MtFunctionPluginConf conf : datas) {
            Map<String, I18nInfo> i18nMap = CollectionUtils.nullToEmpty(conf.getI18nInfo())
                    .stream().collect(Collectors.toMap(I18nInfo::getApiName, x -> x, (x, y) -> x));
            conf.setName(Optional.ofNullable(i18nMap.get(MtFunctionPluginConf.NAME))
                    .map(x -> CollectionUtils.nullToEmpty(x.getLanguageInfo()).get(languageTag))
                    .orElse(conf.getName()));
            conf.setDescription(Optional.ofNullable(i18nMap.get(MtFunctionPluginConf.DESCRIPTION))
                    .map(x -> CollectionUtils.nullToEmpty(x.getLanguageInfo()).get(languageTag))
                    .orElse(conf.getDescription()));
        }
        return datas;
    }

    private Query buildQuery(User user) {
        SearchQuery searchQuery = SearchQueryImpl.filters(Lists.newArrayList(
                FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter(),
                FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue())).getFilter()
        ));

        return Query.builder()
                .searchQuery(searchQuery)
                .limit(AppFrameworkConfig.DEFAULT_MAX_QUERY_LIMIT)
                .offset(0)
                .needReturnCountNum(false)
                .needReturnQuote(false)
                .build();
    }

    // 查询租户下所有数据
    private List<MtFunctionPluginConf> findAllByTenantId(User user, Query query, boolean needI18) {
        List<MtFunctionPluginConf> result = CollectionUtils.nullToEmpty(repository.findBy(user, query, MtFunctionPluginConf.class));
        if (needI18) {
            result = setI18n(result, user.getTenantId());
        }
        return result;
    }

    // 按照api查询一条数据
    private Optional<MtFunctionPluginConf> findByApiName(User user, String apiName, boolean needI18) {
        if (StringUtils.isBlank(apiName)) {
            return Optional.empty();
        }
        Query query = buildQuery(user);
        query.and(
                FilterExt.of(Operator.EQ, MtFunctionPluginConf.API_NAME, apiName).getFilter()
        );
        return findOne(user, query, needI18);
    }

    // 查询单条数据
    private Optional<MtFunctionPluginConf> findOne(User user, Query query, boolean needI18) {
        query.setLimit(1);
        return findAllByTenantId(user, query, needI18).stream().findFirst();
    }

    // 查询业务对象下的所有数据
    private List<MtFunctionPluginConf> findAllByRefObj(User user, String refObjApi, boolean needI18) {
        if (StringUtils.isBlank(refObjApi)) {
            return Lists.newArrayList();
        }
        Query query = buildQuery(user);
        query.and(
                FilterExt.of(Operator.EQ, REF_OBJECT_API_NAME, refObjApi).getFilter()
        );
        return findAllByTenantId(user, query, needI18);
    }

    /**
     * 通过请求类型, 查询可用的数据
     * @param refObjApi  业务对象
     * @param moduleName 页面类型
     */
    private Optional<MtFunctionPluginConf> findByModuleName(User user, String refObjApi, String moduleName) {
        if (StringUtils.isBlank(moduleName) || StringUtils.isBlank(refObjApi)) {
            return Optional.empty();
        }
        Query query = buildQuery(user);
        query.and(
                FilterExt.of(Operator.EQ, MtFunctionPluginConf.MODULE_NAME, moduleName).getFilter(),
                FilterExt.of(Operator.EQ, REF_OBJECT_API_NAME, refObjApi).getFilter()
        );
        return findOne(user, query, false);
    }

    private ReferenceData buildReferenceData(MtFunctionPluginConf config) {
        return ReferenceData.builder()
                .sourceType(SourceTypes.OBJECT_EXTENSION)
                .sourceLabel(config.getName())
                .sourceValue(config.getRefObjectApiName() + "." + config.getApiName())
                .targetType(TargetTypes.FUNCTION)
                .targetValue(config.getFunctionApiName())
                .build();
    }

    private void verifyModuleNameUnique(User user, MtFunctionPluginConf config) {
        // 相同 module_name 只能存在一个
        findByModuleName(user, config.getRefObjectApiName(), config.getModuleName()).ifPresent(conf -> {
            throw new ValidateException(I18N.text(I18NKey.EXTENSION_MODULE_NAME_EXIST));
        });
    }

    // 批量删除数据并删除引用关系
    private List<MtFunctionPluginConf> bulkInvalidAndDelete(User user, List<MtFunctionPluginConf> configs) {
        if (CollectionUtils.empty(configs)) {
            return configs;
        }
        List<ReferenceData> referenceDataList = configs.stream()
                .map(this::buildReferenceData)
                .collect(Collectors.toList());
        functionLogicService.batchDeleteRelation(user, referenceDataList);
        return repository.bulkDelete(user, configs);
    }

    @Override
    // 批量创建和函数的引用关系
    public void createFunctionReference(User user, List<MtFunctionPluginConf> configs) {
        if (CollectionUtils.empty(configs)) {
            return;
        }
        List<ReferenceData> referenceDataList = configs.stream()
                .map(this::buildReferenceData)
                .collect(Collectors.toList());
        functionLogicService.deleteAndCreateRelation(user, referenceDataList);
    }

    @Override
    public List<MtFunctionPluginConf> findAllForList(String refObjApi, User user) {
        if (StringUtils.isBlank(refObjApi)) {
            return Lists.newArrayList();
        }
        List<MtFunctionPluginConf> result = findAllByRefObj(user, refObjApi, true);
        result.removeIf(x -> !x.show());    // 最初迁移时部分类型虽然迁移但是用户不可见
        return result;
    }

    @Override
    public Optional<MtFunctionPluginConf> findAvailableRuntime(User user,
                                                               String objectApiName, String requestCode) {
        log.debug("{} search for {}", user.getTenantId(), requestCode);
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OBJECT_EXTENSION, user.getTenantId())) {
            return Optional.empty();
        }
        return findByModuleName(user, objectApiName, requestCode)
                .filter(MtFunctionPluginConf::enabled);
    }

    /**
     * 按照api查询关系(非删除数据api全租户唯一)
     *
     * @param user    用户信息
     * @param apiName 数据api
     */
    @Override
    public Optional<MtFunctionPluginConf> findByApiName(User user, String apiName) {
        return findByApiName(user, apiName, true);
    }

    @Override
    public List<MtFunctionPluginConf> findByApiNames(User user, List<String> apiNames) {
        Optional.ofNullable(apiNames).ifPresent(x -> x.removeIf(Objects::isNull));
        if (CollectionUtils.empty(apiNames)) {
            return Lists.newArrayList();
        }
        Query query = buildQuery(user);
        query.and(FilterExt.of(Operator.IN, API_NAME, apiNames).getFilter());
        return findAllByTenantId(user, query, false);
    }

    @Override
    @Transactional
    public MtFunctionPluginConf create(User user, MtFunctionPluginConf config) {
        if(Objects.isNull(config)){
            throw  new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        validateData(Lists.newArrayList(config));
        verifyModuleNameUnique(user, config);
        config.setTenantId(user.getTenantId());
        config.setIsActive(true);
        MtFunctionPluginConf pluginConf;
        try {
            pluginConf = repository.create(user, config);
        } catch (MetaDataBusinessException e) {
            if (FS_PAAS_SPECIAL_TABLE_INSERT_EXCEPTION.getCode() == e.getErrorCode()) {
                throw new ValidateException(I18N.text(I18NKey.EXTENSION_API_NAME_EXIST, config.getApiName()));
            }
            else {
                throw e;
            }
        }
        createFunctionReference(user, Lists.newArrayList(config));
        logService.log(user, EventType.ADD, ActionType.CREATE_EXTENSION, config.getRefObjectApiName(), I18N.text(I18NKey.OBJECT_EXTENSION, config.getName(), config.getApiName()));
        return pluginConf;
    }

    @Override
    @Transactional
    public List<MtFunctionPluginConf> bulkCreate(User user, List<MtFunctionPluginConf> configs) {
        List<MtFunctionPluginConf> pluginConfs = repository.bulkCreate(user, configs);
        createFunctionReference(user, configs);
        return pluginConfs;
    }


    @Override
    @Transactional
    public MtFunctionPluginConf update(User user, MtFunctionPluginConf config) {
        if(Objects.isNull(config)){
            throw  new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        validateData(Lists.newArrayList(config));
        findByApiName(user, config.getApiName(), false)
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.EXTENSION_API_NAME_NOT_EXIST,  config.getApiName())));
        List<MtFunctionPluginConf> pluginConfs = repository.bulkUpdateByFields(user, Lists.newArrayList(config),
                Lists.newArrayList(MODULE_TYPE, METHODS, FUNCTION_API_NAME, NAME, DESCRIPTION, I18N_INFO)); // AGENT_TYPE ?
        MtFunctionPluginConf updateRet = pluginConfs.stream().findFirst().orElse(null);
        if (Objects.isNull(updateRet)) {
            throw new ValidateException(I18N.text(I18NKey.UPDATE_FAILED));
        }
        createFunctionReference(user, Lists.newArrayList(config));
        logService.log(user, EventType.MODIFY, ActionType.UPDATE_EXTENSION, config.getRefObjectApiName(),
                I18N.text(I18NKey.OBJECT_EXTENSION, config.getName(), config.getApiName()));
        return updateRet;
    }

    @Override
    public MtFunctionPluginConf enable(User user, String apiName) {
        return findByApiName(user, apiName, false)
                .map(config -> {
                    config.setIsActive(true);
                    List<MtFunctionPluginConf> pluginConfs = repository.bulkUpdateByFields(user, Lists.newArrayList(config),
                            Lists.newArrayList(IS_ACTIVE));
                    logService.log(user, EventType.ENABLE, ActionType.ENABLE_EXTENSION, config.getRefObjectApiName(),
                            I18N.text(I18NKey.OBJECT_EXTENSION, config.getName(), config.getApiName()));
                    return pluginConfs.stream().findFirst().orElse(null);
                })
                .orElse(null);
    }

    @Override
    public MtFunctionPluginConf disable(User user, String apiName) {
        return findByApiName(user, apiName, false)
                .map(config -> {
                    config.setIsActive(false);
                    List<MtFunctionPluginConf> pluginConfs = repository.bulkUpdateByFields(user, Lists.newArrayList(config),
                            Lists.newArrayList(IS_ACTIVE));
                    logService.log(user, EventType.DISABLE, ActionType.DISABLE_EXTENSION, config.getRefObjectApiName(),
                            I18N.text(I18NKey.OBJECT_EXTENSION, config.getName(), config.getApiName()));
                    return pluginConfs.stream().findFirst().orElse(null);
                })
                .orElse(null);
    }

    @Override
    public void deleteAll(User user, String refObjApi) {
        if (StringUtils.isBlank(user.getTenantId()) || StringUtils.isBlank(refObjApi)) {
            return;
        }
        bulkInvalidAndDelete(user, findAllByRefObj(user, refObjApi, false));
    }

    @Override
    @Transactional
    public void delete(User user, String apiName) {
        MtFunctionPluginConf config = findByApiName(user, apiName, false)
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.EXTENSION_API_NAME_NOT_EXIST, apiName)));
        if (config.enabled()) {
            throw new ValidateException(I18N.text(I18NKey.EXTENSION_ENABLE_STATUS_NOT_DELETE, apiName));
        }
        bulkInvalidAndDelete(user, Lists.newArrayList(config));
        logService.log(user, EventType.DELETE, ActionType.DELETE_EXTENSION, config.getRefObjectApiName(), I18N.text(I18NKey.OBJECT_EXTENSION, config.getName(), config.getApiName()));
    }

    @Override
    public List<MtFunctionPluginConf> bulkDeleteDataToBeCreat(User user, List<MtFunctionPluginConf> configs, StopWatch stopWatch) {
        if (CollectionUtils.empty(configs)) {
            return Lists.newArrayList();
        }

        Query query = buildQuery(user);
        List<IFilter> filters = Lists.newArrayList();
        for (MtFunctionPluginConf conf : configs) {
            String groupId = String.join("-", conf.getRefObjectApiName(), conf.getModuleName());

            IFilter refObjFilter = FilterExt.of(Operator.EQ, REF_OBJECT_API_NAME, conf.getRefObjectApiName()).getFilter();
            refObjFilter.setFilterGroup(groupId);
            filters.add(refObjFilter);

            IFilter moduleNameFilter = FilterExt.of(Operator.EQ, MODULE_NAME, conf.getModuleName()).getFilter();
            moduleNameFilter.setFilterGroup(groupId);
            filters.add(moduleNameFilter);
        }
        SearchQuery searchQuery = SearchQueryImpl.filters(filters);
        query.mergeSearchQuery(searchQuery);
        List<MtFunctionPluginConf> dbDuplicatedDatas = findAllByTenantId(user, query, false);

        if (CollectionUtils.empty(dbDuplicatedDatas)) {
            return Lists.newArrayList();
        }

        List<MtFunctionPluginConf> dbDuplicatedDatasCreatBySystem = dbDuplicatedDatas.stream()
                .filter(x -> User.SUPPER_ADMIN_USER_ID.equals(x.getCreateBy()))
                .collect(Collectors.toList());    // 只删除创建人是-10000的
        List<String> deletedDataUniqIds = bulkInvalidAndDelete(user, dbDuplicatedDatasCreatBySystem).stream()
                .map(x -> x.UniqueId()).collect(Collectors.toList());
        stopWatch.lap(String.format("deleted duplicate system data: %d, uniqueId: %s", deletedDataUniqIds.size(), deletedDataUniqIds));

        return dbDuplicatedDatas.stream().filter(x -> !deletedDataUniqIds.contains(x.UniqueId())).collect(Collectors.toList());
    }

    @Override
    public void validateData(List<MtFunctionPluginConf> datas) {
        MtFunctionPluginConf.validateData(datas);
    }
}
