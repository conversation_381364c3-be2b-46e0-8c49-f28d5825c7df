package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.service.dto.function.FunctionPluginConfReq;
import com.facishare.paas.appframework.metadata.plugin.FunctionPluginConfLogicService;
import com.facishare.paas.appframework.metadata.repository.model.MtFunctionPluginConf;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectExtensionService单元测试类
 * 测试函数插件配置管理服务的各种功能
 * 严格遵循JDK8语法规范，不使用JDK8以上新特性
 */
@DisplayName("ObjectExtensionService单元测试")
public class ObjectExtensionServiceTest extends BaseServiceTest {

    @Mock
    private FunctionPluginConfLogicService functionPluginConfLogicService;

    @InjectMocks
    private ObjectExtensionService objectExtensionService;

    private static final String API_NAME = "testPlugin";
    private static final String REF_OBJ_API_NAME = "TestObj__c";
    private static final String MODULE_NAME = "testModule";

    @Override
    protected String getServiceName() {
        return "object_extension";
    }

    @BeforeEach
    void setUp() {
        // 基础设置已在BaseServiceTest中完成
    }

    @Test
    @DisplayName("GenerateByAI - 测试list方法成功场景")
    void testList_Success() {
        // Arrange
        FunctionPluginConfReq.FindArg arg = new FunctionPluginConfReq.FindArg();
        arg.setRefObjApiName(REF_OBJ_API_NAME);

        MtFunctionPluginConf mockConfig = createMockConfig();
        when(functionPluginConfLogicService.findAllForList(REF_OBJ_API_NAME, testUser))
                .thenReturn(Lists.newArrayList(mockConfig));

        // Act
        FunctionPluginConfReq.BatchResult result = objectExtensionService.list(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getConfigs());
        assertEquals(1, result.getConfigs().size());
        verify(functionPluginConfLogicService, times(1)).findAllForList(REF_OBJ_API_NAME, testUser);
    }

    @Test
    @DisplayName("GenerateByAI - 测试list方法空结果场景")
    void testList_EmptyResult() {
        // Arrange
        FunctionPluginConfReq.FindArg arg = new FunctionPluginConfReq.FindArg();
        arg.setRefObjApiName(REF_OBJ_API_NAME);

        when(functionPluginConfLogicService.findAllForList(REF_OBJ_API_NAME, testUser))
                .thenReturn(Lists.newArrayList());

        // Act
        FunctionPluginConfReq.BatchResult result = objectExtensionService.list(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getConfigs());
        assertEquals(0, result.getConfigs().size());
        verify(functionPluginConfLogicService, times(1)).findAllForList(REF_OBJ_API_NAME, testUser);
    }

    @Test
    @DisplayName("GenerateByAI - 测试find方法成功场景")
    void testFind_Success() {
        // Arrange
        FunctionPluginConfReq.FindArg arg = new FunctionPluginConfReq.FindArg();
        arg.setApiName(API_NAME);

        MtFunctionPluginConf mockConfig = createMockConfig();
        when(functionPluginConfLogicService.findByApiName(testUser, API_NAME))
                .thenReturn(Optional.of(mockConfig));

        // Act
        FunctionPluginConfReq.Result result = objectExtensionService.find(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getConfig());
        assertEquals(API_NAME, result.getConfig().getApiName());
        verify(functionPluginConfLogicService, times(1)).findByApiName(testUser, API_NAME);
    }

    @Test
    @DisplayName("GenerateByAI - 测试find方法配置不存在异常")
    void testFind_ConfigNotFound() {
        // Arrange
        FunctionPluginConfReq.FindArg arg = new FunctionPluginConfReq.FindArg();
        arg.setApiName(API_NAME);

        when(functionPluginConfLogicService.findByApiName(testUser, API_NAME))
                .thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ValidateException.class, () -> {
            objectExtensionService.find(arg, serviceContext);
        });
        verify(functionPluginConfLogicService, times(1)).findByApiName(testUser, API_NAME);
    }

    @Test
    @DisplayName("GenerateByAI - 测试create方法成功场景")
    void testCreate_Success() {
        // Arrange
        FunctionPluginConfReq.Arg arg = new FunctionPluginConfReq.Arg();
        MtFunctionPluginConf inputConfig = createMockConfig();
        arg.setConfig(inputConfig);

        MtFunctionPluginConf resultConfig = createMockConfig();
        when(functionPluginConfLogicService.create(testUser, inputConfig))
                .thenReturn(resultConfig);

        // Act
        FunctionPluginConfReq.Result result = objectExtensionService.create(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getConfig());
        verify(functionPluginConfLogicService, times(1)).create(testUser, inputConfig);
    }

    @Test
    @DisplayName("GenerateByAI - 测试update方法成功场景")
    void testUpdate_Success() {
        // Arrange
        FunctionPluginConfReq.Arg arg = new FunctionPluginConfReq.Arg();
        MtFunctionPluginConf inputConfig = createMockConfig();
        arg.setConfig(inputConfig);

        MtFunctionPluginConf resultConfig = createMockConfig();
        when(functionPluginConfLogicService.update(testUser, inputConfig))
                .thenReturn(resultConfig);

        // Act
        FunctionPluginConfReq.Result result = objectExtensionService.update(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getConfig());
        verify(functionPluginConfLogicService, times(1)).update(testUser, inputConfig);
    }

    @Test
    @DisplayName("GenerateByAI - 测试enable方法成功场景")
    void testEnable_Success() {
        // Arrange
        FunctionPluginConfReq.FindArg arg = new FunctionPluginConfReq.FindArg();
        arg.setApiName(API_NAME);

        MtFunctionPluginConf resultConfig = createMockConfig();
        when(functionPluginConfLogicService.enable(testUser, API_NAME))
                .thenReturn(resultConfig);

        // Act
        FunctionPluginConfReq.Result result = objectExtensionService.enable(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getConfig());
        verify(functionPluginConfLogicService, times(1)).enable(testUser, API_NAME);
    }

    @Test
    @DisplayName("GenerateByAI - 测试disable方法成功场景")
    void testDisable_Success() {
        // Arrange
        FunctionPluginConfReq.FindArg arg = new FunctionPluginConfReq.FindArg();
        arg.setApiName(API_NAME);

        MtFunctionPluginConf resultConfig = createMockConfig();
        when(functionPluginConfLogicService.disable(testUser, API_NAME))
                .thenReturn(resultConfig);

        // Act
        FunctionPluginConfReq.Result result = objectExtensionService.disable(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getConfig());
        verify(functionPluginConfLogicService, times(1)).disable(testUser, API_NAME);
    }

    @Test
    @DisplayName("GenerateByAI - 测试delete方法成功场景")
    void testDelete_Success() {
        // Arrange
        FunctionPluginConfReq.FindArg arg = new FunctionPluginConfReq.FindArg();
        arg.setApiName(API_NAME);

        // Act
        FunctionPluginConfReq.Result result = objectExtensionService.delete(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getConfig());
        verify(functionPluginConfLogicService, times(1)).delete(testUser, API_NAME);
    }

    // ===========================================
    // 边界和异常测试场景
    // ===========================================

    @Test
    @DisplayName("GenerateByAI - 测试list方法RefObjApiName为null的边界场景")
    void testList_NullRefObjApiName() {
        // Arrange
        FunctionPluginConfReq.FindArg arg = new FunctionPluginConfReq.FindArg();
        arg.setRefObjApiName(null);

        when(functionPluginConfLogicService.findAllForList(null, testUser))
                .thenReturn(Lists.newArrayList());

        // Act
        FunctionPluginConfReq.BatchResult result = objectExtensionService.list(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getConfigs());
        assertEquals(0, result.getConfigs().size());
        verify(functionPluginConfLogicService, times(1)).findAllForList(null, testUser);
    }

    @Test
    @DisplayName("GenerateByAI - 测试find方法参数为null的边界场景")
    void testFind_NullArg() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            objectExtensionService.find(null, serviceContext);
        });
        verify(functionPluginConfLogicService, never()).findByApiName(any(), any());
    }

    @Test
    @DisplayName("GenerateByAI - 测试find方法ApiName为null的边界场景")
    void testFind_NullApiName() {
        // Arrange
        FunctionPluginConfReq.FindArg arg = new FunctionPluginConfReq.FindArg();
        arg.setApiName(null);

        when(functionPluginConfLogicService.findByApiName(testUser, null))
                .thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ValidateException.class, () -> {
            objectExtensionService.find(arg, serviceContext);
        });
        verify(functionPluginConfLogicService, times(1)).findByApiName(testUser, null);
    }

    @Test
    @DisplayName("GenerateByAI - 测试create方法参数为null的边界场景")
    void testCreate_NullArg() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            objectExtensionService.create(null, serviceContext);
        });
        verify(functionPluginConfLogicService, never()).create(any(), any());
    }

    @Test
    @DisplayName("GenerateByAI - 测试update方法参数为null的边界场景")
    void testUpdate_NullArg() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            objectExtensionService.update(null, serviceContext);
        });
        verify(functionPluginConfLogicService, never()).update(any(), any());
    }

    @Test
    @DisplayName("GenerateByAI - 测试enable方法参数为null的边界场景")
    void testEnable_NullArg() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            objectExtensionService.enable(null, serviceContext);
        });
        verify(functionPluginConfLogicService, never()).enable(any(), any());
    }

    @Test
    @DisplayName("GenerateByAI - 测试enable方法ApiName为null的边界场景")
    void testEnable_NullApiName() {
        // Arrange
        FunctionPluginConfReq.FindArg arg = new FunctionPluginConfReq.FindArg();
        arg.setApiName(null);

        when(functionPluginConfLogicService.enable(testUser, null))
                .thenReturn(null);

        // Act
        FunctionPluginConfReq.Result result = objectExtensionService.enable(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getConfig());
        verify(functionPluginConfLogicService, times(1)).enable(testUser, null);
    }

    @Test
    @DisplayName("GenerateByAI - 测试enable方法返回null的场景")
    void testEnable_ReturnsNull() {
        // Arrange
        FunctionPluginConfReq.FindArg arg = new FunctionPluginConfReq.FindArg();
        arg.setApiName(API_NAME);

        when(functionPluginConfLogicService.enable(testUser, API_NAME))
                .thenReturn(null);

        // Act
        FunctionPluginConfReq.Result result = objectExtensionService.enable(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getConfig());
        verify(functionPluginConfLogicService, times(1)).enable(testUser, API_NAME);
    }

    @Test
    @DisplayName("GenerateByAI - 测试disable方法参数为null的边界场景")
    void testDisable_NullArg() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            objectExtensionService.disable(null, serviceContext);
        });
        verify(functionPluginConfLogicService, never()).disable(any(), any());
    }

    @Test
    @DisplayName("GenerateByAI - 测试disable方法ApiName为null的边界场景")
    void testDisable_NullApiName() {
        // Arrange
        FunctionPluginConfReq.FindArg arg = new FunctionPluginConfReq.FindArg();
        arg.setApiName(null);

        when(functionPluginConfLogicService.disable(testUser, null))
                .thenReturn(null);

        // Act
        FunctionPluginConfReq.Result result = objectExtensionService.disable(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getConfig());
        verify(functionPluginConfLogicService, times(1)).disable(testUser, null);
    }

    @Test
    @DisplayName("GenerateByAI - 测试disable方法返回null的场景")
    void testDisable_ReturnsNull() {
        // Arrange
        FunctionPluginConfReq.FindArg arg = new FunctionPluginConfReq.FindArg();
        arg.setApiName(API_NAME);

        when(functionPluginConfLogicService.disable(testUser, API_NAME))
                .thenReturn(null);

        // Act
        FunctionPluginConfReq.Result result = objectExtensionService.disable(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getConfig());
        verify(functionPluginConfLogicService, times(1)).disable(testUser, API_NAME);
    }

    @Test
    @DisplayName("GenerateByAI - 测试delete方法参数为null的边界场景")
    void testDelete_NullArg() {
        // Act & Assert
        assertThrows(NullPointerException.class, () -> {
            objectExtensionService.delete(null, serviceContext);
        });
        verify(functionPluginConfLogicService, never()).delete(any(), any());
    }

    @Test
    @DisplayName("GenerateByAI - 测试delete方法ApiName为null的边界场景")
    void testDelete_NullApiName() {
        // Arrange
        FunctionPluginConfReq.FindArg arg = new FunctionPluginConfReq.FindArg();
        arg.setApiName(null);

        // Act
        FunctionPluginConfReq.Result result = objectExtensionService.delete(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getConfig());
        verify(functionPluginConfLogicService, times(1)).delete(testUser, null);
    }

    /**
     * 创建测试用的Mock配置对象
     */
    private MtFunctionPluginConf createMockConfig() {
        MtFunctionPluginConf config = mock(MtFunctionPluginConf.class);
        when(config.getApiName()).thenReturn(API_NAME);
        when(config.getModuleName()).thenReturn(MODULE_NAME);
        when(config.getIsActive()).thenReturn(true);
        when(config.getRefObjectApiName()).thenReturn(REF_OBJ_API_NAME);
        return config;
    }
}
