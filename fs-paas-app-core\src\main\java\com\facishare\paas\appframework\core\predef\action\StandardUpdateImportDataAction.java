package com.facishare.paas.appframework.core.predef.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.OrganizationInfo;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.ImportExportExt;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.ImportConfig;
import com.facishare.paas.appframework.metadata.relation.CalculateFields;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.common.util.UdobjConstants;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.HtmlRichText;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.util.UdobjGrayConfigKey.ADD_TEAM_MEMBER_LIMIT_GRAY;

@Slf4j
public class StandardUpdateImportDataAction extends BaseImportDataAction {
    private Map<Integer, ObjectDataExt> dataInStore = Maps.newHashMap();
    private Map<String, ObjectDataExt> dataInStoreById = Maps.newHashMap();

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        List<String> funPrivilegeCodes = Lists.newArrayList();
        funPrivilegeCodes.addAll(StandardAction.UpdateImportData.getFunPrivilegeCodes());
        if (BooleanUtils.isTrue(arg.getUpdateOwner())) {
            funPrivilegeCodes.addAll(StandardAction.ChangeOwner.getFunPrivilegeCodes());
        }
        return funPrivilegeCodes;
    }

    @Override
    protected void customInit(List<ImportData> dataList) {
        customFillFields(dataList);
        // 根据数据ID查询，校验数据是否重复
        if (arg.getMatchingType() == BaseImportDataAction.MATCHING_TYPE_ID) {
            //根据Id
            fillId(dataList, IObjectData.ID);
        } else if (arg.getMatchingType() == BaseImportDataAction.MATCHING_TYPE_UNIQUE_RULE) {
            // 唯一性规则校验
            stopWatch.lap("validUniquenessRule");
            validUniquenessRule();
            stopWatch.lap("validUniquenessRule end");
        } else if (arg.getMatchingType() == BaseImportDataAction.MATCHING_TYPE_SPECIFIED_FIELD) {
            if (StringUtils.isBlank(arg.getSpecifiedField())) {
                throw new ValidateException(I18N.text(I18NKey.PARAM_EMPTY));
            }
            fillId(dataList, arg.getSpecifiedField());
        } else {
            //根据主属性
            fillId(dataList, IObjectData.NAME);
        }

        // 校验查重逻辑
        validDuplicateSearch();

    }

    protected void customFillFields(List<ImportData> dataList) {

    }

    /**
     * 校验是否填写了数据ID
     */
    @Override
    public void validateNameID(List<ImportData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        if (MATCHING_TYPE_ID == arg.getMatchingType()) {
            //按照ID匹配校验
            List<ImportError> errorList = Lists.newArrayList();
            for (ImportData importData : dataList) {
                IObjectData data = importData.getData();
                if (Objects.isNull(data.getId()) || "".equals(data.getId())) {
                    errorList.add(new ImportError(importData.getRowNo(),
                            I18NExt.getOrDefault(I18NKey.DATA_ID_MUST_FILL, I18NKey.DATA_ID_MUST_FILL, objectDescribeExt.getNameField().getLabel())));
                }
            }
            mergeErrorList(errorList);
        }
    }

    private void fillId(List<ImportData> dataList, String importUniqueField) {
        Optional<IFieldDescribe> importUniqueFieldDescribe = objectDescribeExt.getFieldDescribeSilently(importUniqueField);
        if (!importUniqueFieldDescribe.isPresent()) {
            throw new ValidateException(I18NExt.text(I18NKey.UPDATE_IMPORT_NOT_EXIST_FIELD));
        }
        doValidateMatchTypeFieldValue(dataList, importUniqueFieldDescribe.get());
        //根据主属性查数据
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        SearchTemplateQueryExt templateQueryExt = SearchTemplateQueryExt.of(searchTemplateQuery);
        templateQueryExt.setOffset(0);
        templateQueryExt.setLimit(dataList.size() * 2);   //更新按照主属性导入时，主属性重复的数据不能导入
        templateQueryExt.addIsDeletedFalseFilter();
        templateQueryExt.setPermissionType(1);
        List<String> fieldValueList = dataList.stream()
                .map(a -> a.getData().get(importUniqueField, String.class))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(fieldValueList)) {
            return;
        }
        templateQueryExt.addFilter(Operator.IN, importUniqueField, fieldValueList);
        // 按主属性更新导入,需要按默认值筛选
        if (BooleanUtils.isTrue(importUniqueFieldDescribe.get().getEnableMultiLang())) {
            templateQueryExt.setSearchLang(ISearchTemplateQuery.DEFAULT_SEARCH_LANG);
        }
        QueryResult<IObjectData> dbSearchQuery = queryDataByInit(searchTemplateQuery);

        List<ImportError> errorList = Lists.newArrayList();
        //根据主属性或特定字段导入时，如果该字段不是唯一的，需要校验该字段的唯一性
        // （特定字段的校验可去，特定字段只有唯一的才可以导入，在这块其实不需要处理）
        boolean fieldRepeatable = (arg.getMatchingType() == BaseImportDataAction.MATCHING_TYPE_NAME
                || arg.getMatchingType() == BaseImportDataAction.MATCHING_TYPE_SPECIFIED_FIELD)
                && BooleanUtils.isNotTrue(importUniqueFieldDescribe.get().isUnique());
        for (ImportData importData : dataList) {
            if (fieldRepeatable) {
                long count = dbSearchQuery.getData().stream()
                        .filter(data -> data.get(importUniqueField).equals(importData.getData().get(importUniqueField)))
                        .count();
                if (count > 1) {
                    errorList.add(new ImportError(importData.getRowNo(),
                            I18NExt.getOrDefault(I18NKey.IMPORT_NAME_DUPLICATE, I18NKey.IMPORT_NAME_DUPLICATE,
                                    importUniqueFieldDescribe.get().getLabel(), importData.getData().get(importUniqueField))));
                    continue;
                }
            }
            boolean dataIsExist = false;
            for (IObjectData data : dbSearchQuery.getData()) {
                String dbFieldValue = data.get(importUniqueField, String.class);
                if (Objects.equals(importData.getData().get(importUniqueField, String.class), dbFieldValue)) {
                    importData.getData().setId(data.getId());
                    //控制主属性是否支持更新:1.特定不支持更新主属性 2.根据主属性导入 3.主属性是自定编号
                    if (isNotSupportUpdateName() || IObjectData.NAME.equals(importUniqueField)
                            || FieldDescribeExt.of(objectDescribe.getFieldDescribe(IObjectData.NAME)).isAutoNumber()) {
                        importData.getData().set(IObjectData.NAME, data.getName());
                    }
                    // 处理更新导入相关团队
                    // 更新导入不应该允许添加或者删除相关团队中的负责人
                    dealTeamMemberWithOwner(importData, ObjectDataExt.of(data), errorList);
                    dataIsExist = true;
                    ObjectDataExt dataExt = ObjectDataExt.of(data);
                    dataInStore.put(importData.getRowNo(), dataExt);
                    dataInStoreById.put(data.getId(), dataExt);
                    break;
                }
            }
            if (!dataIsExist) {
                errorList.add(new ImportError(importData.getRowNo(), I18NExt.text(I18NKey.OBJECT_DATA_NOT_FOUND)));
            }
        }
        mergeErrorList(errorList);
    }

    private QueryResult<IObjectData> queryDataByInit(SearchTemplateQuery searchTemplateQuery) {
        MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder()
                .user(actionContext.getUser())
                .isSimple(false)
                .esSearchSkipRecentUpdateCheck(false)
                .skipRelevantTeam(false)
                .searchRichTextExtra(true)
                .build();
        if (AppFrameworkConfig.objectMultiLangGray(actionContext.getTenantId(), actionContext.getObjectApiName())) {
            queryContext.setKeepAllMultiLangValue(true);
        }
        return serviceFacade.findByQueryWithContext(queryContext, objectDescribe.getApiName(), searchTemplateQuery);
    }

    private void doValidateMatchTypeFieldValue(List<ImportData> dataList, IFieldDescribe fieldDescribe) {
        List<ImportError> errorList = Lists.newArrayList();
        for (ImportData importData : dataList) {
            String importUniqueFieldValue = ObjectDataExt.of(importData.getData()).getStringValueInImport(fieldDescribe.getApiName());
            if (StringUtils.isBlank(importUniqueFieldValue)) {
                String label = fieldDescribe.getLabel();
                if (arg.getMatchingType() == BaseImportDataAction.MATCHING_TYPE_ID) {
                    label = I18NExt.getOrDefault(I18NKey.DATAID_LABEL, "唯一性ID");// ignoreI18n
                }
                errorList.add(new ImportError(importData.getRowNo(), I18NExt.getOrDefault(I18NKey.UPDATE_IMPORT_MATCH_FIELD_DATA,
                        "按[{0}]更新导入，[{1}]必须填写。", label, label)));// ignoreI18n
            }
        }
        mergeErrorList(errorList);
    }

    /**
     * 是否支持更新导入修改主属性
     *
     * @return true代表不支持更新
     */
    protected boolean isNotSupportUpdateName() {
        return false;
    }

    @Override
    protected void handelUniqueRuleField(IUniqueRule uniqueRule, IObjectDescribe objectDescribe) {
        if (!Objects.equals(arg.getMatchingType(), BaseImportDataAction.MATCHING_TYPE_UNIQUE_RULE)) {
            return;
        }
        if (!UniqueRuleExt.isEffectiveWhenImport(uniqueRule)) {
            throw new ValidateException(I18N.text(I18NKey.NO_UNIQUE_RULES_AVAILABLE));
        }
        Set<String> unauthorizedFields = serviceFacade.getUnauthorizedFields(actionContext.getUser(), objectDescribe.getApiName());
        UniqueRuleExt.of(uniqueRule).handelHeaderField(objectDescribe, getValidImportFields(), unauthorizedFields);
    }

    @Override
    protected IUdefFunction findFunction() {
        String value = ImportConfig.getFuncRelationValue(objectDescribeExt.getApiName(), ImportConfig.UPDATE, getImportPreProcessing());
        return serviceFacade.getFunctionLogicService().findFunctionByFuncRelationKey(actionContext.getUser(), ImportConfig.IMPORT_RELATION_TYPE,
                value, objectDescribeExt.getApiName()).orElse(null);
    }

    @Override
    protected IObjectData prepareData(ImportData importData) {
        ObjectDataExt objectDataExt = dataInStore.get(importData.getRowNo());
        if (Objects.isNull(objectDataExt)) {
            return importData.getData();
        }
        IObjectData copyData = objectDataExt.copy();
        if (arg.getIsEmptyValueToUpdate()) {
            ObjectDataExt.of(copyData).putAll(ObjectDataExt.of(importData.getData()).toMap());
        } else {
            ObjectDataExt.of(copyData).putAllNotEmptyValue(importData.getData());
        }
        return copyData;
    }

    @Override
    protected void validUniquenessRule() {
        // 更新导入，如果匹配方式为唯一性规则，但没有生效的规则时。抛出异常
        if (Objects.equals(arg.getMatchingType(), BaseImportDataAction.MATCHING_TYPE_UNIQUE_RULE)
                && !UniqueRuleExt.isEffectiveWhenImport(uniqueRule)) {
            throw new ValidateException(I18N.text(I18NKey.NO_UNIQUE_RULES_AVAILABLE));
        }
        super.validUniquenessRule();
    }

    @Override
    protected void customValidate(List<ImportData> dataList) {
        // 校验数据权限
        if (needImportDataPrivilegeCheck()) {
            validateDataPermission(dataList);
        }
        //校验锁定
        validateLock(dataList);
        //校验主对象是否正确
        validateMasterData(dataList);
        //校验跳转的阶段是否合法
        validateStageThrusterField(dataList);
    }

    @Override
    protected void updateDuplicateSearchData(List<IObjectData> actualList) {
        if (!incrementUpdate()) {
            return;
        }
        try {
            List<IObjectData> objectDataList = actualList.stream().map(this::prepareData).collect(Collectors.toList());
            serviceFacade.handleDuplicateSearchRule(duplicatedSearchList, objectDataList, objectDescribe, actionContext.getUser());
        } catch (Exception e) {
            log.error("import updateDuplicateSearchData error ", e);
        }
    }

    private IObjectData prepareData(IObjectData data) {
        ObjectDataExt objectDataExt = dataInStoreById.get(data.getId());
        if (Objects.isNull(objectDataExt)) {
            return data;
        }
        IObjectData copyData = objectDataExt.copy();
        if (BooleanUtils.isTrue(arg.getIsEmptyValueToUpdate())) {
            ObjectDataExt.of(copyData).putAll(ObjectDataExt.of(data).toMap());
        } else {
            ObjectDataExt.of(copyData).putAllNotEmptyValue(data);
        }
        return copyData;
    }

    /**
     * 是否需要校验导入数据的数据权限
     *
     * @return true 需要 false 不需要
     */
    protected boolean needImportDataPrivilegeCheck() {
        return true;
    }

    @Override
    protected void startImportApprovalFlow() {
        // 更新导入不触发审批流
    }

    private void validateStageThrusterField(List<ImportData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        //商机2.0对象特殊处理不走这段逻辑
        if (Utils.NEW_OPPORTUNITY_API_NAME.equals(objectDescribe.getApiName())) {
            return;
        }
        Optional<IFieldDescribe> stageThrusterField = ObjectDescribeExt.of(objectDescribe).getFieldIsUsedByStage();
        // 判断该对象是否有阶段推进器使用的字段;
        if (!stageThrusterField.isPresent()) {
            return;
        }
        // 如果更新模板中没有填写阶段推进器使用的字段，就不校验
        if (!ObjectDataExt.of(dataList.get(0).getData()).toMap().containsKey(stageThrusterField.get().getApiName())) {
            return;
        }

        List<ImportError> errorList = Lists.newArrayList();

        Set<String> ids = dataList.stream()
                .filter(x -> !Strings.isNullOrEmpty(x.getData().getId()))
                .map(x -> x.getData().getId())
                .collect(Collectors.toSet());
        validateMoveToStages(dataList, stageThrusterField, errorList, ids);
        mergeErrorList(errorList);

    }

    private void validateMoveToStages(List<ImportData> dataList, Optional<IFieldDescribe> stageThrusterField, List<ImportError> errorList, Set<String> ids) {

        Map<String, List<String>> canMoveToStagesOfObjects = serviceFacade.canMoveToStagesOfObjects(actionContext.getUser(), objectDescribe.getApiName(), ids);
        if (CollectionUtils.empty(canMoveToStagesOfObjects)) {
            return;
        }
        dataList.forEach(importData -> {
            String stageThrusterValue = (String) importData.getData().get(stageThrusterField.get().getApiName());
            if (!arg.getIsEmptyValueToUpdate() && StringUtils.isBlank(stageThrusterValue)) {
                // 不填写阶段推进器配置的单选字段不进行校验
                return;
            }
            // 阶段推进器重新激活
            if (Objects.isNull(canMoveToStagesOfObjects.get(importData.getData().getId()))) {
                return;
            }
            if (!canMoveToStagesOfObjects.get(importData.getData().getId()).contains(stageThrusterValue)) {
                // TODO: 2019-02-27 确认提示信息之后改为多语
                errorList.add(new ImportError(importData.getRowNo(), "不可跳转到该阶段")); // ignoreI18n
            }
        });
    }

    private void validateMasterData(List<ImportData> dataList) {
        if (!objectDescribeExt.isSlaveObject() || CollectionUtils.empty(dataList)) {
            return;
        }

        Optional<MasterDetailFieldDescribe> masterDetailField = objectDescribeExt.getMasterDetailFieldDescribe();
        if (!masterDetailField.isPresent() || !dataList.get(0).containsField(masterDetailField.get().getApiName())) {
            return;
        }

        String masterApiName = masterDetailField.get().getApiName();
        List<ImportError> errorList = Lists.newArrayList();
        dataList.forEach(data -> {
            ObjectDataExt objectData = dataInStore.get(data.getRowNo());
            if (!Objects.isNull(objectData) &&
                    !Objects.isNull(data.getData().get(masterApiName)) &&
                    !Objects.equals(objectData.get(masterApiName), data.getData().get(masterApiName))) {
                errorList.add(new ImportError(data.getRowNo(), I18NExt.text(I18NKey.RELATED_MUSTER_UNLIKE)));
            }
        });
        mergeErrorList(errorList);
    }

    @Override
    protected void customDefaultValue(List<IObjectData> validList) {
        removeMultiCurrencyFields(validList);
        //处理为空的数据是否导入
        removeNonUpdateField(validList);
        removeNoIdData(validList);
        mergeSystemField(validList);
    }

    protected void removeMultiCurrencyFields(List<IObjectData> validList) {
        //多货币字段不能更新为空
        validList.forEach(x -> ObjectDataExt.of(x).removeEmptyMultiCurrencyFields());
    }

    @Override
    protected final void calculateFormula(List<IObjectData> validDataList) {
        try {
            //更新数据需要先和数据库里的数据合并再计算
            validDataList.stream().filter(x -> dataInStoreById.containsKey(x.getId())).forEach(x -> {
                ObjectDataExt.of(x).merge(dataInStoreById.get(x.getId()));
            });
            CalculateFields calculateFields = infraServiceFacade.computeCalculateFieldsForAddAction(objectDescribe, null, true);
            serviceFacade.batchCalculateBySortFields(actionContext.getUser(), validDataList, calculateFields.getDescribeMap(), calculateFields.getCalculateFieldMap());
        } catch (Exception e) {
            log.error("calculateFormula failed", e);
        }
    }

    @Override
    protected void setupTeamInterconnectedDepartments() {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.TEAM_INTERCONNECTED_DEPARTMENT_BY_DATA_OWNER_GRAY, actionContext.getTenantId())
                && BooleanUtils.isTrue(arg.getUpdateOwner())) {
            super.setupTeamInterconnectedDepartments();
        }
    }

    @Override
    protected boolean isFillOutOwner(ObjectDataExt objectDataExt) {
        // 更新导入、无外部负责人字段，不做任何处理
        if (!objectDataExt.containsField(IObjectData.OUT_OWNER)) {
            return false;
        }
        return super.isFillOutOwner(objectDataExt);
    }

    @Override
    protected boolean isValidateOutOwner(ObjectDataExt objectDataExt) {
        // 更新导入、无外部负责人字段，不做任何处理
        if (!objectDataExt.containsField(IObjectData.OUT_OWNER)) {
            return false;
        }
        return super.isValidateOutOwner(objectDataExt);
    }

    @Override
    protected void fillOutOwner(List<ImportData> dataList) {
        if (BooleanUtils.isTrue(arg.getIsEmptyValueToUpdate())) {
            for (ImportData importData : dataList) {
                ObjectDataExt objectDataExt = ObjectDataExt.of(importData.getData());
                objectDataExt.synchronizeOutTeamMemberOwner("", "");
            }
            return;
        }
        super.fillOutOwner(dataList);
    }

    @Override
    protected void fillOutTeamMember(List<ImportData> dataList) {
        // 下游导入，为空更新时，当负责人被更新为空，需要清空相关团队的负责人
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OUTER_IMPORT_SUPPORT_OUT_OWNER_GRAY, actionContext.getTenantId()) || !actionContext.getUser().isOutUser()) {
            return;
        }
        if (BooleanUtils.isTrue(arg.getIsEmptyValueToUpdate())) {
            for (ImportData importData : dataList) {
                ObjectDataExt objectDataExt = ObjectDataExt.of(importData.getData());
                Optional<String> outOwnerId = objectDataExt.getOutOwnerId();
                if (!outOwnerId.isPresent()) {
                    objectDataExt.synchronizeOutTeamMemberOwner("", "");
                }
            }
        }
    }

    @Override
    protected boolean removeOutTeamMember() {
        return BooleanUtils.isTrue(arg.getRemoveOutTeamMember());
    }

    @Override
    protected IObjectData getValidationFunctionObjectData(IObjectData objectData) {
        ObjectDataExt dbDataExt = dataInStoreById.get(objectData.getId());
        ObjectDataExt.of(objectData).merge(dbDataExt);
        return objectData;
    }

    private void removeNoIdData(List<IObjectData> validList) {
        validList.removeIf(a -> Objects.isNull(a.getId()));
    }

    private void mergeSystemField(List<IObjectData> validList) {
        validList.forEach(data -> {
            data.setDescribeId(objectDescribeExt.getId());
            ObjectDataExt objectDataExt = dataInStoreById.get(data.getId());
            data.setVersion(objectDataExt.getVersion());
        });
    }

    private void removeNonUpdateField(List<IObjectData> validList) {
        if (arg.getIsEmptyValueToUpdate()) {
            return;
        }

        validList.forEach(data -> {
            ObjectDataDocument dataDocument = ObjectDataDocument.of(data);
            dataDocument.entrySet().removeIf(next -> {
                String value = getStringValue(data, next.getKey());
                return Strings.isNullOrEmpty(value);
            });
        });
    }

    @Override
    protected List<IObjectData> importData(List<IObjectData> validList) {
        RequestContext requestContext = RequestContextManager.getContext();
        requestContext.setAttribute(RequestContext.Attributes.TRIGGER_WORK_FLOW, arg.getIsWorkFlowEnabled());
        requestContext.setAttribute(ActionContextExt.IS_ALLOW_DELETE_ALL_TEAM_MEMBER, Boolean.TRUE);
        ObjectDataExt.correctValue(actionContext.getUser(), validList, objectDescribe);
        IActionContext context = ActionContextExt.of(actionContext.getUser(), requestContext)
                .setObjectDataSource("import").setActionType("updateImport").getContext();
        if (incrementUpdate()) {
            // 从所有data中获取所有需要更新的字段
            List<String> updateFieldList = validList.stream()
                    .map(IObjectData::getDataField)
                    .flatMap(Set::stream)
                    .distinct()
                    .collect(Collectors.toList());
            if (log.isDebugEnabled()) {
                log.debug("importData batchUpdateWithData,user:{},updateFieldList:{}", actionContext.getUser(), updateFieldList);
            }
//            return serviceFacade.batchUpdateByFields(context, validList, updateFieldList);
            return serviceFacade.batchUpdateWithData(context, validList, updateFieldList);
        }
        return serviceFacade.batchUpdate(context, validList, actionContext.getUser());
    }

    protected boolean incrementUpdate() {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.IMPORT_INCREMENT_UPDATE_FIELD, actionContext.getTenantId())
                || UdobjGrayConfig.isAllow(UdobjGrayConfigKey.IMPORT_INCREMENT_UPDATE_FIELD_GRAY, actionContext.getTenantId());
    }

    @Override
    protected void validUniquenessRuleInDB() {
        List<String> fieldNames = getRuleFieldName(uniqueRule);
        if (CollectionUtils.empty(fieldNames)) {
            // 没有可以使用的唯一性规则字段
            List<ImportError> errorList = dataList.stream().map(importData -> new ImportError(importData.getRowNo(),
                            I18NExt.getOrDefault(I18NKey.UNIQUENESS_CONFIGURATION_ERROR, I18NKey.UNIQUENESS_CONFIGURATION_ERROR,
                                    UniqueRuleExt.of(uniqueRule).joiningFieldLabel(" and ", objectDescribeExt))))
                    .collect(Collectors.toList());
            mergeErrorList(errorList);
            return;
        }

        Map<String, List<String>> duplicateIDMap = findDuplicateDataMapByUniquenessRule();

        Map<String, IObjectData> objectDataMap = getObjectDataMap(duplicateIDMap);

        List<ImportError> errorList = Lists.newArrayList();
        for (ImportData importData : dataList) {
            String id = importData.getData().getId();
            if (objectDataMap.containsKey(id)) {
                ObjectDataExt dataExt = ObjectDataExt.of(objectDataMap.get(id));
                importData.getData().setId(dataExt.getId());
                // 处理相关团队负责人
                dealTeamMemberWithOwner(importData, dataExt, errorList);
                dataInStore.put(importData.getRowNo(), dataExt);
                dataInStoreById.put(dataExt.getId(), dataExt);
                continue;
            }

            // ES中没有查到结果
            if (CollectionUtils.empty(duplicateIDMap.get(id))) {
                ImportError importError = new ImportError(importData.getRowNo(),
                        I18NExt.getOrDefault(I18NKey.VALID_UNIQUENESS_MESSAGE_NOT_FOUND, I18NKey.VALID_UNIQUENESS_MESSAGE_NOT_FOUND,
                                UniqueRuleExt.of(uniqueRule).joiningFieldLabel(" and ", objectDescribeExt)));
                errorList.add(importError);
                continue;
            }
            // ES中查到多个结果
            if (duplicateIDMap.get(id).size() > 1) {
                ImportError importError = new ImportError(importData.getRowNo(),
                        I18NExt.getOrDefault(I18NKey.VALID_UNIQUENESS_MESSAGE_DUPLICATED, I18NKey.VALID_UNIQUENESS_MESSAGE_DUPLICATED,
                                UniqueRuleExt.of(uniqueRule).joiningFieldLabel(" and ", objectDescribeExt)));
                errorList.add(importError);
                continue;
            }
            // 数据在ES中查到但在数据库中没有找到
            ImportError importError = new ImportError(importData.getRowNo(),
                    I18NExt.getOrDefault(I18NKey.VALID_UNIQUENESS_MESSAGE_NOT_FOUND, I18NKey.VALID_UNIQUENESS_MESSAGE_NOT_FOUND,
                            UniqueRuleExt.of(uniqueRule).joiningFieldLabel(" and ", objectDescribeExt)));
            errorList.add(importError);
            log.warn("data not found in db, tenantId=>{}, describeApiName=>{}, dataId=>{}",
                    actionContext.getTenantId(), objectDescribeExt.getApiName(), duplicateIDMap.get(id));
        }

        mergeErrorList(errorList);
    }

    /**
     * 处理相关团队负责人
     *
     * @param importData 导入数据
     * @param dataExt    查询出的数据
     */
    private void dealTeamMemberWithOwner(ImportData importData, ObjectDataExt dataExt, List<ImportError> errorList) {
        ObjectDataExt importDataExt = ObjectDataExt.of(importData.getData());
        // 处理更新导入相关团队
        if (CollectionUtils.empty(importDataExt.getTeamMembers())) {
            // 用户没有传相关团队时,使用原来相关团队数据
            importDataExt.setTeamMembers(dataExt.getTeamMembers());
            dealOwner(importData, dataExt);
            return;
        }
        // 更新导入不允许添加或者删除相关团队中的负责人
        if (BooleanUtils.isTrue(arg.getIsEmptyValueToUpdate()) || CollectionUtils.notEmpty(importDataExt.getTeamMembers())) {
            // 只有相关团队字段不为空，才处理负责人，因为不传该字段，元数据不会更新
            List<TeamMember> oldTeamMember = dataExt.getTeamMembers();
            List<TeamMember> oldOwnerAndOutOwner = oldTeamMember.stream()
                    .filter(teamMember -> teamMember.getRole() == TeamMember.Role.OWNER
                            && teamMember.getMemberType() == TeamMember.MemberType.EMPLOYEE)
                    .collect(Collectors.toList());
            // 移除用户输入的负责人和外部负责人
            importDataExt.getTeamMembers().removeIf(teamMember -> teamMember.getRole() == TeamMember.Role.OWNER
                    && teamMember.getMemberType() == TeamMember.MemberType.EMPLOYEE);
            importDataExt.addTeamMembers(oldOwnerAndOutOwner);
            if (TeamMember.isTeamMemberTypeExportGray(actionContext.getTenantId())) {
                //补充外部相关的成员
                List<TeamMember> otherTeamMembers = oldTeamMember.stream()
                        .filter(TeamMember::isOutMember)
                        .collect(Collectors.toList());
                importDataExt.addTeamMembers(otherTeamMembers);
            } else {
                //补充除了人员的其他相关团队成员
                List<TeamMember> otherTeamMembers = oldTeamMember.stream()
                        .filter(x -> x.isOutMember() || !TeamMember.MemberType.EMPLOYEE.equals(x.getMemberType()))
                        .collect(Collectors.toList());
                importDataExt.addTeamMembers(otherTeamMembers);
            }
            List<TeamMember> teamMembers = importDataExt.getTeamMembers();
            if (UdobjGrayConfig.isAllow(ADD_TEAM_MEMBER_LIMIT_GRAY, actionContext.getTenantId())) {
                int addTeamMemberLimit = AppFrameworkConfig.getAddTeamMemberLimit(actionContext.getTenantId());
                Map<TeamMember.MemberType, Map<String, List<TeamMember>>> teamMemberListMap = teamMembers.stream()
                        .collect(Collectors.groupingBy(TeamMember::getMemberType, Collectors.groupingBy(TeamMember::getEmployee)));
                long count = teamMemberListMap.values().stream().mapToLong(x -> x.keySet().size()).sum();
                if (count > addTeamMemberLimit) {
                    String error = I18NExt.text(I18NKey.ADD_TEAM_MEMBER_LIMIT, addTeamMemberLimit, count - addTeamMemberLimit);
                    errorList.add(ImportError.builder().errorMessage(error).rowNo(importData.getRowNo()).build());
                }
            }
        } else {
            importDataExt.setTeamMembers(dataExt.getTeamMembers());
        }
        dealOwner(importData, dataExt);
    }

    private void dealOwner(ImportData importData, ObjectDataExt dataExt) {
        ObjectDataExt importDataExt = ObjectDataExt.of(importData.getData());
        if (CollectionUtils.empty(importDataExt.getOwner()) && CollectionUtils.empty(dataExt.getOwner())) {
            return;
        }
        if (BooleanUtils.isTrue(arg.getUpdateOwner()) && importDataExt.getOwnerId().isPresent()) {
            dealOwnerTeamMember(importData, dataExt);
            doOtherObjectsOwner(importDataExt, dataExt);
        } else {
            importDataExt.setOwner(dataExt.getOwner());
        }
    }

    protected void doOtherObjectsOwner(ObjectDataExt importDataExt, ObjectDataExt dataExt) {

    }

    private void dealOwnerTeamMember(ImportData importData, ObjectDataExt dataExt) {
        Optional<String> oldOwnerId = dataExt.getOwnerId();
        ObjectDataExt importDataExt = ObjectDataExt.of(importData.getData());
        Optional<String> ownerId = importDataExt.getOwnerId();
        if (ownerId.isPresent() && oldOwnerId.isPresent() && StringUtils.equals(ownerId.get(), oldOwnerId.get())) {
            return;
        }
        importData.setOwnerChange(true);
        //清空原有相关团队上游负责人
        importDataExt.getTeamMembers().removeIf(x -> TeamMember.Role.OWNER.equals(x.getRole())
                && TeamMember.MemberType.EMPLOYEE.equals(x.getMemberType())
                && !x.isOutMember());
        //处理现在的负责人
        if (ownerId.isPresent()) {
            TeamMember owner = new TeamMember(ownerId.get(), TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE);
            importDataExt.addTeamMembers(Lists.newArrayList(owner));
        }
        if (Objects.nonNull(arg.getOldOwnerTeamMember())
                && CollectionUtils.notEmpty(arg.getOldOwnerTeamMember().getRoleList())
                && oldOwnerId.isPresent()
                && StringUtils.equals(arg.getOldOwnerTeamMember().getStrategy(),
                UdobjConstants.CHANGE_OWNER_STRATEGY.SET_ORIGINAL_OWNER_TO_NORMAL_MEMBER.getValue())
                && getRelatedTeamEnabled()) {
            List<TeamMember> teamMembers = Lists.newArrayList();
            boolean teamRoleGray = TeamMember.isTeamRoleGray(actionContext.getTenantId());
            for (String roleType : arg.getOldOwnerTeamMember().getRoleList()) {
                //处理原负责人的相关团队
                TeamMember teamMember;
                if (teamRoleGray) {
                    teamMember = new TeamMember(oldOwnerId.get(), roleType,
                            TeamMember.Permission.of(arg.getOldOwnerTeamMember().getPermissionType()), null);
                } else {
                    teamMember = new TeamMember(oldOwnerId.get(), TeamMember.Role.of(roleType),
                            TeamMember.Permission.of(arg.getOldOwnerTeamMember().getPermissionType()));
                }
                teamMembers.add(teamMember);
            }
            importDataExt.addTeamMembers(teamMembers);
        }
    }

    /**
     * @param duplicateIDMap ES查询结果，key为生成的临时ID，value为ES中查到重复数据ID
     * @return key 为生成的临时ID，value为对应的重复数据
     */
    private Map<String, IObjectData> getObjectDataMap(Map<String, List<String>> duplicateIDMap) {
        // 根据ES查到数id来分组，key是ES中的数据Id，value是自己生成的临时id
        Map<String, List<String>> idMap = duplicateIDMap.entrySet().stream()
                .filter(entry -> CollectionUtils.notEmpty(entry.getValue()) && entry.getValue().size() == 1)
                .collect(Collectors.groupingBy(x -> x.getValue().get(0), Collectors.mapping(Map.Entry::getKey, Collectors.toList())));

        IActionContext context = ActionContextExt.of(actionContext.getUser())
                .setKeepAllMultiLangValue(true)
                .getContext();
        List<IObjectData> objectDataByIds = serviceFacade.findObjectDataByIds(context, Lists.newArrayList(idMap.keySet()), objectDescribeExt.getApiName());

        Map<String, IObjectData> objectDataMap = objectDataByIds.stream().collect(Collectors.toMap(IObjectData::getId, x -> x));

        Map<String, IObjectData> resultMap = Maps.newHashMap();
        objectDataMap.forEach((id, objectData) -> idMap.get(id).forEach(x -> resultMap.put(x, objectData)));
        return resultMap;
    }


    @Override
    protected void convertFields(List<BaseImportDataAction.ImportData> dataList) {
        if (!isSupportFieldMapping()) {
            if (MATCHING_TYPE_ID == arg.getMatchingType()) {
                //补充_id字段
                ImportExportExt.convertNameToID(dataList, arg.getRows());
            }
            if (MATCHING_TYPE_ID == arg.getMatchingType() || importReferenceFieldMappingSwitch) {
                // 替换关联/主从字段为ID
                ImportExportExt.convertReferenceNameToID(dataList, arg.getRows(), objectDescribeExt, null, importReferenceFieldMappingSwitch);
            }
        }
        super.convertFields(dataList);
    }

    private void validateLock(List<ImportData> dataList) {
        List<ImportError> errorList = Lists.newArrayList();
        dataList.forEach(data -> {
            ObjectDataExt objectData = dataInStore.get(data.getRowNo());
            if (!Objects.isNull(objectData) && objectData.isLock()) {
                errorList.add(new ImportError(data.getRowNo(), I18NExt.text(I18NKey.DATA_LOCKED)));
            }
        });
        mergeErrorList(errorList);
    }

    private void validateDataPermission(List<ImportData> importDataList) {
        List<IObjectData> dataList = Lists.newArrayList();
        for (ImportData importData : importDataList) {
            IObjectData data = dataInStore.get(importData.getRowNo());
            if (Objects.nonNull(data)) {
                dataList.add(data);
            }
        }
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        Map<String, Permissions> permissionsMap = serviceFacade.checkDataPrivilege(actionContext.getUser(),
                dataList, objectDescribeExt, ObjectAction.UPDATE.getActionCode());
        List<ImportError> errorList = Lists.newArrayList();
        importDataList.stream()
                .filter(a -> !Objects.isNull(a.getData().getId()))
                .forEach(data -> {
                    Permissions permissions = permissionsMap.get(data.getData().getId());
                    if (Objects.isNull(permissions) || !Objects.equals(Permissions.READ_WRITE, permissions)) {
                        errorList.add(new ImportError(data.getRowNo(), I18NExt.text(I18NKey.USER_NO_DATA_PRIVILEGE)));
                    }
                });
        mergeErrorList(errorList);
    }

    @Override
    protected void startImportWorkFlow(List<IObjectData> actualList) {
    }

    @Override
    protected void recordImportDataLog(List<IObjectData> actualList) {
        if (CollectionUtils.empty(actualList)) {
            return;
        }
        //过滤掉禁用字段
        Set<String> removeFields = objectDescribeExt.getFieldDescribesSilently().stream()
                .filter(f -> !f.isActive())
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toSet());
        //更新导入支持导入合作伙伴和外部负责人，过滤下游企业的修改记录
        removeFields.add(IObjectData.OUT_TENANT_ID);
        actualList.forEach(a -> ObjectDataExt.of(a).remove(removeFields));
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            List<HtmlRichText> richTextFields = objectDescribeExt.getRichTextFields();
            if (CollectionUtils.notEmpty(richTextFields) && UdobjGrayConfig.isAllow("html_rich_text_log_gray", actionContext.getTenantId())) {
                Table<String, String, Object> table = HashBasedTable.create();
                for (HtmlRichText richTextField : richTextFields) {
                    dataInStoreById.forEach((id, objectDataExt) -> {
                        Object richTextValue = objectDataExt.get(richTextField.getApiName());
                        if (Objects.isNull(richTextValue)) {
                            richTextValue = "";
                        }
                        Object richTextAbstractValue = objectDataExt.get(RichTextExt.getRichTextAbstractName(richTextField.getApiName()));
                        if (Objects.isNull(richTextAbstractValue)) {
                            richTextAbstractValue = "";
                        }
                        table.put(id, richTextField.getApiName(), richTextValue);
                        table.put(id, RichTextExt.getRichTextAbstractName(richTextField.getApiName()), richTextAbstractValue);
                    });
                }
                log.info("updateImport htmlRichText,user:{},table:{}", actionContext.getUser(), table);
            }
            Map<String, IObjectData> dbDataMap = dataInStoreById.values().stream().collect(Collectors.toMap(ObjectDataExt::getId, ObjectDataExt::getObjectData));
            serviceFacade.updateImportLog(actionContext.getUser(), EventType.MODIFY, ActionType.UpdateImport, objectDescribe, actualList, dbDataMap, getLogExtendsInfo());
        });
        parallelTask.run();

        // 记录添加或者移除相关团队
        if (!objectDescribeExt.isSlaveObject()) {
            List<IObjectData> objectDataList = ObjectDataExt.copyList(actualList);
            // 用户没有传相关团队时，从修改记录
            objectDataList.removeIf(data -> Objects.isNull(data.get(ObjectDataExt.RELEVANT_TEAM)));
            List<IObjectData> dataInStoreList = Lists.newArrayList(dataInStoreById.values());
            serviceFacade.logByActionType(actionContext.getUser(), EventType.MODIFY, ActionType.AddEmployee, dataInStoreList, objectDataList, objectDescribe);
            serviceFacade.logByActionType(actionContext.getUser(), EventType.MODIFY, ActionType.RemoveEmployee, dataInStoreList, objectDataList, objectDescribe);
            if (BooleanUtils.isTrue(arg.getUpdateOwner())) {
                serviceFacade.logByActionType(actionContext.getUser(), EventType.MODIFY, ActionType.ChangeOwner, dataInStoreList, objectDataList, objectDescribe);
            }
        }

    }

    @Override
    protected void validateUniqueDataInDB() {

        Set<String> uniqueFieldApiName = objectDescribeExt.getActiveFieldDescribes().stream()
                .filter(IFieldDescribe::isUnique)
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toSet());
        if (CollectionUtils.empty(uniqueFieldApiName)) {
            return;
        }

        List<ImportError> errorList = Lists.newArrayList();
        for (ImportData importData : dataList) {
            String str = infraServiceFacade.checkUnique(importData.getData(), objectDescribe, true);
            if (!Strings.isNullOrEmpty(str)) {
                errorList.add(new ImportError(importData.getRowNo(), str));
            }
        }
        mergeErrorList(errorList);
    }

    @Override
    protected String getParentSelectOneValue(IObjectData data, String parentApiName) {
        String value = getStringValue(data, parentApiName);
        if (Strings.isNullOrEmpty(value) && !Objects.isNull(data.getId())) {
            ObjectDataExt objectDataExt = dataInStoreById.get(data.getId());
            if (!Objects.isNull(objectDataExt)) {
                value = String.valueOf(objectDataExt.get(parentApiName));
            }
        }
        return value;
    }

    @Override
    protected boolean checkUniqueInDB(IObjectData data, IObjectDescribe describe) {
        return infraServiceFacade.isUniqueCheck(data, describe, true);
    }

    @Override
    protected boolean customFilterHeader(String apiName) {
        if (Strings.isNullOrEmpty(apiName)) {
            return true;
        }
        List<IFieldDescribe> validImportFields = getValidImportFields();
        return validImportFields.stream().noneMatch(f -> apiName.equals(f.getApiName()));
    }

    @Override
    protected List<IFieldDescribe> getValidImportFields() {
        if (CollectionUtils.empty(validFieldDescribeList)) {
            List<IFieldDescribe> updateImportFields = infraServiceFacade.getUpdateImportTemplateField(actionContext.getUser(), objectDescribe);
            if (CollectionUtils.empty(updateImportFields)) {
                return Lists.newArrayList();
            }
            updateImportFields.removeIf(x -> (IFieldType.AUTO_NUMBER.equals(x.getType()) &&
                    !(Objects.equals(arg.getSpecifiedField(), x.getApiName()) || IObjectData.NAME.equals(x.getApiName()))));
            return validFieldDescribeList = updateImportFields;
        }
        return validFieldDescribeList;
    }

    @Override
    protected void fillDataOwnDeptAndOrganization() {
        // 从对象不支持导入归属部门
        if (objectDescribeExt.isSlaveObject()) {
            return;
        }
        Set<Integer> errorRowNoSet = allErrorList.stream().map(ImportError::getRowNo).collect(Collectors.toSet());
        User user = actionContext.getUser();
        Set<String> ownerIds = dataList.stream()
                .filter(ImportData::isOwnerChange)
                .filter(importData -> !errorRowNoSet.contains(importData.getRowNo()))
                .map(ImportData::getData)
                .map(data -> ObjectDataExt.of(data).getOwnerId().orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        //更新导入不支持清空负责人
        if (CollectionUtils.empty(ownerIds)) {
            return;
        }
        OrganizationInfo organizationInfo = serviceFacade.findMainOrgAndDeptByUserId(actionContext.getTenantId(), user.getUserId(), Lists.newArrayList(ownerIds));
        fillDataOwnDept(errorRowNoSet, organizationInfo);
    }

    @Override
    protected Map<String, Set<String>> fillFieldValue(List<IObjectData> objectDataList) {
        return Collections.emptyMap();
    }

    private void fillDataOwnDept(Set<Integer> errorRowNoSet, OrganizationInfo organizationInfo) {
        for (ImportData importData : dataList) {
            if (errorRowNoSet.contains(importData.getRowNo()) || !importData.isOwnerChange()) {
                continue;
            }
            ObjectDataExt dataExt = ObjectDataExt.of(importData.getData());
            dataExt.getOwnerId()
                    .map(organizationInfo::getMainDeptId)
                    .ifPresent(dataExt::setDataOwnDepartmentId);
        }
    }


    @Override
    protected boolean isCheckExcelUniqueField(IFieldDescribe fieldDescribe, String fieldValue) {
        return (BooleanUtils.isTrue(fieldDescribe.isUnique())
                || (MATCHING_TYPE_ID == arg.getMatchingType() && IObjectData.ID.equals(fieldDescribe.getApiName()))
                || (MATCHING_TYPE_NAME == arg.getMatchingType() && IObjectData.NAME.equals(fieldDescribe.getApiName()))) && StringUtils.isNotBlank(fieldValue);
    }

    @Override
    protected void getPhoneNumberInfo(List<ImportData> dataList) {
    }

    /**
     * fieldDescribe字段的值为空且为必填
     *
     * @return null or 提示信息
     */
    @Override
    protected String checkWithMustFillField(IFieldDescribe fieldDescribe) {
        if (arg.getIsEmptyValueToUpdate()) {
            return super.checkWithMustFillField(fieldDescribe);
        }
        // 主属性匹配的更新导入，主属性字段不能为空
        if (IObjectData.NAME.equals(fieldDescribe.getApiName()) && MATCHING_TYPE_NAME == arg.getMatchingType()) {
            return super.checkWithMustFillField(fieldDescribe);
        }
        if (Objects.equals(fieldDescribe.getApiName(), arg.getSpecifiedField()) && MATCHING_TYPE_SPECIFIED_FIELD == arg.getMatchingType()) {
            return super.checkWithMustFillField(fieldDescribe);
        }
        return null;
    }

    @Override
    protected boolean updateMultiLangFieldEmptyValue() {
        return BooleanUtils.isTrue(arg.getIsEmptyValueToUpdate());
    }

    /**
     * 获取数据库中的数据
     */
    protected Map<Integer, ObjectDataExt> getDataInStore() {
        return dataInStore;
    }

    protected Map<String, ObjectDataExt> getDataInStoreById() {
        return dataInStoreById;
    }
}
