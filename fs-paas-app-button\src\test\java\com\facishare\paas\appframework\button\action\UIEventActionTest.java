package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * UIEventAction单元测试 - JUnit5版本
 * 
 * GenerateByAI
 * 
 * 测试UIEventAction UI事件动作的核心功能：
 * - getType: 返回UI_EVENT类型
 * - invoke: 主入口方法和UI事件处理逻辑
 * - getActionParameter: 参数解析
 * - 返回值结构验证
 * 
 * 覆盖场景：
 * - 正常UI事件处理流程
 * - 不同UI事件ID的处理
 * - 参数解析和验证
 * - 返回值结构的正确性
 * - JSON参数的解析异常处理
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("UIEventAction - UI事件动作测试")
class UIEventActionTest {

    @InjectMocks
    private UIEventAction uiEventAction;

    // 测试常量
    private static final String TENANT_ID = "74255";
    private static final String USER_ID = "1000";
    private static final String DESCRIBE_API_NAME = "TestObject";
    private static final String BUTTON_API_NAME = "testUIEventButton";
    private static final String UI_EVENT_ID = "testUIEvent123";

    private User testUser;
    private IObjectData testObjectData;
    private IObjectDescribe testDescribe;
    private IUdefButton testButton;
    private IUdefAction testAction;
    private ActionExecutorContext testContext;
    private ButtonExecutor.Arg testArg;

    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser = new User(TENANT_ID, USER_ID);
        
        // 创建测试对象数据
        testObjectData = new ObjectData();
        testObjectData.set("id", "123");
        testObjectData.set("name", "test ui event");
        
        // 创建Mock对象
        testDescribe = mock(IObjectDescribe.class);
        testButton = mock(IUdefButton.class);
        testAction = mock(IUdefAction.class);
        testContext = mock(ActionExecutorContext.class);
        testArg = mock(ButtonExecutor.Arg.class);
        
        // 配置基础Mock行为
        when(testDescribe.getApiName()).thenReturn(DESCRIBE_API_NAME);
        when(testButton.getApiName()).thenReturn(BUTTON_API_NAME);
        when(testContext.getUser()).thenReturn(testUser);
        when(testContext.getDescribe()).thenReturn(testDescribe);
        when(testContext.getButton()).thenReturn(testButton);
        when(testContext.getAction()).thenReturn(testAction);
        when(testArg.getObjectData()).thenReturn(testObjectData);
        when(testArg.toDetails()).thenReturn(Collections.emptyMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getType方法返回UI_EVENT类型
     */
    @Test
    @DisplayName("getType - 返回UI_EVENT类型")
    void testGetType_ReturnsUIEventType() {
        // When
        ActionExecutorType result = uiEventAction.getType();
        
        // Then
        assertNotNull(result, "类型不应为null");
        assertEquals(ActionExecutorType.UI_EVENT, result, "应返回UI_EVENT类型");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法的正常UI事件处理流程
     */
    @Test
    @DisplayName("invoke - 正常UI事件处理流程")
    void testInvoke_NormalUIEventFlow() {
        // Given
        String actionParameterJson = "{\n"
                + "    \"ui_event_id\": \"" + UI_EVENT_ID + "\",\n"
                + "    \"func_api_name\": \"testFunction\",\n"
                + "    \"func_args\": []\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        
        // When
        ButtonExecutor.Result result = uiEventAction.invoke(testArg, testContext);
        
        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isHasReturnValue(), "应有返回值");
        assertEquals("UIEvent", result.getReturnType(), "返回类型应为UIEvent");
        assertEquals(UI_EVENT_ID, result.getReturnValue(), "返回值应为UI事件ID");
        
        // 验证getActionParamter被调用
        verify(testAction).getActionParamter();
    }

    /**
     * GenerateByAI
     * 测试内容描述：参数化测试不同的UI事件ID
     */
    @ParameterizedTest
    @ValueSource(strings = {
        "event1", 
        "event_with_underscore", 
        "event-with-dash", 
        "event123", 
        "complexEvent_456-test",
        "中文事件ID"
    })
    @DisplayName("invoke - 不同UI事件ID处理")
    void testInvoke_DifferentUIEventIds(String uiEventId) {
        // Given
        String actionParameterJson = "{\n"
                + "    \"ui_event_id\": \"" + uiEventId + "\",\n"
                + "    \"func_api_name\": \"testFunction\",\n"
                + "    \"func_args\": []\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        
        // When
        ButtonExecutor.Result result = uiEventAction.invoke(testArg, testContext);
        
        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isHasReturnValue(), "应有返回值");
        assertEquals("UIEvent", result.getReturnType(), "返回类型应为UIEvent");
        assertEquals(uiEventId, result.getReturnValue(), "返回值应为对应的UI事件ID");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法处理null UI事件ID
     */
    @Test
    @DisplayName("invoke - null UI事件ID处理")
    void testInvoke_NullUIEventId() {
        // Given
        String actionParameterJson = "{\n"
                + "    \"ui_event_id\": null,\n"
                + "    \"func_api_name\": \"testFunction\",\n"
                + "    \"func_args\": []\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        
        // When
        ButtonExecutor.Result result = uiEventAction.invoke(testArg, testContext);
        
        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isHasReturnValue(), "应有返回值");
        assertEquals("UIEvent", result.getReturnType(), "返回类型应为UIEvent");
        assertNull(result.getReturnValue(), "返回值应为null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法处理空字符串UI事件ID
     */
    @Test
    @DisplayName("invoke - 空字符串UI事件ID处理")
    void testInvoke_EmptyUIEventId() {
        // Given
        String actionParameterJson = "{\n"
                + "    \"ui_event_id\": \"\",\n"
                + "    \"func_api_name\": \"testFunction\",\n"
                + "    \"func_args\": []\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        
        // When
        ButtonExecutor.Result result = uiEventAction.invoke(testArg, testContext);
        
        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isHasReturnValue(), "应有返回值");
        assertEquals("UIEvent", result.getReturnType(), "返回类型应为UIEvent");
        assertEquals("", result.getReturnValue(), "返回值应为空字符串");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法处理缺少ui_event_id字段的JSON
     */
    @Test
    @DisplayName("invoke - 缺少ui_event_id字段处理")
    void testInvoke_MissingUIEventIdField() {
        // Given
        String actionParameterJson = "{\n"
                + "    \"func_api_name\": \"testFunction\",\n"
                + "    \"func_args\": []\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        
        // When
        ButtonExecutor.Result result = uiEventAction.invoke(testArg, testContext);
        
        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isHasReturnValue(), "应有返回值");
        assertEquals("UIEvent", result.getReturnType(), "返回类型应为UIEvent");
        assertNull(result.getReturnValue(), "返回值应为null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getActionParameter方法的JSON解析功能
     */
    @Test
    @DisplayName("getActionParameter - JSON解析功能")
    void testGetActionParameter_JsonParsing() {
        // Given
        String actionParameterJson = "{\n"
                + "    \"ui_event_id\": \"" + UI_EVENT_ID + "\",\n"
                + "    \"func_api_name\": \"testFunction\",\n"
                + "    \"func_args\": [\n"
                + "        {\n"
                + "            \"name\": \"param1\",\n"
                + "            \"type\": \"text\",\n"
                + "            \"value\": \"value1\"\n"
                + "        }\n"
                + "    ]\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        
        // When
        AbstractFuncAction.ActionParameter result = uiEventAction.getActionParameter(testAction);
        
        // Then
        assertNotNull(result, "结果不应为null");
        assertEquals(UI_EVENT_ID, result.getUiEventId(), "UI事件ID应正确解析");
        assertEquals("testFunction", result.getFunctionAPIName(), "函数API名称应正确解析");
        assertNotNull(result.getFunctionArgList(), "函数参数列表不应为null");
        assertEquals(1, result.getFunctionArgList().size(), "应有1个函数参数");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getActionParameter方法处理无效JSON
     */
    @Test
    @DisplayName("getActionParameter - 无效JSON处理")
    void testGetActionParameter_InvalidJson() {
        // Given
        String invalidJson = "{invalid json format";
        when(testAction.getActionParamter()).thenReturn(invalidJson);
        
        // When & Then
        assertThrows(Exception.class, () -> {
            uiEventAction.getActionParameter(testAction);
        }, "无效JSON应抛出异常");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法处理复杂的JSON参数
     */
    @Test
    @DisplayName("invoke - 复杂JSON参数处理")
    void testInvoke_ComplexJsonParameters() {
        // Given
        String complexActionParameterJson = "{\n"
                + "    \"ui_event_id\": \"" + UI_EVENT_ID + "\",\n"
                + "    \"func_api_name\": \"complexFunction\",\n"
                + "    \"func_args\": [\n"
                + "        {\n"
                + "            \"name\": \"stringParam\",\n"
                + "            \"type\": \"text\",\n"
                + "            \"value\": \"complex string value\"\n"
                + "        },\n"
                + "        {\n"
                + "            \"name\": \"numberParam\",\n"
                + "            \"type\": \"number\",\n"
                + "            \"value\": \"42\"\n"
                + "        },\n"
                + "        {\n"
                + "            \"name\": \"booleanParam\",\n"
                + "            \"type\": \"boolean\",\n"
                + "            \"value\": \"true\"\n"
                + "        }\n"
                + "    ],\n"
                + "    \"extra_field\": \"extra_value\"\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(complexActionParameterJson);
        
        // When
        ButtonExecutor.Result result = uiEventAction.invoke(testArg, testContext);
        
        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isHasReturnValue(), "应有返回值");
        assertEquals("UIEvent", result.getReturnType(), "返回类型应为UIEvent");
        assertEquals(UI_EVENT_ID, result.getReturnValue(), "返回值应为UI事件ID");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试返回值结构的完整性
     */
    @Test
    @DisplayName("invoke - 返回值结构完整性验证")
    void testInvoke_ReturnValueStructureIntegrity() {
        // Given
        String actionParameterJson = "{\n"
                + "    \"ui_event_id\": \"" + UI_EVENT_ID + "\"\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        
        // When
        ButtonExecutor.Result result = uiEventAction.invoke(testArg, testContext);
        
        // Then
        assertNotNull(result, "结果不应为null");
        
        // 验证返回值结构的所有字段
        assertTrue(result.isHasReturnValue(), "hasReturnValue应为true");
        assertEquals("UIEvent", result.getReturnType(), "returnType应为UIEvent");
        assertEquals(UI_EVENT_ID, result.getReturnValue(), "returnValue应为UI事件ID");
        
        // 验证其他字段为默认值
        assertNull(result.getObjectData(), "objectData应为null");
        assertNull(result.getDetails(), "details应为null");
        assertNull(result.getTargetDescribeApiName(), "targetDescribeApiName应为null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法的日志记录
     */
    @Test
    @DisplayName("invoke - 日志记录验证")
    void testInvoke_LoggingVerification() {
        // Given
        String actionParameterJson = "{\n"
                + "    \"ui_event_id\": \"" + UI_EVENT_ID + "\",\n"
                + "    \"func_api_name\": \"testFunction\"\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        
        // When
        ButtonExecutor.Result result = uiEventAction.invoke(testArg, testContext);
        
        // Then
        assertNotNull(result, "结果不应为null");
        assertEquals(UI_EVENT_ID, result.getReturnValue(), "返回值应正确");
        
        // 注意：由于日志是debug级别，在单元测试中通常不会输出
        // 这里主要验证方法执行没有异常
        verify(testAction).getActionParamter();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法处理特殊字符的UI事件ID
     */
    @Test
    @DisplayName("invoke - 特殊字符UI事件ID处理")
    void testInvoke_SpecialCharactersUIEventId() {
        // Given - 使用不包含双引号的特殊字符，避免JSON解析问题
        String specialUIEventId = "event@#$%^&*()_+-=[]{}|;',./<>?";
        String actionParameterJson = "{\n"
                + "    \"ui_event_id\": \"" + specialUIEventId + "\"\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);

        // When
        ButtonExecutor.Result result = uiEventAction.invoke(testArg, testContext);

        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isHasReturnValue(), "应有返回值");
        assertEquals("UIEvent", result.getReturnType(), "返回类型应为UIEvent");
        assertEquals(specialUIEventId, result.getReturnValue(), "返回值应为特殊字符UI事件ID");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法处理长UI事件ID
     */
    @Test
    @DisplayName("invoke - 长UI事件ID处理")
    void testInvoke_LongUIEventId() {
        // Given
        StringBuilder longUIEventId = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longUIEventId.append("a");
        }
        String actionParameterJson = "{\n"
                + "    \"ui_event_id\": \"" + longUIEventId.toString() + "\"\n"
                + "}";
        when(testAction.getActionParamter()).thenReturn(actionParameterJson);
        
        // When
        ButtonExecutor.Result result = uiEventAction.invoke(testArg, testContext);
        
        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isHasReturnValue(), "应有返回值");
        assertEquals("UIEvent", result.getReturnType(), "返回类型应为UIEvent");
        assertEquals(longUIEventId.toString(), result.getReturnValue(), "返回值应为长UI事件ID");
        assertEquals(1000, ((String) result.getReturnValue()).length(), "UI事件ID长度应为1000");
    }
}
