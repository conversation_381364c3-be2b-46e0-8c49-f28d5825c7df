package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ConvertAction单元测试 - JUnit5版本
 * 
 * GenerateByAI
 * 
 * 测试ConvertAction数据转换动作的核心功能：
 * - getType: 返回CONVERT类型
 * - invoke: 主入口方法
 * - startCustomButton: 核心转换逻辑
 * - getMappingRule: 映射规则获取
 * - validateTargetApiName: 目标对象验证
 * - removeMaskFieldValue: 掩码字段处理
 * 
 * 覆盖场景：
 * - 正常数据转换流程
 * - 映射规则不存在的异常处理
 * - 目标对象验证失败的处理
 * - 从对象数据的处理
 * - 掩码字段的移除
 * - 各种数据类型的转换
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("ConvertAction - 数据转换动作测试")
class ConvertActionTest {

    @Mock
    private ObjectMappingService objectMappingService;
    
    @Mock
    private MetaDataFindService metaDataFindService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private MetaDataMiscService metaDataMiscService;

    @Spy
    @InjectMocks
    private ConvertAction convertAction;

    // 测试常量
    private static final String TENANT_ID = "74255";
    private static final String USER_ID = "1000";
    private static final String SOURCE_API_NAME = "SourceObject";
    private static final String TARGET_API_NAME = "TargetObject";
    private static final String RULE_API_NAME = "testMappingRule";
    private static final String BUTTON_API_NAME = "convertButton";

    private User testUser;
    private IObjectData testObjectData;
    private IObjectDescribe sourceDescribe;
    private IObjectDescribe targetDescribe;
    private IObjectDescribe detailDescribe;
    private IUdefButton testButton;
    private IUdefAction testAction;
    private IObjectMappingRuleInfo mappingRule;
    private ActionExecutorContext testContext;
    private ButtonExecutor.Arg testArg;

    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser = new User(TENANT_ID, USER_ID);
        
        // 创建测试对象数据
        testObjectData = new ObjectData();
        testObjectData.set("id", "123");
        testObjectData.set("name", "test source");
        testObjectData.set("amount", 1000);
        
        // 创建Mock对象
        sourceDescribe = mock(IObjectDescribe.class);
        targetDescribe = mock(IObjectDescribe.class);
        detailDescribe = mock(IObjectDescribe.class);
        testButton = mock(IUdefButton.class);
        testAction = mock(IUdefAction.class);
        mappingRule = mock(IObjectMappingRuleInfo.class);
        testContext = mock(ActionExecutorContext.class);
        testArg = mock(ButtonExecutor.Arg.class);
        
        // 配置基础Mock行为
        when(sourceDescribe.getApiName()).thenReturn(SOURCE_API_NAME);
        when(targetDescribe.getApiName()).thenReturn(TARGET_API_NAME);
        when(detailDescribe.getApiName()).thenReturn("DetailObject");
        when(testButton.getApiName()).thenReturn(BUTTON_API_NAME);
        when(mappingRule.getRuleApiName()).thenReturn(RULE_API_NAME);
        when(mappingRule.getTargetApiName()).thenReturn(TARGET_API_NAME);
        
        when(testContext.getUser()).thenReturn(testUser);
        when(testContext.getDescribe()).thenReturn(sourceDescribe);
        when(testContext.getButton()).thenReturn(testButton);
        when(testContext.getAction()).thenReturn(testAction);
        when(testArg.getObjectData()).thenReturn(testObjectData);
        when(testArg.toDetails()).thenReturn(Collections.emptyMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getType方法返回CONVERT类型
     */
    @Test
    @DisplayName("getType - 返回CONVERT类型")
    void testGetType_ReturnsConvertType() {
        // When
        ActionExecutorType result = convertAction.getType();
        
        // Then
        assertNotNull(result, "类型不应为null");
        assertEquals(ActionExecutorType.CONVERT, result, "应返回CONVERT类型");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法的正常执行流程
     */
    @Test
    @DisplayName("invoke - 正常执行流程")
    void testInvoke_NormalFlow() {
        // Given
        String actionParameter = "{\"object_mapping_api_name\":\"" + RULE_API_NAME + "\"}";
        when(testAction.getActionParamter()).thenReturn(actionParameter);
        
        // 配置映射规则
        when(objectMappingService.findByApiName(testUser, RULE_API_NAME))
                .thenReturn(Arrays.asList(mappingRule));
        
        // 配置目标对象描述
        when(describeLogicService.findObject(TENANT_ID, TARGET_API_NAME))
                .thenReturn(targetDescribe);
        
        // 配置从对象描述
        when(describeLogicService.findDetailDescribes(TENANT_ID, SOURCE_API_NAME))
                .thenReturn(Collections.emptyList());
        
        // 配置映射结果
        ObjectMappingService.MappingDataResult mappingResult = ObjectMappingService.MappingDataResult.builder()
                .objectData(createTargetObjectData())
                .details(Collections.emptyMap())
                .build();
        when(objectMappingService.mappingData(eq(testUser), any(ObjectMappingService.MappingDataArg.class)))
                .thenReturn(mappingResult);
        
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt targetDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(targetDescribe)).thenReturn(targetDescribeExt);
            when(targetDescribeExt.isSlaveObjectCreateWithMasterAndHiddenDetailButton()).thenReturn(false);
            
            // When
            ButtonExecutor.Result result = convertAction.invoke(testArg, testContext);
            
            // Then
            assertNotNull(result, "结果不应为null");
            assertNotNull(result.getObjectData(), "转换后的对象数据不应为null");
            assertEquals(TARGET_API_NAME, result.getTargetDescribeApiName(), "目标对象API名称应正确");
            assertNotNull(result.getDetails(), "详情数据不应为null");
            
            // 验证关键方法被调用
            verify(objectMappingService).findByApiName(testUser, RULE_API_NAME);
            verify(describeLogicService).findObject(TENANT_ID, TARGET_API_NAME);
            verify(objectMappingService).mappingData(eq(testUser), any(ObjectMappingService.MappingDataArg.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试映射规则不存在时的异常处理
     */
    @Test
    @DisplayName("startCustomButton - 映射规则不存在异常")
    void testStartCustomButton_MappingRuleNotExist() {
        // Given
        String actionParameter = "{\"object_mapping_api_name\":\"nonExistentRule\"}";
        when(testAction.getActionParamter()).thenReturn(actionParameter);
        
        // 配置映射规则不存在
        when(objectMappingService.findByApiName(testUser, "nonExistentRule"))
                .thenReturn(Collections.emptyList());
        
        // When & Then
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            convertAction.startCustomButton(testObjectData, Collections.emptyMap(), testUser, 
                    testArg, testAction, testButton, sourceDescribe);
        });
        
        assertNotNull(exception.getMessage(), "异常消息不应为null");
        verify(objectMappingService).findByApiName(testUser, "nonExistentRule");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试目标对象验证失败的处理
     */
    @Test
    @DisplayName("validateTargetApiName - 从对象隐藏详情按钮验证失败")
    void testValidateTargetApiName_SlaveObjectValidationFailed() {
        // Given
        String actionParameter = "{\"object_mapping_api_name\":\"" + RULE_API_NAME + "\"}";
        when(testAction.getActionParamter()).thenReturn(actionParameter);
        
        // 配置映射规则
        when(objectMappingService.findByApiName(testUser, RULE_API_NAME))
                .thenReturn(Arrays.asList(mappingRule));
        
        // 配置目标对象描述
        when(describeLogicService.findObject(TENANT_ID, TARGET_API_NAME))
                .thenReturn(targetDescribe);
        
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt targetDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(targetDescribe)).thenReturn(targetDescribeExt);
            // 配置为隐藏详情按钮的从对象
            when(targetDescribeExt.isSlaveObjectCreateWithMasterAndHiddenDetailButton()).thenReturn(true);
            
            // When & Then
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                convertAction.startCustomButton(testObjectData, Collections.emptyMap(), testUser, 
                        testArg, testAction, testButton, sourceDescribe);
            });
            
            assertNotNull(exception.getMessage(), "异常消息不应为null");
            verify(describeLogicService).findObject(TENANT_ID, TARGET_API_NAME);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试从对象数据的处理
     */
    @Test
    @DisplayName("startCustomButton - 从对象数据处理")
    void testStartCustomButton_DetailObjectDataProcessing() {
        // Given
        String actionParameter = "{\"object_mapping_api_name\":\"" + RULE_API_NAME + "\"}";
        when(testAction.getActionParamter()).thenReturn(actionParameter);
        
        // 配置映射规则
        when(objectMappingService.findByApiName(testUser, RULE_API_NAME))
                .thenReturn(Arrays.asList(mappingRule));
        
        // 配置目标对象描述
        when(describeLogicService.findObject(TENANT_ID, TARGET_API_NAME))
                .thenReturn(targetDescribe);
        
        // 配置从对象描述
        when(describeLogicService.findDetailDescribes(TENANT_ID, SOURCE_API_NAME))
                .thenReturn(Arrays.asList(detailDescribe));
        
        // 配置从对象数据查询
        List<IObjectData> detailDataList = Arrays.asList(createDetailObjectData());
        QueryResult<IObjectData> findResult = mock(QueryResult.class);
        when(findResult.getData()).thenReturn(detailDataList);
        when(metaDataFindService.findBySearchQuery(eq(testUser), eq("DetailObject"), any(SearchTemplateQuery.class)))
                .thenReturn(findResult);

        // 配置映射结果
        Map<String, List<IObjectData>> detailsMap = new HashMap<>();
        detailsMap.put("DetailObject", detailDataList);
        ObjectMappingService.MappingDataResult mappingResult = ObjectMappingService.MappingDataResult.builder()
                .objectData(createTargetObjectData())
                .details(detailsMap)
                .build();
        when(objectMappingService.mappingData(eq(testUser), any(ObjectMappingService.MappingDataArg.class)))
                .thenReturn(mappingResult);
        
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt targetDescribeExt = mock(ObjectDescribeExt.class);
            ObjectDescribeExt detailDescribeExt = mock(ObjectDescribeExt.class);
            
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(targetDescribe)).thenReturn(targetDescribeExt);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(detailDescribe)).thenReturn(detailDescribeExt);
            
            when(targetDescribeExt.isSlaveObjectCreateWithMasterAndHiddenDetailButton()).thenReturn(false);
            
            // 配置主从关系字段
            MasterDetailFieldDescribe masterDetailField = mock(MasterDetailFieldDescribe.class);
            when(masterDetailField.getIsCreateWhenMasterCreate()).thenReturn(Boolean.TRUE);
            when(detailDescribeExt.getMasterDetailFieldDescribe()).thenReturn(Optional.of(masterDetailField));
            
            when(metaDataFindService.buildDetailSearchTemplateQuery(eq(testUser), eq(detailDescribeExt), eq(testObjectData)))
                    .thenReturn(mock(SearchTemplateQuery.class));
            
            // When
            ButtonExecutor.Result result = convertAction.startCustomButton(testObjectData, Collections.emptyMap(), 
                    testUser, testArg, testAction, testButton, sourceDescribe);
            
            // Then
            assertNotNull(result, "结果不应为null");
            assertNotNull(result.getDetails(), "详情数据不应为null");
            assertTrue(result.getDetails().containsKey("DetailObject"), "应包含详情对象数据");
            
            // 验证从对象数据查询被调用
            verify(metaDataFindService).findBySearchQuery(eq(testUser), eq("DetailObject"), any(SearchTemplateQuery.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：参数化测试不同的映射规则API名称（通过startCustomButton间接测试）
     */
    @ParameterizedTest
    @ValueSource(strings = {"rule1", "rule2", "rule3", "complexRule_123", "rule_with_underscores"})
    @DisplayName("startCustomButton - 不同映射规则API名称")
    void testStartCustomButton_DifferentRuleApiNames(String ruleApiName) {
        // Given
        String actionParameter = "{\"object_mapping_api_name\":\"" + ruleApiName + "\"}";
        when(testAction.getActionParamter()).thenReturn(actionParameter);

        IObjectMappingRuleInfo expectedRule = mock(IObjectMappingRuleInfo.class);
        when(expectedRule.getRuleApiName()).thenReturn(ruleApiName);
        when(expectedRule.getTargetApiName()).thenReturn(TARGET_API_NAME);

        when(objectMappingService.findByApiName(testUser, ruleApiName))
                .thenReturn(Arrays.asList(expectedRule));

        // 配置目标对象描述
        when(describeLogicService.findObject(TENANT_ID, TARGET_API_NAME))
                .thenReturn(targetDescribe);

        // 配置从对象描述
        when(describeLogicService.findDetailDescribes(TENANT_ID, SOURCE_API_NAME))
                .thenReturn(Collections.emptyList());

        // 配置映射结果
        ObjectMappingService.MappingDataResult mappingResult = ObjectMappingService.MappingDataResult.builder()
                .objectData(createTargetObjectData())
                .details(Collections.emptyMap())
                .build();
        when(objectMappingService.mappingData(eq(testUser), any(ObjectMappingService.MappingDataArg.class)))
                .thenReturn(mappingResult);

        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
             MockedStatic<ObjectMappingExt> mockedObjectMappingExt = mockStatic(ObjectMappingExt.class)) {

            ObjectDescribeExt targetDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(targetDescribe)).thenReturn(targetDescribeExt);
            when(targetDescribeExt.isSlaveObjectCreateWithMasterAndHiddenDetailButton()).thenReturn(false);

            mockedObjectMappingExt.when(() -> ObjectMappingExt.of(Arrays.asList(expectedRule)))
                    .thenReturn(mock(ObjectMappingExt.class));

            // When
            ButtonExecutor.Result result = convertAction.startCustomButton(testObjectData, Collections.emptyMap(),
                    testUser, testArg, testAction, testButton, sourceDescribe);

            // Then
            assertNotNull(result, "结果不应为null");
            verify(objectMappingService).findByApiName(testUser, ruleApiName);
        }
    }



    /**
     * GenerateByAI
     * 测试内容描述：测试掩码字段的移除处理
     */
    @Test
    @DisplayName("removeMaskFieldValue - 掩码字段移除")
    void testRemoveMaskFieldValue_MaskFieldRemoval() {
        // Given
        String actionParameter = "{\"object_mapping_api_name\":\"" + RULE_API_NAME + "\"}";
        when(testAction.getActionParamter()).thenReturn(actionParameter);

        // 配置映射规则
        when(objectMappingService.findByApiName(testUser, RULE_API_NAME))
                .thenReturn(Arrays.asList(mappingRule));

        // 配置目标对象描述
        when(describeLogicService.findObject(TENANT_ID, TARGET_API_NAME))
                .thenReturn(targetDescribe);

        // 配置从对象描述
        when(describeLogicService.findDetailDescribes(TENANT_ID, SOURCE_API_NAME))
                .thenReturn(Arrays.asList(detailDescribe));

        // 配置映射结果
        ObjectMappingService.MappingDataResult mappingResult = ObjectMappingService.MappingDataResult.builder()
                .objectData(createTargetObjectData())
                .details(Collections.emptyMap())
                .build();
        when(objectMappingService.mappingData(eq(testUser), any(ObjectMappingService.MappingDataArg.class)))
                .thenReturn(mappingResult);

        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt targetDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(targetDescribe)).thenReturn(targetDescribeExt);
            when(targetDescribeExt.isSlaveObjectCreateWithMasterAndHiddenDetailButton()).thenReturn(false);

            // When
            ButtonExecutor.Result result = convertAction.startCustomButton(testObjectData, Collections.emptyMap(),
                    testUser, testArg, testAction, testButton, sourceDescribe);

            // Then
            assertNotNull(result, "结果不应为null");

            // 验证掩码字段移除方法被调用
            verify(metaDataMiscService).removeMaskFieldValue(eq(testUser), eq(testObjectData),
                    any(Map.class), any(Map.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试JSON参数解析的异常处理（通过startCustomButton间接测试）
     */
    @Test
    @DisplayName("startCustomButton - JSON解析异常处理")
    void testStartCustomButton_JsonParsingException() {
        // Given - 无效的JSON格式
        String invalidActionParameter = "{invalid json format";
        when(testAction.getActionParamter()).thenReturn(invalidActionParameter);

        // When & Then
        assertThrows(Exception.class, () -> {
            convertAction.startCustomButton(testObjectData, Collections.emptyMap(), testUser,
                    testArg, testAction, testButton, sourceDescribe);
        }, "无效JSON应抛出异常");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试复杂的数据转换场景
     */
    @Test
    @DisplayName("startCustomButton - 复杂数据转换场景")
    void testStartCustomButton_ComplexDataConversion() {
        // Given
        String actionParameter = "{\"object_mapping_api_name\":\"" + RULE_API_NAME + "\"}";
        when(testAction.getActionParamter()).thenReturn(actionParameter);

        // 创建复杂的源对象数据
        IObjectData complexSourceData = new ObjectData();
        complexSourceData.set("id", "complex123");
        complexSourceData.set("name", "复杂源对象");
        complexSourceData.set("amount", 5000.50);
        complexSourceData.set("date", "2023-12-01");
        complexSourceData.set("status", "active");
        complexSourceData.set("tags", Arrays.asList("tag1", "tag2", "tag3"));

        Map<String, Object> nestedData = new HashMap<>();
        nestedData.put("nestedField", "nestedValue");
        nestedData.put("nestedNumber", 42);
        complexSourceData.set("nested", nestedData);

        // 配置映射规则
        when(objectMappingService.findByApiName(testUser, RULE_API_NAME))
                .thenReturn(Arrays.asList(mappingRule));

        // 配置目标对象描述
        when(describeLogicService.findObject(TENANT_ID, TARGET_API_NAME))
                .thenReturn(targetDescribe);

        // 配置从对象描述
        when(describeLogicService.findDetailDescribes(TENANT_ID, SOURCE_API_NAME))
                .thenReturn(Collections.emptyList());

        // 创建复杂的转换结果
        IObjectData complexTargetData = new ObjectData();
        complexTargetData.set("id", "convertedComplex123");
        complexTargetData.set("convertedName", "转换后的复杂对象");
        complexTargetData.set("convertedAmount", 5000.50);
        complexTargetData.set("convertedDate", "2023-12-01T00:00:00Z");
        complexTargetData.set("convertedStatus", "ACTIVE");

        ObjectMappingService.MappingDataResult mappingResult = ObjectMappingService.MappingDataResult.builder()
                .objectData(complexTargetData)
                .details(Collections.emptyMap())
                .build();
        when(objectMappingService.mappingData(eq(testUser), any(ObjectMappingService.MappingDataArg.class)))
                .thenReturn(mappingResult);

        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt targetDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(targetDescribe)).thenReturn(targetDescribeExt);
            when(targetDescribeExt.isSlaveObjectCreateWithMasterAndHiddenDetailButton()).thenReturn(false);

            // When
            ButtonExecutor.Result result = convertAction.startCustomButton(complexSourceData, Collections.emptyMap(),
                    testUser, testArg, testAction, testButton, sourceDescribe);

            // Then
            assertNotNull(result, "结果不应为null");
            assertNotNull(result.getObjectData(), "转换后的对象数据不应为null");
            assertEquals("convertedComplex123", result.getObjectData().get("id"), "转换后的ID应正确");
            assertEquals("转换后的复杂对象", result.getObjectData().get("convertedName"), "转换后的名称应正确");
            assertEquals(5000.50, result.getObjectData().get("convertedAmount"), "转换后的金额应正确");
            assertEquals(TARGET_API_NAME, result.getTargetDescribeApiName(), "目标对象API名称应正确");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多个从对象的数据处理
     */
    @Test
    @DisplayName("startCustomButton - 多个从对象数据处理")
    void testStartCustomButton_MultipleDetailObjects() {
        // Given
        String actionParameter = "{\"object_mapping_api_name\":\"" + RULE_API_NAME + "\"}";
        when(testAction.getActionParamter()).thenReturn(actionParameter);

        // 配置映射规则
        when(objectMappingService.findByApiName(testUser, RULE_API_NAME))
                .thenReturn(Arrays.asList(mappingRule));

        // 配置目标对象描述
        when(describeLogicService.findObject(TENANT_ID, TARGET_API_NAME))
                .thenReturn(targetDescribe);

        // 配置多个从对象描述
        IObjectDescribe detailDescribe1 = mock(IObjectDescribe.class);
        IObjectDescribe detailDescribe2 = mock(IObjectDescribe.class);
        when(detailDescribe1.getApiName()).thenReturn("DetailObject1");
        when(detailDescribe2.getApiName()).thenReturn("DetailObject2");

        when(describeLogicService.findDetailDescribes(TENANT_ID, SOURCE_API_NAME))
                .thenReturn(Arrays.asList(detailDescribe1, detailDescribe2));

        // 配置从对象数据查询
        List<IObjectData> detailDataList1 = Arrays.asList(createDetailObjectData("detail1"));
        List<IObjectData> detailDataList2 = Arrays.asList(createDetailObjectData("detail2"));

        QueryResult<IObjectData> findResult1 = mock(QueryResult.class);
        QueryResult<IObjectData> findResult2 = mock(QueryResult.class);
        when(findResult1.getData()).thenReturn(detailDataList1);
        when(findResult2.getData()).thenReturn(detailDataList2);

        when(metaDataFindService.findBySearchQuery(eq(testUser), eq("DetailObject1"), any(SearchTemplateQuery.class)))
                .thenReturn(findResult1);
        when(metaDataFindService.findBySearchQuery(eq(testUser), eq("DetailObject2"), any(SearchTemplateQuery.class)))
                .thenReturn(findResult2);

        // 配置映射结果
        Map<String, List<IObjectData>> convertedDetails = new HashMap<>();
        convertedDetails.put("DetailObject1", detailDataList1);
        convertedDetails.put("DetailObject2", detailDataList2);

        ObjectMappingService.MappingDataResult mappingResult = ObjectMappingService.MappingDataResult.builder()
                .objectData(createTargetObjectData())
                .details(convertedDetails)
                .build();
        when(objectMappingService.mappingData(eq(testUser), any(ObjectMappingService.MappingDataArg.class)))
                .thenReturn(mappingResult);

        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt targetDescribeExt = mock(ObjectDescribeExt.class);
            ObjectDescribeExt detailDescribeExt1 = mock(ObjectDescribeExt.class);
            ObjectDescribeExt detailDescribeExt2 = mock(ObjectDescribeExt.class);

            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(targetDescribe)).thenReturn(targetDescribeExt);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(detailDescribe1)).thenReturn(detailDescribeExt1);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(detailDescribe2)).thenReturn(detailDescribeExt2);

            when(targetDescribeExt.isSlaveObjectCreateWithMasterAndHiddenDetailButton()).thenReturn(false);

            // 配置主从关系字段
            MasterDetailFieldDescribe masterDetailField1 = mock(MasterDetailFieldDescribe.class);
            MasterDetailFieldDescribe masterDetailField2 = mock(MasterDetailFieldDescribe.class);
            when(masterDetailField1.getIsCreateWhenMasterCreate()).thenReturn(Boolean.TRUE);
            when(masterDetailField2.getIsCreateWhenMasterCreate()).thenReturn(Boolean.TRUE);
            when(detailDescribeExt1.getMasterDetailFieldDescribe()).thenReturn(Optional.of(masterDetailField1));
            when(detailDescribeExt2.getMasterDetailFieldDescribe()).thenReturn(Optional.of(masterDetailField2));

            when(metaDataFindService.buildDetailSearchTemplateQuery(eq(testUser), eq(detailDescribeExt1), eq(testObjectData)))
                    .thenReturn(mock(SearchTemplateQuery.class));
            when(metaDataFindService.buildDetailSearchTemplateQuery(eq(testUser), eq(detailDescribeExt2), eq(testObjectData)))
                    .thenReturn(mock(SearchTemplateQuery.class));

            // When
            ButtonExecutor.Result result = convertAction.startCustomButton(testObjectData, Collections.emptyMap(),
                    testUser, testArg, testAction, testButton, sourceDescribe);

            // Then
            assertNotNull(result, "结果不应为null");
            assertNotNull(result.getDetails(), "详情数据不应为null");
            assertEquals(2, result.getDetails().size(), "应包含2个详情对象数据");
            assertTrue(result.getDetails().containsKey("DetailObject1"), "应包含DetailObject1数据");
            assertTrue(result.getDetails().containsKey("DetailObject2"), "应包含DetailObject2数据");

            // 验证多个从对象数据查询被调用
            verify(metaDataFindService).findBySearchQuery(eq(testUser), eq("DetailObject1"), any(SearchTemplateQuery.class));
            verify(metaDataFindService).findBySearchQuery(eq(testUser), eq("DetailObject2"), any(SearchTemplateQuery.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试映射数据参数的构建
     */
    @Test
    @DisplayName("startCustomButton - 映射数据参数构建验证")
    void testStartCustomButton_MappingDataArgConstruction() {
        // Given
        String actionParameter = "{\"object_mapping_api_name\":\"" + RULE_API_NAME + "\"}";
        when(testAction.getActionParamter()).thenReturn(actionParameter);

        // 配置映射规则
        when(objectMappingService.findByApiName(testUser, RULE_API_NAME))
                .thenReturn(Arrays.asList(mappingRule));

        // 配置目标对象描述
        when(describeLogicService.findObject(TENANT_ID, TARGET_API_NAME))
                .thenReturn(targetDescribe);

        // 配置从对象描述
        when(describeLogicService.findDetailDescribes(TENANT_ID, SOURCE_API_NAME))
                .thenReturn(Collections.emptyList());

        // 配置映射结果
        ObjectMappingService.MappingDataResult mappingResult = ObjectMappingService.MappingDataResult.builder()
                .objectData(createTargetObjectData())
                .details(Collections.emptyMap())
                .build();
        when(objectMappingService.mappingData(eq(testUser), any(ObjectMappingService.MappingDataArg.class)))
                .thenReturn(mappingResult);

        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            ObjectDescribeExt targetDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(targetDescribe)).thenReturn(targetDescribeExt);
            when(targetDescribeExt.isSlaveObjectCreateWithMasterAndHiddenDetailButton()).thenReturn(false);

            // When
            convertAction.startCustomButton(testObjectData, Collections.emptyMap(),
                    testUser, testArg, testAction, testButton, sourceDescribe);

            // Then
            // 验证映射数据参数的构建
            verify(objectMappingService).mappingData(eq(testUser), argThat(arg -> {
                return RULE_API_NAME.equals(arg.getRuleApiName()) &&
                       testObjectData.equals(arg.getObjectData()) &&
                       arg.getDetails() != null;
            }));
        }
    }

    /**
     * 创建目标对象数据的辅助方法
     */
    private IObjectData createTargetObjectData() {
        IObjectData targetData = new ObjectData();
        targetData.set("id", "target123");
        targetData.set("name", "converted target");
        targetData.set("convertedAmount", 1000);
        return targetData;
    }

    /**
     * 创建详情对象数据的辅助方法
     */
    private IObjectData createDetailObjectData() {
        IObjectData detailData = new ObjectData();
        detailData.set("id", "detail123");
        detailData.set("masterId", "123");
        detailData.set("detailName", "test detail");
        return detailData;
    }

    /**
     * 创建带ID的详情对象数据的辅助方法
     */
    private IObjectData createDetailObjectData(String id) {
        IObjectData detailData = new ObjectData();
        detailData.set("id", id);
        detailData.set("masterId", "123");
        detailData.set("detailName", "test detail " + id);
        return detailData;
    }
}
