package com.facishare.paas.appframework.button.dto;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CustomBizButtonExecutor DTO单元测试 - JUnit5版本
 * 
 * GenerateByAI
 * 
 * 测试CustomBizButtonExecutor接口下的内部类：
 * - Arg: 自定义业务按钮执行参数类
 * - Result: 自定义业务按钮执行结果类
 * 
 * 覆盖场景：
 * - 对象构造和字段设置
 * - Builder模式的使用
 * - 静态工厂方法
 * - 数据转换方法（Map与IObjectData互转）
 * - JSON序列化和反序列化
 * - 边界条件和null值处理
 */
@DisplayName("CustomBizButtonExecutor - 自定义业务按钮DTO测试")
class CustomBizButtonExecutorTest {

    private Gson gson;
    private IObjectData testObjectData;
    private Map<String, Object> testArgs;
    private Map<String, List<IObjectData>> testDetails;
    private Map<String, Object> testObjectDataMap;
    private Map<String, List<Map<String, Object>>> testDetailsMap;

    @BeforeEach
    void setUp() {
        gson = new GsonBuilder().create();
        
        // 创建测试数据
        testObjectData = new ObjectData();
        testObjectData.set("id", "123");
        testObjectData.set("name", "test object");
        testObjectData.set("amount", 1000);
        
        testArgs = new HashMap<>();
        testArgs.put("param1", "value1");
        testArgs.put("param2", 42);
        
        testDetails = new HashMap<>();
        List<IObjectData> detailList = Arrays.asList(testObjectData);
        testDetails.put("DetailObject", detailList);
        
        // 创建Map格式的测试数据
        testObjectDataMap = new HashMap<>();
        testObjectDataMap.put("id", "123");
        testObjectDataMap.put("name", "test object");
        testObjectDataMap.put("amount", 1000);
        
        testDetailsMap = new HashMap<>();
        List<Map<String, Object>> detailMapList = Arrays.asList(testObjectDataMap);
        testDetailsMap.put("DetailObject", detailMapList);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Arg类的Builder模式构造
     */
    @Test
    @DisplayName("Arg - Builder模式构造测试")
    void testArgBuilder() {
        // Given & When
        CustomBizButtonExecutor.Arg arg = CustomBizButtonExecutor.Arg.builder()
                .describeApiName("TestObject")
                .objectDataId("123")
                .dataIds(Arrays.asList("id1", "id2"))
                .buttonApiName("testButton")
                .args(testArgs)
                .objectData(testObjectDataMap)
                .details(testDetailsMap)
                .bizKey("testBizKey")
                .actionStage("before")
                .build();

        // Then
        assertNotNull(arg, "Arg对象不应为null");
        assertEquals("TestObject", arg.getDescribeApiName(), "描述API名称应正确");
        assertEquals("123", arg.getObjectDataId(), "对象数据ID应正确");
        assertEquals(Arrays.asList("id1", "id2"), arg.getDataIds(), "数据ID列表应正确");
        assertEquals("testButton", arg.getButtonApiName(), "按钮API名称应正确");
        assertEquals(testArgs, arg.getArgs(), "参数应正确");
        assertEquals(testObjectDataMap, arg.getObjectData(), "对象数据应正确");
        assertEquals(testDetailsMap, arg.getDetails(), "详情数据应正确");
        assertEquals("testBizKey", arg.getBizKey(), "业务键应正确");
        assertEquals("before", arg.getActionStage(), "动作阶段应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Arg类的静态工厂方法of
     */
    @Test
    @DisplayName("Arg - 静态工厂方法of测试")
    void testArgOfMethod() {
        // Given
        List<String> dataIds = Arrays.asList("id1", "id2", "id3");
        Map<String, Object> actionParams = new HashMap<>();
        actionParams.put("action", "custom");

        // When
        CustomBizButtonExecutor.Arg arg = CustomBizButtonExecutor.Arg.of(
                "TestObject",
                "123",
                dataIds,
                "testButton",
                testArgs,
                testObjectData,
                testDetails,
                "testBizKey",
                "before",
                actionParams
        );

        // Then
        assertNotNull(arg, "Arg对象不应为null");
        assertEquals("TestObject", arg.getDescribeApiName(), "描述API名称应正确");
        assertEquals("123", arg.getObjectDataId(), "对象数据ID应正确");
        assertEquals(dataIds, arg.getDataIds(), "数据ID列表应正确");
        assertEquals("testButton", arg.getButtonApiName(), "按钮API名称应正确");
        assertEquals(testArgs, arg.getArgs(), "参数应正确");
        assertNotNull(arg.getObjectData(), "对象数据不应为null");
        assertNotNull(arg.getDetails(), "详情数据不应为null");
        assertEquals("testBizKey", arg.getBizKey(), "业务键应正确");
        assertEquals("before", arg.getActionStage(), "动作阶段应正确");
        assertEquals(actionParams, arg.getActionParams(), "动作参数应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Arg类处理null值
     */
    @Test
    @DisplayName("Arg - null值处理测试")
    void testArgWithNullValues() {
        // When
        CustomBizButtonExecutor.Arg arg = CustomBizButtonExecutor.Arg.of(
                "TestObject",
                null,
                null,
                "testButton",
                null,
                null,
                null,
                null,
                null,
                null
        );

        // Then
        assertNotNull(arg, "Arg对象不应为null");
        assertEquals("TestObject", arg.getDescribeApiName(), "描述API名称应正确");
        assertNull(arg.getObjectDataId(), "对象数据ID应为null");
        assertNull(arg.getDataIds(), "数据ID列表应为null");
        assertEquals("testButton", arg.getButtonApiName(), "按钮API名称应正确");
        assertNull(arg.getArgs(), "参数应为null");
        assertNull(arg.getObjectData(), "对象数据应为null");
        assertNull(arg.getDetails(), "详情数据应为null");
        assertNull(arg.getBizKey(), "业务键应为null");
        assertNull(arg.getActionStage(), "动作阶段应为null");
        assertNull(arg.getActionParams(), "动作参数应为null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Result类的Builder模式构造
     */
    @Test
    @DisplayName("Result - Builder模式构造测试")
    void testResultBuilder() {
        // Given & When
        CustomBizButtonExecutor.Result result = CustomBizButtonExecutor.Result.builder()
                .objectData(testObjectDataMap)
                .details(testDetailsMap)
                .targetDescribeApiName("TargetObject")
                .hasReturnValue(true)
                .returnValue("test result")
                .returnType("String")
                .block(false)
                .build();

        // Then
        assertNotNull(result, "Result对象不应为null");
        assertEquals(testObjectDataMap, result.getObjectData(), "对象数据应正确");
        assertEquals(testDetailsMap, result.getDetails(), "详情数据应正确");
        assertEquals("TargetObject", result.getTargetDescribeApiName(), "目标描述API名称应正确");
        assertTrue(result.isHasReturnValue(), "有返回值标志应为true");
        assertEquals("test result", result.getReturnValue(), "返回值应正确");
        assertEquals("String", result.getReturnType(), "返回类型应正确");
        assertFalse(result.isBlock(), "阻断标志应为false");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Result类的默认值
     */
    @Test
    @DisplayName("Result - 默认值测试")
    void testResultDefaults() {
        // When
        CustomBizButtonExecutor.Result result = CustomBizButtonExecutor.Result.builder().build();

        // Then
        assertNotNull(result, "Result对象不应为null");
        assertTrue(result.isBlock(), "默认应为阻断");
        assertFalse(result.isHasReturnValue(), "默认无返回值");
        assertNull(result.getObjectData(), "对象数据应为null");
        assertNull(result.getDetails(), "详情数据应为null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Result类的toObjectData方法
     */
    @Test
    @DisplayName("Result - toObjectData方法测试")
    void testResultToObjectData() {
        // Given
        CustomBizButtonExecutor.Result result = CustomBizButtonExecutor.Result.builder()
                .objectData(testObjectDataMap)
                .build();

        // When
        IObjectData objectData = result.toObjectData();

        // Then
        assertNotNull(objectData, "转换后的对象数据不应为null");
        assertEquals("123", objectData.get("id"), "ID字段应正确");
        assertEquals("test object", objectData.get("name"), "名称字段应正确");
        assertEquals(1000, objectData.get("amount"), "金额字段应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Result类的toObjectData方法处理null值
     */
    @Test
    @DisplayName("Result - toObjectData方法null值处理测试")
    void testResultToObjectDataWithNull() {
        // Given
        CustomBizButtonExecutor.Result result = CustomBizButtonExecutor.Result.builder()
                .objectData(null)
                .build();

        // When
        IObjectData objectData = result.toObjectData();

        // Then
        assertNull(objectData, "转换后的对象数据应为null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Result类的toDetails方法
     */
    @Test
    @DisplayName("Result - toDetails方法测试")
    void testResultToDetails() {
        // Given
        CustomBizButtonExecutor.Result result = CustomBizButtonExecutor.Result.builder()
                .details(testDetailsMap)
                .build();

        // When
        Map<String, List<IObjectData>> details = result.toDetails();

        // Then
        assertNotNull(details, "转换后的详情数据不应为null");
        assertTrue(details.containsKey("DetailObject"), "应包含DetailObject键");
        List<IObjectData> detailList = details.get("DetailObject");
        assertNotNull(detailList, "详情列表不应为null");
        assertEquals(1, detailList.size(), "详情列表大小应为1");
        
        IObjectData detailData = detailList.get(0);
        assertEquals("123", detailData.get("id"), "详情数据ID应正确");
        assertEquals("test object", detailData.get("name"), "详情数据名称应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Result类的toDetails方法处理null值
     */
    @Test
    @DisplayName("Result - toDetails方法null值处理测试")
    void testResultToDetailsWithNull() {
        // Given
        CustomBizButtonExecutor.Result result = CustomBizButtonExecutor.Result.builder()
                .details(null)
                .build();

        // When
        Map<String, List<IObjectData>> details = result.toDetails();

        // Then
        assertNotNull(details, "转换后的详情数据不应为null");
        assertTrue(details.isEmpty(), "应返回空Map");
    }

    /**
     * GenerateByAI
     * 测试内容描述：参数化测试不同的返回类型
     */
    @ParameterizedTest
    @ValueSource(strings = {"String", "Integer", "Boolean", "Object", "UIAction", "CustomResult"})
    @DisplayName("Result - 不同返回类型测试")
    void testResultDifferentReturnTypes(String returnType) {
        // When
        CustomBizButtonExecutor.Result result = CustomBizButtonExecutor.Result.builder()
                .hasReturnValue(true)
                .returnType(returnType)
                .returnValue("test value")
                .build();

        // Then
        assertNotNull(result, "Result对象不应为null");
        assertTrue(result.isHasReturnValue(), "应有返回值");
        assertEquals(returnType, result.getReturnType(), "返回类型应正确");
        assertEquals("test value", result.getReturnValue(), "返回值应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试JSON序列化和反序列化
     */
    @Test
    @DisplayName("JSON序列化反序列化测试")
    void testJsonSerialization() {
        // Given
        CustomBizButtonExecutor.Arg originalArg = CustomBizButtonExecutor.Arg.builder()
                .describeApiName("TestObject")
                .objectDataId("123")
                .buttonApiName("testButton")
                .bizKey("testBizKey")
                .actionStage("before")
                .build();

        // When - 序列化
        String json = gson.toJson(originalArg);
        assertNotNull(json, "JSON字符串不应为null");
        assertTrue(json.contains("TestObject"), "JSON应包含描述API名称");
        assertTrue(json.contains("testButton"), "JSON应包含按钮API名称");

        // Then - 反序列化
        CustomBizButtonExecutor.Arg deserializedArg = gson.fromJson(json, CustomBizButtonExecutor.Arg.class);
        assertNotNull(deserializedArg, "反序列化对象不应为null");
        assertEquals(originalArg.getDescribeApiName(), deserializedArg.getDescribeApiName(), 
                "描述API名称应一致");
        assertEquals(originalArg.getButtonApiName(), deserializedArg.getButtonApiName(), 
                "按钮API名称应一致");
        assertEquals(originalArg.getBizKey(), deserializedArg.getBizKey(), "业务键应一致");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试复杂数据结构的处理
     */
    @Test
    @DisplayName("复杂数据结构处理测试")
    void testComplexDataStructure() {
        // Given
        Map<String, Object> complexArgs = new HashMap<>();
        complexArgs.put("stringParam", "test");
        complexArgs.put("intParam", 42);
        complexArgs.put("boolParam", true);
        complexArgs.put("listParam", Arrays.asList("item1", "item2"));
        Map<String, String> mapParam = new HashMap<>();
        mapParam.put("key1", "value1");
        mapParam.put("key2", "value2");
        complexArgs.put("mapParam", mapParam);

        // When
        CustomBizButtonExecutor.Arg arg = CustomBizButtonExecutor.Arg.builder()
                .args(complexArgs)
                .build();

        // Then
        assertNotNull(arg, "Arg对象不应为null");
        assertEquals(complexArgs, arg.getArgs(), "复杂参数应正确");
        
        Map<String, Object> retrievedArgs = arg.getArgs();
        assertEquals("test", retrievedArgs.get("stringParam"), "字符串参数应正确");
        assertEquals(42, retrievedArgs.get("intParam"), "整数参数应正确");
        assertEquals(true, retrievedArgs.get("boolParam"), "布尔参数应正确");
        assertNotNull(retrievedArgs.get("listParam"), "列表参数不应为null");
        assertNotNull(retrievedArgs.get("mapParam"), "Map参数不应为null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试数据一致性验证
     */
    @Test
    @DisplayName("数据一致性验证测试")
    void testDataConsistency() {
        // Given
        CustomBizButtonExecutor.Result result = CustomBizButtonExecutor.Result.builder()
                .objectData(testObjectDataMap)
                .details(testDetailsMap)
                .build();

        // When
        IObjectData convertedObjectData = result.toObjectData();
        Map<String, List<IObjectData>> convertedDetails = result.toDetails();

        // Then
        // 验证对象数据一致性
        assertEquals(testObjectDataMap.get("id"), convertedObjectData.get("id"), "ID应一致");
        assertEquals(testObjectDataMap.get("name"), convertedObjectData.get("name"), "名称应一致");
        
        // 验证详情数据一致性
        assertEquals(testDetailsMap.size(), convertedDetails.size(), "详情数据大小应一致");
        assertTrue(convertedDetails.containsKey("DetailObject"), "应包含相同的键");
    }
}
