package com.facishare.paas.appframework.button.dto;

import com.facishare.paas.appframework.metadata.dto.RuleResult;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.*;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ButtonExecutor DTO单元测试 - JUnit5版本
 * 
 * GenerateByAI
 * 
 * 测试ButtonExecutor接口下的内部类：
 * - Arg: 按钮执行参数类
 * - Result: 按钮执行结果类
 * - ExtendInfo: 扩展信息类
 * - DetailDataMergeResult: 详情数据合并结果类
 * 
 * 覆盖场景：
 * - 对象构造和字段设置
 * - Builder模式的使用
 * - 静态工厂方法
 * - JSON序列化和反序列化
 * - 数据转换和处理方法
 * - 边界条件和null值处理
 */
@DisplayName("ButtonExecutor - DTO数据传输对象测试")
class ButtonExecutorTest {

    private Gson gson;
    private IObjectData testObjectData;
    private Map<String, Object> testArgs;
    private Map<String, List<IObjectData>> testDetails;

    @BeforeEach
    void setUp() {
        gson = new GsonBuilder().create();
        
        // 创建测试数据
        testObjectData = new ObjectData();
        testObjectData.set("id", "123");
        testObjectData.set("name", "test object");
        
        testArgs = new HashMap<>();
        testArgs.put("param1", "value1");
        testArgs.put("param2", 42);
        
        testDetails = new HashMap<>();
        List<IObjectData> detailList = Arrays.asList(testObjectData);
        testDetails.put("DetailObject", detailList);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Arg类的Builder模式构造
     */
    @Test
    @DisplayName("Arg - Builder模式构造测试")
    void testArgBuilder() {
        // Given & When
        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder()
                .describeApiName("TestObject")
                .objectDataId("123")
                .buttonApiName("testButton")
                .args(testArgs)
                .objectData(testObjectData)
                .details(testDetails)
                .bizKey("testBizKey")
                .actionStage("before")
                .skipValidationFunction(true)
                .triggerBatchFunc(false)
                .build();

        // Then
        assertNotNull(arg, "Arg对象不应为null");
        assertEquals("TestObject", arg.getDescribeApiName(), "描述API名称应正确");
        assertEquals("123", arg.getObjectDataId(), "对象数据ID应正确");
        assertEquals("testButton", arg.getButtonApiName(), "按钮API名称应正确");
        assertEquals(testArgs, arg.getArgs(), "参数应正确");
        assertEquals(testObjectData, arg.getObjectData(), "对象数据应正确");
        assertEquals(testDetails, arg.getDetails(), "详情数据应正确");
        assertEquals("testBizKey", arg.getBizKey(), "业务键应正确");
        assertEquals("before", arg.getActionStage(), "动作阶段应正确");
        assertTrue(arg.isSkipValidationFunction(), "跳过验证函数标志应为true");
        assertFalse(arg.isTriggerBatchFunc(), "触发批量函数标志应为false");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Arg类的静态工厂方法of
     */
    @Test
    @DisplayName("Arg - 静态工厂方法of测试")
    void testArgOfMethod() {
        // When
        ButtonExecutor.Arg arg = ButtonExecutor.Arg.of(testObjectData, testDetails, testArgs);

        // Then
        assertNotNull(arg, "Arg对象不应为null");
        assertEquals(testObjectData, arg.getObjectData(), "对象数据应正确");
        assertEquals(testDetails, arg.getDetails(), "详情数据应正确");
        assertEquals(testArgs, arg.getArgs(), "参数应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Arg类的ofTriggerFunction方法
     */
    @Test
    @DisplayName("Arg - ofTriggerFunction方法测试")
    void testArgOfTriggerFunction() {
        // Given
        Map<String, Object> actionParams = new HashMap<>();
        actionParams.put("action", "trigger");

        // When
        ButtonExecutor.Arg arg = ButtonExecutor.Arg.ofTriggerFunction(
                testObjectData, testArgs, actionParams);

        // Then
        assertNotNull(arg, "Arg对象不应为null");
        assertEquals(testObjectData, arg.getObjectData(), "对象数据应正确");
        assertEquals(testArgs, arg.getArgs(), "参数应正确");
        assertEquals(actionParams, arg.getActionParams(), "动作参数应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Arg类的ofTriggerFunction重载方法
     */
    @Test
    @DisplayName("Arg - ofTriggerFunction重载方法测试")
    void testArgOfTriggerFunctionWithDetails() {
        // Given
        Map<String, Object> actionParams = new HashMap<>();
        actionParams.put("action", "trigger");

        // When
        ButtonExecutor.Arg arg = ButtonExecutor.Arg.ofTriggerFunction(
                testObjectData, testArgs, testDetails, actionParams);

        // Then
        assertNotNull(arg, "Arg对象不应为null");
        assertEquals(testObjectData, arg.getObjectData(), "对象数据应正确");
        assertEquals(testArgs, arg.getArgs(), "参数应正确");
        assertEquals(testDetails, arg.getDetails(), "详情数据应正确");
        assertEquals(actionParams, arg.getActionParams(), "动作参数应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Arg类的数据ID列表构造方法
     */
    @Test
    @DisplayName("Arg - 数据ID列表构造方法测试")
    void testArgOfWithDataIds() {
        // Given
        List<String> dataIds = Arrays.asList("id1", "id2", "id3");

        // When
        ButtonExecutor.Arg arg = ButtonExecutor.Arg.of(dataIds, testArgs);

        // Then
        assertNotNull(arg, "Arg对象不应为null");
        assertEquals(dataIds, arg.getDataIds(), "数据ID列表应正确");
        assertEquals(testArgs, arg.getArgs(), "参数应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Arg类的toDetails方法
     */
    @Test
    @DisplayName("Arg - toDetails方法测试")
    void testArgToDetails() {
        // Given
        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder()
                .details(testDetails)
                .build();

        // When
        Map<String, List<IObjectData>> result = arg.toDetails();

        // Then
        assertNotNull(result, "结果不应为null");
        assertEquals(testDetails, result, "应返回设置的详情数据");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Arg类的toDetails方法使用Supplier
     */
    @Test
    @DisplayName("Arg - toDetails方法使用Supplier测试")
    void testArgToDetailsWithSupplier() {
        // Given
        Supplier<Map<String, List<IObjectData>>> supplier = () -> testDetails;
        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder()
                .detailDataSupplier(supplier)
                .build();

        // When
        Map<String, List<IObjectData>> result = arg.toDetails();

        // Then
        assertNotNull(result, "结果不应为null");
        assertEquals(testDetails, result, "应返回Supplier提供的详情数据");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Arg类的toDetails方法返回空Map
     */
    @Test
    @DisplayName("Arg - toDetails方法返回空Map测试")
    void testArgToDetailsEmpty() {
        // Given
        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder().build();

        // When
        Map<String, List<IObjectData>> result = arg.toDetails();

        // Then
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isEmpty(), "应返回空Map");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Result类的Builder模式构造
     */
    @Test
    @DisplayName("Result - Builder模式构造测试")
    void testResultBuilder() {
        // Given
        RuleResult ruleResult = new RuleResult();
        ButtonExecutor.DetailDataMergeResult mergeResult = 
                ButtonExecutor.DetailDataMergeResult.of(Arrays.asList(testObjectData), 
                        Arrays.asList(), Arrays.asList());

        // When
        ButtonExecutor.Result result = ButtonExecutor.Result.builder()
                .objectData(testObjectData)
                .details(testDetails)
                .targetDescribeApiName("TargetObject")
                .hasReturnValue(true)
                .returnValue("test result")
                .returnType("String")
                .block(false)
                .ignoreSendingRemind(true)
                .detailDataMergeResult(mergeResult)
                .functionResult("function executed")
                .validateRuleResult(ruleResult)
                .build();

        // Then
        assertNotNull(result, "Result对象不应为null");
        assertEquals(testObjectData, result.getObjectData(), "对象数据应正确");
        assertEquals(testDetails, result.getDetails(), "详情数据应正确");
        assertEquals("TargetObject", result.getTargetDescribeApiName(), "目标描述API名称应正确");
        assertTrue(result.isHasReturnValue(), "有返回值标志应为true");
        assertEquals("test result", result.getReturnValue(), "返回值应正确");
        assertEquals("String", result.getReturnType(), "返回类型应正确");
        assertFalse(result.isBlock(), "阻断标志应为false");
        assertTrue(result.isIgnoreSendingRemind(), "忽略发送提醒标志应为true");
        assertEquals(mergeResult, result.getDetailDataMergeResult(), "详情数据合并结果应正确");
        assertEquals("function executed", result.getFunctionResult(), "函数结果应正确");
        assertEquals(ruleResult, result.getValidateRuleResult(), "验证规则结果应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Result类的默认值
     */
    @Test
    @DisplayName("Result - 默认值测试")
    void testResultDefaults() {
        // When
        ButtonExecutor.Result result = ButtonExecutor.Result.builder().build();

        // Then
        assertNotNull(result, "Result对象不应为null");
        assertTrue(result.isBlock(), "默认应为阻断");
        assertFalse(result.isHasReturnValue(), "默认无返回值");
        assertFalse(result.isIgnoreSendingRemind(), "默认不忽略发送提醒");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Result类的empty静态方法
     */
    @Test
    @DisplayName("Result - empty静态方法测试")
    void testResultEmpty() {
        // When
        ButtonExecutor.Result result = ButtonExecutor.Result.empty();

        // Then
        assertNotNull(result, "Result对象不应为null");
        assertTrue(result.isBlock(), "默认应为阻断");
        assertNull(result.getObjectData(), "对象数据应为null");
        assertNull(result.getDetails(), "详情数据应为null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ExtendInfo类的构造和字段设置
     */
    @Test
    @DisplayName("ExtendInfo - 构造和字段设置测试")
    void testExtendInfo() {
        // When
        ButtonExecutor.ExtendInfo extendInfo = new ButtonExecutor.ExtendInfo(true);

        // Then
        assertNotNull(extendInfo, "ExtendInfo对象不应为null");
        assertTrue(extendInfo.isEnableValidateRule(), "启用验证规则标志应为true");

        // 测试setter
        extendInfo.setEnableValidateRule(false);
        assertFalse(extendInfo.isEnableValidateRule(), "启用验证规则标志应为false");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试DetailDataMergeResult类的构造和字段设置
     */
    @Test
    @DisplayName("DetailDataMergeResult - 构造和字段设置测试")
    void testDetailDataMergeResult() {
        // Given
        List<IObjectData> toAdd = Arrays.asList(testObjectData);
        List<IObjectData> toUpdate = Arrays.asList();
        List<IObjectData> toDelete = Arrays.asList();

        // When
        ButtonExecutor.DetailDataMergeResult mergeResult = 
                ButtonExecutor.DetailDataMergeResult.of(toAdd, toUpdate, toDelete);

        // Then
        assertNotNull(mergeResult, "DetailDataMergeResult对象不应为null");
        assertEquals(toAdd, mergeResult.getDetailsToAdd(), "待添加详情应正确");
        assertEquals(toUpdate, mergeResult.getDetailsToUpdate(), "待更新详情应正确");
        assertEquals(toDelete, mergeResult.getDetailsToDelete(), "待删除详情应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试DetailDataMergeResult类的默认构造
     */
    @Test
    @DisplayName("DetailDataMergeResult - 默认构造测试")
    void testDetailDataMergeResultDefault() {
        // When
        ButtonExecutor.DetailDataMergeResult mergeResult = new ButtonExecutor.DetailDataMergeResult();

        // Then
        assertNotNull(mergeResult, "DetailDataMergeResult对象不应为null");
        assertNotNull(mergeResult.getDetailsToAdd(), "待添加详情列表不应为null");
        assertNotNull(mergeResult.getDetailsToUpdate(), "待更新详情列表不应为null");
        assertNotNull(mergeResult.getDetailsToDelete(), "待删除详情列表不应为null");
        assertTrue(mergeResult.getDetailsToAdd().isEmpty(), "待添加详情列表应为空");
        assertTrue(mergeResult.getDetailsToUpdate().isEmpty(), "待更新详情列表应为空");
        assertTrue(mergeResult.getDetailsToDelete().isEmpty(), "待删除详情列表应为空");
    }

    /**
     * GenerateByAI
     * 测试内容描述：参数化测试不同的返回类型
     */
    @ParameterizedTest
    @ValueSource(strings = {"String", "Integer", "Boolean", "Object", "UIAction", "UIEvent"})
    @DisplayName("Result - 不同返回类型测试")
    void testResultDifferentReturnTypes(String returnType) {
        // When
        ButtonExecutor.Result result = ButtonExecutor.Result.builder()
                .hasReturnValue(true)
                .returnType(returnType)
                .returnValue("test value")
                .build();

        // Then
        assertNotNull(result, "Result对象不应为null");
        assertTrue(result.isHasReturnValue(), "应有返回值");
        assertEquals(returnType, result.getReturnType(), "返回类型应正确");
        assertEquals("test value", result.getReturnValue(), "返回值应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试JSON序列化和反序列化
     */
    @Test
    @DisplayName("JSON序列化反序列化测试")
    void testJsonSerialization() {
        // Given
        ButtonExecutor.Arg originalArg = ButtonExecutor.Arg.builder()
                .describeApiName("TestObject")
                .objectDataId("123")
                .buttonApiName("testButton")
                .bizKey("testBizKey")
                .build();

        // When - 序列化
        String json = gson.toJson(originalArg);
        assertNotNull(json, "JSON字符串不应为null");
        assertTrue(json.contains("TestObject"), "JSON应包含描述API名称");
        assertTrue(json.contains("testButton"), "JSON应包含按钮API名称");

        // Then - 反序列化
        ButtonExecutor.Arg deserializedArg = gson.fromJson(json, ButtonExecutor.Arg.class);
        assertNotNull(deserializedArg, "反序列化对象不应为null");
        assertEquals(originalArg.getDescribeApiName(), deserializedArg.getDescribeApiName(), 
                "描述API名称应一致");
        assertEquals(originalArg.getButtonApiName(), deserializedArg.getButtonApiName(), 
                "按钮API名称应一致");
        assertEquals(originalArg.getBizKey(), deserializedArg.getBizKey(), "业务键应一致");
    }
}
