package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * ActionExecutorManager单元测试 - JUnit5版本
 * 
 * GenerateByAI
 * 
 * 测试ActionExecutorManager类的核心功能：
 * - getActionExecutor: 根据类型获取执行器
 * - setApplicationContext: Spring上下文初始化
 * - 执行器注册和管理逻辑
 * - 默认执行器回退机制
 * 
 * 覆盖场景：
 * - 正常执行器获取
 * - 执行器不存在时的默认回退
 * - 所有ActionExecutorType枚举值测试
 * - Spring上下文初始化异常处理
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("ActionExecutorManager - 动作执行器管理器测试")
class ActionExecutorManagerTest {

    @Mock
    private ApplicationContext applicationContext;
    
    @Mock
    private UpdateFieldAction updateFieldAction;
    
    @Mock
    private ActionExecutor mockActionExecutor1;
    
    @Mock
    private ActionExecutor mockActionExecutor2;
    
    @Mock
    private ActionExecutor mockActionExecutor3;

    @InjectMocks
    private ActionExecutorManager actionExecutorManager;

    // 测试常量
    private static final String EXECUTOR_BEAN_NAME_1 = "convertAction";
    private static final String EXECUTOR_BEAN_NAME_2 = "sendEmailAction";
    private static final String EXECUTOR_BEAN_NAME_3 = "aiAgentAction";

    @BeforeEach
    void setUp() {
        // 配置Mock执行器的类型
        when(mockActionExecutor1.getType()).thenReturn(ActionExecutorType.CONVERT);
        when(mockActionExecutor2.getType()).thenReturn(ActionExecutorType.SEND_MAIL);
        when(mockActionExecutor3.getType()).thenReturn(ActionExecutorType.AI_AGENT);
        
        // 配置UpdateFieldAction的类型
        when(updateFieldAction.getType()).thenReturn(ActionExecutorType.UPDATES);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setApplicationContext方法正常初始化执行器
     */
    @Test
    @DisplayName("setApplicationContext - 正常初始化执行器")
    void testSetApplicationContext_NormalInitialization() {
        // 准备测试数据
        Map<String, ActionExecutor> springExecutorMap = new HashMap<>();
        springExecutorMap.put(EXECUTOR_BEAN_NAME_1, mockActionExecutor1);
        springExecutorMap.put(EXECUTOR_BEAN_NAME_2, mockActionExecutor2);
        springExecutorMap.put(EXECUTOR_BEAN_NAME_3, mockActionExecutor3);
        
        when(applicationContext.getBeansOfType(ActionExecutor.class))
                .thenReturn(springExecutorMap);
        
        // 执行测试
        actionExecutorManager.setApplicationContext(applicationContext);
        
        // 验证结果 - 检查执行器是否正确注册
        ActionExecutor convertExecutor = actionExecutorManager.getActionExecutor(ActionExecutorType.CONVERT);
        ActionExecutor sendMailExecutor = actionExecutorManager.getActionExecutor(ActionExecutorType.SEND_MAIL);
        ActionExecutor aiAgentExecutor = actionExecutorManager.getActionExecutor(ActionExecutorType.AI_AGENT);
        
        assertEquals(mockActionExecutor1, convertExecutor, "CONVERT类型应返回mockActionExecutor1");
        assertEquals(mockActionExecutor2, sendMailExecutor, "SEND_MAIL类型应返回mockActionExecutor2");
        assertEquals(mockActionExecutor3, aiAgentExecutor, "AI_AGENT类型应返回mockActionExecutor3");
        
        // 验证Mock调用
        verify(applicationContext).getBeansOfType(ActionExecutor.class);
        verify(mockActionExecutor1).getType();
        verify(mockActionExecutor2).getType();
        verify(mockActionExecutor3).getType();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setApplicationContext方法处理空的执行器Map
     */
    @Test
    @DisplayName("setApplicationContext - 处理空的执行器Map")
    void testSetApplicationContext_EmptyExecutorMap() {
        // 准备测试数据 - 空的执行器Map
        Map<String, ActionExecutor> emptyExecutorMap = new HashMap<>();
        when(applicationContext.getBeansOfType(ActionExecutor.class))
                .thenReturn(emptyExecutorMap);
        
        // 执行测试
        actionExecutorManager.setApplicationContext(applicationContext);
        
        // 验证结果 - 所有类型都应返回默认的updateFieldAction
        ActionExecutor convertExecutor = actionExecutorManager.getActionExecutor(ActionExecutorType.CONVERT);
        ActionExecutor sendMailExecutor = actionExecutorManager.getActionExecutor(ActionExecutorType.SEND_MAIL);
        
        assertEquals(updateFieldAction, convertExecutor, "未注册的类型应返回默认的updateFieldAction");
        assertEquals(updateFieldAction, sendMailExecutor, "未注册的类型应返回默认的updateFieldAction");
        
        // 验证Mock调用
        verify(applicationContext).getBeansOfType(ActionExecutor.class);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试setApplicationContext方法处理BeansException异常
     */
    @Test
    @DisplayName("setApplicationContext - 处理BeansException异常")
    void testSetApplicationContext_BeansException() {
        // 准备测试数据 - 模拟BeansException（需要是BeansException类型才会被捕获）
        when(applicationContext.getBeansOfType(ActionExecutor.class))
                .thenThrow(new BeansException("Spring context error") {});

        // 执行测试 - 应该不抛出异常
        assertDoesNotThrow(() -> {
            actionExecutorManager.setApplicationContext(applicationContext);
        });

        // 验证结果 - 异常情况下应返回默认执行器
        ActionExecutor executor = actionExecutorManager.getActionExecutor(ActionExecutorType.CONVERT);
        assertEquals(updateFieldAction, executor, "异常情况下应返回默认的updateFieldAction");

        // 验证Mock调用
        verify(applicationContext).getBeansOfType(ActionExecutor.class);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getActionExecutor方法获取已注册的执行器
     */
    @Test
    @DisplayName("getActionExecutor - 获取已注册的执行器")
    void testGetActionExecutor_RegisteredExecutor() {
        // 准备测试数据 - 先初始化执行器
        Map<String, ActionExecutor> springExecutorMap = new HashMap<>();
        springExecutorMap.put(EXECUTOR_BEAN_NAME_1, mockActionExecutor1);
        when(applicationContext.getBeansOfType(ActionExecutor.class))
                .thenReturn(springExecutorMap);
        
        actionExecutorManager.setApplicationContext(applicationContext);
        
        // 执行测试
        ActionExecutor result = actionExecutorManager.getActionExecutor(ActionExecutorType.CONVERT);
        
        // 验证结果
        assertNotNull(result, "结果不应为null");
        assertEquals(mockActionExecutor1, result, "应返回正确注册的执行器");
        assertNotEquals(updateFieldAction, result, "不应返回默认执行器");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getActionExecutor方法获取未注册的执行器时返回默认执行器
     */
    @Test
    @DisplayName("getActionExecutor - 未注册执行器返回默认执行器")
    void testGetActionExecutor_UnregisteredExecutorReturnsDefault() {
        // 准备测试数据 - 不初始化任何执行器
        Map<String, ActionExecutor> emptyExecutorMap = new HashMap<>();
        when(applicationContext.getBeansOfType(ActionExecutor.class))
                .thenReturn(emptyExecutorMap);
        
        actionExecutorManager.setApplicationContext(applicationContext);
        
        // 执行测试
        ActionExecutor result = actionExecutorManager.getActionExecutor(ActionExecutorType.CONVERT);
        
        // 验证结果
        assertNotNull(result, "结果不应为null");
        assertEquals(updateFieldAction, result, "未注册的执行器应返回默认的updateFieldAction");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getActionExecutor方法处理null类型参数
     */
    @Test
    @DisplayName("getActionExecutor - 处理null类型参数")
    void testGetActionExecutor_NullType() {
        // 准备测试数据
        Map<String, ActionExecutor> emptyExecutorMap = new HashMap<>();
        when(applicationContext.getBeansOfType(ActionExecutor.class))
                .thenReturn(emptyExecutorMap);
        
        actionExecutorManager.setApplicationContext(applicationContext);
        
        // 执行测试
        ActionExecutor result = actionExecutorManager.getActionExecutor(null);
        
        // 验证结果
        assertNotNull(result, "结果不应为null");
        assertEquals(updateFieldAction, result, "null类型应返回默认的updateFieldAction");
    }

    /**
     * GenerateByAI
     * 测试内容描述：参数化测试所有ActionExecutorType枚举值
     */
    @ParameterizedTest
    @EnumSource(ActionExecutorType.class)
    @DisplayName("getActionExecutor - 参数化测试所有ActionExecutorType")
    void testGetActionExecutor_AllActionExecutorTypes(ActionExecutorType executorType) {
        // 准备测试数据 - 不注册任何执行器，测试默认回退
        Map<String, ActionExecutor> emptyExecutorMap = new HashMap<>();
        when(applicationContext.getBeansOfType(ActionExecutor.class))
                .thenReturn(emptyExecutorMap);
        
        actionExecutorManager.setApplicationContext(applicationContext);
        
        // 执行测试
        ActionExecutor result = actionExecutorManager.getActionExecutor(executorType);
        
        // 验证结果
        assertNotNull(result, "所有类型都应返回非null的执行器");
        assertEquals(updateFieldAction, result, 
                "未注册的执行器类型 " + executorType + " 应返回默认的updateFieldAction");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试执行器覆盖场景（同类型多个执行器）
     */
    @Test
    @DisplayName("getActionExecutor - 执行器覆盖场景")
    void testGetActionExecutor_ExecutorOverride() {
        // 准备测试数据 - 创建两个相同类型的执行器
        ActionExecutor anotherConvertExecutor = mock(ActionExecutor.class);
        when(anotherConvertExecutor.getType()).thenReturn(ActionExecutorType.CONVERT);
        
        Map<String, ActionExecutor> springExecutorMap = new HashMap<>();
        springExecutorMap.put("convertAction1", mockActionExecutor1);
        springExecutorMap.put("convertAction2", anotherConvertExecutor);
        
        when(applicationContext.getBeansOfType(ActionExecutor.class))
                .thenReturn(springExecutorMap);
        
        // 执行测试
        actionExecutorManager.setApplicationContext(applicationContext);
        ActionExecutor result = actionExecutorManager.getActionExecutor(ActionExecutorType.CONVERT);
        
        // 验证结果 - 应该返回其中一个执行器（Map的遍历顺序可能不确定）
        assertNotNull(result, "结果不应为null");
        assertTrue(result == mockActionExecutor1 || result == anotherConvertExecutor,
                "应返回注册的执行器之一");
        assertNotEquals(updateFieldAction, result, "不应返回默认执行器");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试执行器管理器的线程安全性（基础验证）
     */
    @Test
    @DisplayName("getActionExecutor - 基础线程安全性验证")
    void testGetActionExecutor_ThreadSafety() {
        // 准备测试数据
        Map<String, ActionExecutor> springExecutorMap = new HashMap<>();
        springExecutorMap.put(EXECUTOR_BEAN_NAME_1, mockActionExecutor1);
        when(applicationContext.getBeansOfType(ActionExecutor.class))
                .thenReturn(springExecutorMap);
        
        actionExecutorManager.setApplicationContext(applicationContext);
        
        // 执行测试 - 多次调用应返回相同结果
        ActionExecutor result1 = actionExecutorManager.getActionExecutor(ActionExecutorType.CONVERT);
        ActionExecutor result2 = actionExecutorManager.getActionExecutor(ActionExecutorType.CONVERT);
        ActionExecutor result3 = actionExecutorManager.getActionExecutor(ActionExecutorType.CONVERT);
        
        // 验证结果
        assertEquals(result1, result2, "多次调用应返回相同的执行器实例");
        assertEquals(result2, result3, "多次调用应返回相同的执行器实例");
        assertEquals(mockActionExecutor1, result1, "应返回正确的执行器");
    }
}
